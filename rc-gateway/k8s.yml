---
apiVersion: apps/v1
kind: Deployment
metadata:
  annotations: {}
  labels:
    app: gateway
  name: gateway
  namespace: local
  resourceVersion: '83780067'
spec:
  progressDeadlineSeconds: 600
  replicas: 1
  revisionHistoryLimit: 2
  selector:
    matchLabels:
      app: gateway
  strategy:
    rollingUpdate:
      maxSurge: 25%
      maxUnavailable: 25%
    type: RollingUpdate
  template:
    metadata:
      annotations:
        kubectl.kubernetes.io/restartedAt: '2025-05-22T10:43:46+08:00'
      creationTimestamp: null
      labels:
        app: gateway
    spec:
      containers:
        - env:
            - name: JVM_XMS
              value: 520m
            - name: JVM_XMX
              value: 520m
            - name: JVM_XMN
              value: 384m
            - name: SERVER_PORT
              value: '1100'
            - name: SERVER_PROFILE_ACTIVE
              value: dev
            - name: TRACE_PROTOCOL
              value: grpc
            - name: TRACE_ENDPOINT
              value: 'https://axxxx-dev.ap-southeast-1.log.aliyuncs.com:10010'
            - name: TRACE_COMPRESSION
              value: gzip
            - name: TRACE_HEADERS
              value: >-
                x-sls-otel-project=aswat-app-trace-dev,x-sls-otel-instance-id=aswat-develop,x-sls-otel-ak-id=LTAI5xxxxfeKrE5x,x-sls-otel-ak-secret=3kKQWtxxxxm6SzmHz
            - name: TRACE_HOST_NAME
              valueFrom:
                fieldRef:
                  apiVersion: v1
                  fieldPath: status.podIP
            - name: NACOS_HOST
              valueFrom:
                configMapKeyRef:
                  key: nacos.host
                  name: nacos
            - name: NACOS_PORT
              valueFrom:
                configMapKeyRef:
                  key: nacos.port
                  name: nacos
            - name: NACOS_USERNAME
              valueFrom:
                configMapKeyRef:
                  key: nacos.username
                  name: nacos
            - name: NACOS_PASSWORD
              valueFrom:
                configMapKeyRef:
                  key: nacos.password
                  name: nacos
          image: >-
            794038239327.dkr.ecr.ap-southeast-1.amazonaws.com/halar-dev:gateway-20250210v151404
          imagePullPolicy: IfNotPresent
          lifecycle:
            preStop:
              exec:
                command:
                  - /bin/bash
                  - '-c'
                  - >-
                    curl -X POST
                    http://localhost:1100/actuator/nacosservicederegister?key=CxILm9hA1b9hF3Hl
                    --header 'Content-Type:
                    application/vnd.spring-boot.actuator.v2+json;charset=UTF-8'
                  - sleep 30
          livenessProbe:
            failureThreshold: 3
            httpGet:
              path: /actuator/health
              port: 1100
              scheme: HTTP
            periodSeconds: 30
            successThreshold: 1
            timeoutSeconds: 20
          name: gateway
          ports:
            - containerPort: 1100
              protocol: TCP
          readinessProbe:
            failureThreshold: 2
            httpGet:
              path: /actuator/health
              port: 1100
              scheme: HTTP
            periodSeconds: 5
            successThreshold: 1
            timeoutSeconds: 2
          resources:
            limits:
              memory: 1Gi
            requests:
              memory: 500Mi
          startupProbe:
            failureThreshold: 60
            httpGet:
              path: /actuator/health
              port: 1100
              scheme: HTTP
            periodSeconds: 5
            successThreshold: 1
            timeoutSeconds: 2
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
      dnsPolicy: ClusterFirst
      imagePullSecrets:
        - name: aliyun-secret
      restartPolicy: Always
      schedulerName: default-scheduler
      securityContext: {}
      terminationGracePeriodSeconds: 120
status:
  availableReplicas: 1
  conditions:
    - lastTransitionTime: '2025-05-10T07:37:29Z'
      lastUpdateTime: '2025-05-10T07:37:29Z'
      message: Deployment has minimum availability.
      reason: MinimumReplicasAvailable
      status: 'True'
      type: Available
    - lastTransitionTime: '2025-02-10T15:14:45Z'
      lastUpdateTime: '2025-05-22T02:45:14Z'
      message: ReplicaSet "gateway-5bc64bc6c6" has successfully progressed.
      reason: NewReplicaSetAvailable
      status: 'True'
      type: Progressing
  observedGeneration: 40
  readyReplicas: 1
  replicas: 1
  updatedReplicas: 1

---
apiVersion: v1
kind: Service
metadata:
  annotations: {}
  name: gateway
  namespace: local
  resourceVersion: '22869117'
spec:
  clusterIP: **************
  clusterIPs:
    - **************
  internalTrafficPolicy: Cluster
  ipFamilies:
    - IPv4
  ipFamilyPolicy: SingleStack
  ports:
    - port: 80
      protocol: TCP
      targetPort: 1100
  selector:
    app: gateway
  sessionAffinity: None
  type: ClusterIP
status:
  loadBalancer: {}

---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  annotations:
    alb.ingress.kubernetes.io/healthcheck-interval-seconds: '5'
    alb.ingress.kubernetes.io/healthcheck-path: /
    alb.ingress.kubernetes.io/healthcheck-timeout-seconds: '2'
    alb.ingress.kubernetes.io/healthy-threshold-count: '2'
    alb.ingress.kubernetes.io/scheme: internet-facing
    alb.ingress.kubernetes.io/target-group-attributes: deregistration_delay.timeout_seconds=0
    alb.ingress.kubernetes.io/target-type: ip
    alb.ingress.kubernetes.io/unhealthy-threshold-count: '2'
  finalizers:
    - ingress.k8s.aws/resources
  name: gateway
  namespace: local
  resourceVersion: '22869120'
spec:
  ingressClassName: alb
  rules:
    - http:
        paths:
          - backend:
              service:
                name: gateway
                port:
                  number: 80
            path: /
            pathType: Prefix
status:
  loadBalancer:
    ingress:
      - hostname: >-
          k8s-local-gateway-3deb78194e-**********.ap-southeast-1.elb.amazonaws.com

