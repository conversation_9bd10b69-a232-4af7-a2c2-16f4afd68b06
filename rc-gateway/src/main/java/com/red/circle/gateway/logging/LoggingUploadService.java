package com.red.circle.gateway.logging;

import com.google.common.base.Throwables;
import com.red.circle.component.tencent.cls.service.ClsService;
import com.red.circle.tool.core.text.StringUtils;
import java.util.Objects;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR> on 2024/1/12
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class LoggingUploadService {

  @Value("${spring.profiles.active:unknown}")
  private String profileActive;
  private final ClsService clsService;

  private String getTopic() {
    log.info("profileActive: {}", profileActive);
    if (Objects.equals(profileActive, "prod")) {
      return "30e80b29-9fbd-4630-a45d-398146b14ad5";
    }

//    if (Objects.equals(profileActive, "dev")) {
//      return "180afe72-b0fe-4502-bbc2-55a8a5193e78";
//    }

    return "30e80b29-9fbd-4630-a45d-398146b14ad5";
  }

  public void upload(GatewayLogging logging) {
    String topic = getTopic();
    if (Objects.isNull(logging)) {
      return;
    }
    if (StringUtils.isNotBlank(topic)) {
      try {
        clsService.upload(topic, logging);
      } catch (Exception ex) {
        log.error("Upload log error: {}", Throwables.getStackTraceAsString(ex));
      }
    }
  }

}
