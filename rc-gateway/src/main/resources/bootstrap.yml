spring-profile.active: @profiles.active@

spring:
  application:
    name: @artifactId@
  cloud:
    nacos:
      username: ${NACOS_USERNAME:nacos}
      password: ${NACOS_PASSWORD:nacos}
      discovery:
        server-addr: ${NACOS_HOST:127.0.0.1}:${NACOS_PORT:8848}
        namespace: 69450bca-ce4a-4e67-8bb8-b21bcf55d0fd
        group: ${spring-profile.active}
        register-enabled: true  # 启用服务注册
        service: ${spring.application.name}  # 显式指定注册的服务名（可选）
      config:
        server-addr: ${NACOS_HOST:127.0.0.1}:${NACOS_PORT:8848}
        file-extension: @register.file-extension@
        namespace: 69450bca-ce4a-4e67-8bb8-b21bcf55d0fd
        group: ${spring.application.name}
        shared-configs:
          - data-id: <EMAIL>-extension@
            group: ${spring.application.name}
            refresh: true
    loadbalancer:
      cache:
        ttl: 5s

  config:
    import:
      - optional:nacos:<EMAIL>-extension@
      - optional:nacos:application-${spring-profile.active}.@register.file-extension@
      - optional:nacos:${spring.application.name}-${spring-profile.active}.@register.file-extension@
      - optional:nacos:<EMAIL>-extension@?refresh=true&group=@config.group.common@
      - optional:nacos:<EMAIL>-extension@?refresh=true&group=@config.group.common@
logging:
  level:
    com:
      alibaba:
        cloud:
          nacos:
            configdata:
              NacosConfigDataLoader: error
            client:
              NacosPropertySourceBuilder: error
---
spring:
  cloud:
    gateway:
      httpclient:
        response-timeout: PT30S
        pool:
          max-idle-time: 5000
