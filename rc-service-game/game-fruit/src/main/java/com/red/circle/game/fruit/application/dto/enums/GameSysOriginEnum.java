package com.red.circle.game.fruit.application.dto.enums;

import com.red.circle.component.core.enums.ISysOriginPlatform;

/**
 * 系统来源.
 *
 * <AUTHOR> on 2020/11/25
 */
public enum GameSysOriginEnum implements ISysOriginPlatform {

  ASWAT("Aswat"),
  TWO_FUN("2Fun"),
  HALAR("Halar"),
  LOTFUN("Lotfun"),
  HOOKA("Hooka"),
  MARCIE("MARCIE"),
  ;

  private final String originName;

  GameSysOriginEnum(String originName) {
    this.originName = originName;
  }

  public static GameSysOriginEnum toEnum(String key) {
    try {
      return GameSysOriginEnum.valueOf(key);
    } catch (Exception ex) {
      // ignore
    }
    return null;
  }

  public String concatUnderscoreJoin(String val) {
    return this.name().concat("_").concat(val);
  }

  @Override
  public String getSysOrigin() {
    return this.name();
  }

  @Override
  public String getSysOriginName() {
    return this.originName;
  }

}
