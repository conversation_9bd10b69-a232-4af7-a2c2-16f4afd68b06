package com.red.circle.scheduling.job.family;

import com.red.circle.other.inner.endpoint.family.FamilyDailyTaskTriggerRecordClient;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.shardingsphere.elasticjob.api.ShardingContext;
import org.apache.shardingsphere.elasticjob.simple.job.SimpleJob;
import org.springframework.stereotype.Component;

/**
 * 工会每日任务处理.
 * <p>
 * com.sugartime.app.server.scheduler.DailyTask.dailyTask()
 *
 * <AUTHOR> on 2023/10/22
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class FamilyDailyTask implements SimpleJob {

  private final FamilyDailyTaskTriggerRecordClient familyDailyTaskTriggerRecordClient;

  @Override
  public void execute(ShardingContext shardingContext) {
    familyDailyTaskTriggerRecordClient.deleteAll();
  }

}
