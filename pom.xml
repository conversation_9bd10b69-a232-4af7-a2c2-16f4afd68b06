<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xmlns="http://maven.apache.org/POM/4.0.0"
  xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>
  <artifactId>red-circle-service</artifactId>
  <groupId>com.red.circle</groupId>
  <packaging>pom</packaging>
  <version>1.0.0-RELEASES</version>

  <modules>
    <module>rc-dependencies</module>
    <module>rc-common-business</module>
    <module>rc-gateway</module>
<!--    <module>rc-register</module>-->
    <module>rc-service</module>
    <module>rc-visual</module>
    <module>rc-auth</module>
    <module>rc-examples</module>
    <module>rc-scheduling</module>
    <module>rc-service-game</module>
  </modules>

  <properties>
    <maven.deploy.skip>true</maven.deploy.skip>
  </properties>

</project>
