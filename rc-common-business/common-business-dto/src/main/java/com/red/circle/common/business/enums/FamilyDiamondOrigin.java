package com.red.circle.common.business.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 工会钻石来源枚举.
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum FamilyDiamondOrigin {

  /**
   * 成员收礼提成.
   */
  MEMBER_GIFT_COMMISSION("Member gift commission"),

  /**
   * 兑换金币.
   */
  EXCHANGE_TO_GOLD("Exchange to gold"),

  /**
   * 钻石提现.
   */
  WITHDRAW_DIAMOND("Withdraw diamond"),

  /**
   * 管理员调整.
   */
  ADMIN_ADJUST("Admin adjust");

  private final String desc;

}
