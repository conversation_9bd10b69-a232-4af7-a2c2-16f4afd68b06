package com.red.circle.common.business.enums;

/**
 * 发送道具来源.
 *
 * <AUTHOR> on 2021/6/24
 */
public enum SendPropsOrigin {

  /**
   * cp奖励.
   */
  CP_REWARD("Cp Reward"),

  /**
   * 累计充值奖励.
   */
  CUMULATIVE_RECHARGE_REWARDS("Accumulated recharge"),

  /**
   * 周星.
   */
  WEEK_STAR("Week star"),

  /**
   * 房间奖励.
   */
  ROOM_REWARD("Room Reward"),

  /**
   * 爆水晶游戏.
   */
  GAME_BURST_CRYSTAL("Game Explosion Crystal"),

  /**
   * 爆水晶游戏宝箱奖励.
   */
  GAME_BURST_CRYSTAL_LUCKY_BOX("Game burst crystal box"),

  /**
   * 爆水晶游戏突破记录.
   */
  GAME_BURST_CRYSTAL_SPRINT("Game burst crystal sprint"),

  /**
   * 首次充值.
   */
  FIRST_CHARGE_REWARD("First charge"),

  /**
   * 工会每周宝箱奖励领取
   */
  FAMILY_AWARD_RECEIVE("Family Weekly Treasure Chest"),

  /**
   * 每周CP相互赠送礼物榜单.
   */
  WEEK_CP_GIFT("Week cp gift"),

  /**
   * 房间PK奖励
   */
  ROOM_PK("Room PK"),

  /**
   * King国王王后.
   */
  WEEK_USER_CONSUME("King and Queen"),

  /**
   * 宠物奖励.
   */
  PET_REWARD("Pet reward"),

  /**
   * 宠物转盘抽奖.
   */
  PET_TURNTABLE_LOTTERY("Pet Turntable Lottery"),

  /**
   * 砸金蛋.
   */
  EGG("Game Egg"),

  /**
   * 扑克游戏.
   */
  POKER("Game Poker"),

  /**
   * 骰子游戏.
   */
  DICE("Game Dice"),

  /**
   * 游戏王奖励.
   */
  GAME_KING_AWARD("Game king"),

  /**
   * 每周国王.
   */
  WEEK_KING("Week king"),

  /**
   * 每周王后.
   */
  WEEK_QUEEN("Wekk Queen"),

  /**
   * 每日签到.
   */
  DAILY_REGISTER("Daily Attendance"),

  /**
   * 幸运礼物抽奖.
   */
  LUCKY_GIFT_REWARD("Luck Gift"),

  /**
   * 邀请用户奖励
   */
  INVITE_USER_REWARDS("Invite users"),

  /**
   * 每周游戏任务奖励.
   */
  WEEKLY_GAME_TASKS("Weekly game tasks"),

  /**
   * 每周最佳特殊关系奖励.
   */
  ACTIVITY_FRIENDSHIP_CARD_REWARDS("Weekly Best Special Relationship"),

  /**
   * 活动奖励.
   */
  ACTIVITY_REWARD("Activity"),

  /**
   * SVIP 奖励.
   */
  SVIP_REWARD("SVIP"),

  /**
   * 收款单据.
   */
  COLLECTION_RECEIPT("Collection documents"),

  /**
   * 12月政策奖励.
   */
  DECEMBER_POLICY_REWARDS("Policy rewards for December"),

  /**
   * 政策奖励.
   */
  POLICY_REWARDS("Policy incentives"),

  /**
   * 主播代理系统奖励.
   */
  HOST_AGENT_SYSTEM_REWARD("Host Agent System Rewards"),

  /**
   * 代理主播数量激励奖励.
   */
  ACTIVE_AGENT_ANCHOR_COUNT_REWARD("Incentive reward for the number of proxy anchors"),

  /**
   * 代理名下主播累计月目标奖励.
   */
  ACTIVE_AGENT_MONTH_TARGET_REWARD(
      "Accumulated monthly target rewards for anchors under the agent's name"),

  /**
   * 主播月目标奖励.
   */
  ACTIVE_ANCHOR_MONTH_TARGET_REWARD("Monthly Target Rewards for Broadcasters"),

  /**
   * 主播日目标奖励.
   */
  ACTIVE_ANCHOR_DAY_TARGET_REWARD("Anchor Day Goal Rewards"),

  /**
   * 代理活动奖励-周.
   */
  AGENT_ACTIVE_WEEK_REWARD("Agency Activity Rewards - Week"),

  /**
   * LUCKY BOX 赏金任务领取.
   */
  LUCKY_BOX_RECEIVE("Lucky Box Receive "),

  /**
   * luckyBox赏金任务领取.
   */
  LUCKY_BOX_REWARD("[LuckyBox] Give Props Reward"),

  /**
   * 代理活动奖励-月.
   */
  AGENT_ACTIVE_MONTH_REWARD("Agency Activity Rewards - Month"),

  /**
   * 消耗活动奖励.
   */
  CONSUMPTION_ACTIVITY("Consumption Activity"),

  /**
   * 官方-赠送.
   */
  OFFICIAL_GIFT("Official gift"),

  /**
   * 活动奖励.
   */
  ACTIVITY_AWARD("Activity Award"),

  /**
   * 自己购买或朋友赠送.
   */
  BUY_OR_GIVE("Buy Or Friend Gift"),
  /**
   * admin  free.
   */
  ADMIN_FREE("admin free"),

  /**
   * 摩天轮宝箱奖励.
   */
  GAME_FRUIT_OPEN_BOX_REWARD("Game Fruit Open Box Rewards"),

  /**
   * 摩天轮任务奖励.
   */
  GAME_FRUIT_TASK_REWARD("Game Fruit Receive Task Rewards"),
  /**
   * KTV游戏每周榜单奖励.
   */
  GAME_KTV_WEEK_RANK_REWARD("Game Ktv rank reward"),
  /**
   * 房间粉丝人气票活动.
   */
  ROOM_FAN_VOTES_ACTIVITY("Room fan votes activity"),

  /**
   * 日常任务简历.
   */
  DAILY_TASK_REWARD("Daily Task Reward"),
  /**
   * 裂变任务奖励.
   */
  MEMBER_ACTIVE_TASK_REWARD("member activity Reward"),

  ;

  private final String desc;

  SendPropsOrigin(String desc) {
    this.desc = desc;
  }

  public String getDesc() {
    return desc;
  }

}
