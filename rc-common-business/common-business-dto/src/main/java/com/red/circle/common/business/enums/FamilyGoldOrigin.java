package com.red.circle.common.business.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 工会金币来源枚举.
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum FamilyGoldOrigin {

  /**
   * 钻石兑换.
   */
  DIAMOND_EXCHANGE("Diamond exchange"),

  /**
   * 分发给成员.
   */
  DISTRIBUTE_TO_MEMBER("Distribute to member"),

  /**
   * 成员提现钻石转入.
   */
  MEMBER_WITHDRAW_DIAMOND("Member withdraw diamond"),

  /**
   * 管理员调整.
   */
  ADMIN_ADJUST("Admin adjust");

  private final String desc;

}
