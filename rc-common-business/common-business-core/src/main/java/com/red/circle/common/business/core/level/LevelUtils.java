package com.red.circle.common.business.core.level;

import com.red.circle.common.business.core.enums.SysOriginPlatformEnum;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import lombok.extern.slf4j.Slf4j;

/**
 * 等级工具.
 *
 * <AUTHOR> on 2021/1/16
 */
@Slf4j
public class LevelUtils {

  private static final String ERROR_USER_LEVEL_REQUIRED = "User level required [sysOrigin].";

  private static final Map<LevelType, LevelCalculation> DEFAULT_LEVEL_MAP = Map.of(
      LevelType.USER, new LevelCalculation(1000),
      LevelType.ROOM, new LevelCalculation(10000),
      LevelType.CARD, new LevelCalculation(1000)
  );

  private static final Map<SysOriginPlatformEnum, Map<LevelType, LevelCalculation>> SYS_ORIGIN_CONFIG_MAP = Map.of(
          SysOriginPlatformEnum.TWO_FUN, Map.of(
                  LevelType.USER, new LevelCalculation(1000L * 5L),
                  LevelType.ROOM, new LevelCalculation(10000L * 5L),
                  LevelType.CARD, new LevelCalculation(1000L * 5L)
          )
//          SysOriginPlatformEnum.TARAB, Map.of(
//                  LevelType.USER, new LevelCalculation(1000L * 5L),
//                  LevelType.ROOM, new LevelCalculation(10000L * 5L),
//                  LevelType.CARD, new LevelCalculation(1000L * 5L)
//          )
  );

  /**
   * 用户等级.
   */
  public static UserLevelExperience getUserLevel(SysOriginPlatformEnum sysOrigin, Long experience) {

    if (Objects.isNull(sysOrigin)) {
      throw new IllegalArgumentException(ERROR_USER_LEVEL_REQUIRED);
    }
    return getLevelCalculation(sysOrigin).get(LevelType.USER).getLevel(experience);
  }

  /**
   * 房间等级.
   */
  public static UserLevelExperience getRoomLevel(SysOriginPlatformEnum sysOrigin, Long experience) {

    if (Objects.isNull(sysOrigin)) {
      throw new IllegalArgumentException(ERROR_USER_LEVEL_REQUIRED);
    }

    return getLevelCalculation(sysOrigin)
        .get(LevelType.ROOM)
        .getLevel(experience);
  }

  /**
   * 用户卡片等级.
   */
  public static UserLevelExperience getUserCardLevel(SysOriginPlatformEnum sysOrigin,
      Long experience) {

    if (Objects.isNull(sysOrigin)) {
      throw new IllegalArgumentException(ERROR_USER_LEVEL_REQUIRED);
    }

    return getLevelCalculation(sysOrigin)
        .get(LevelType.CARD)
        .getLevel(experience);
  }

  private static Map<LevelType, LevelCalculation> getLevelCalculation(SysOriginPlatformEnum sysOrigin) {
    return Optional.ofNullable(SYS_ORIGIN_CONFIG_MAP.get(sysOrigin)).orElse(DEFAULT_LEVEL_MAP);
  }

  enum LevelType {
    /**
     * 用户：财富&魅力.
     */
    USER,

    /**
     * 房间
     */
    ROOM,

    /**
     * 卡片等级.
     */
    CARD

  }

}
