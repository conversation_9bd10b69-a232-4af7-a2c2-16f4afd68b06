package com.red.circle.common.business.core.enums;

import com.red.circle.common.business.core.ResultStatus;
import com.red.circle.framework.dto.IResponseErrorCode;

/**
 * 共用业务状态码.
 *
 * <AUTHOR> on 2020/8/20
 */
public enum CommonErrorEnum implements IResponseErrorCode {

  /**
   * 添加失败.
   */
  SAVE_FAILURE(1000, "add failed"),
  /**
   * 修改失败.
   */
  UPDATE_FAILURE(1001, "fail to edit"),
  /**
   * 删除失败.
   */
  DELETE_FAILURE(1002, "failed to delete"),
  /**
   * 发布失败.
   */
  RELEASE_FAILURE(1003, "Publishing failed"),

  /**
   * 上传失败.
   */
  UPLOAD_FAILURE(1004, "upload failed"),
  /**
   * 发送失败.
   */
  SEND_FAILURE(1005, "Failed to send"),

  /**
   * 类型不在范围.
   */
  TYPE_IS_NOT_IN_SCOPE(1006, "Type is not in scope"),

  /**
   * 手机号码错误.
   */
  CELLPHONE_ERROR(1007, "Mobile number error"),
  /**
   * 验证码发送失败.
   */
  SMS_SEND_FAILURE(1008, "Verification code failed to send"),
  /**
   * 验证码已发送不可重复再发.
   */
  SMS_HAS_BEEN_SENT_NON_REPEATABLE(1009,
      "The verification code has been sent and cannot be repeated."),
  /**
   * 上传资源超出限制.
   */
  UPLOAD_RESOURCE_EXCEEDS_LIMIT(1010, "Upload resource exceeds limit"),
  /**
   * 未找到相关信息.
   */
  NO_RELATED_INFORMATION_FOUND(1011, "No related information found"),
  /**
   * 验证码不匹配.
   */
  VERIFICATION_CODE_NOT_MATCH(1012, "Verification code does not match"),

  /**
   * 未找到platform_version.
   */
  NOT_FOUND_PLATFORM_VERSION(1013, "Not found Platform-Version"),
  /**
   * 数据异常.
   */
  DATA_ERROR(1014, "Data error"),
  /**
   * 不可忽略必填字段.
   */
  REQUIRED_FIELDS_CANNOT_BE_IGNORED(1015, "Required fields cannot be ignored"),
  /**
   * 经纬度错误.
   */
  LATITUDE_AND_LONGITUDE_ERROR(1016, "Latitude and longitude error"),
  /**
   * 上传图片违规.
   */
  UPLOAD_PICTURE_VIOLATION(1017, "Upload image violation"),
  /**
   * 验证签名失败.
   */
  VERIFY_SIGNATURE_FAILURE(1018, "Verify signature failure"),
  /**
   * 限制访问资源.
   */
  GET_RESOURCES_RESTRICTED(1019, "Access to resources is restricted"),
  /**
   * 配对成功.
   */
  PAIR_SUCCESS(1020, "Pair success"),
  /**
   * 解锁失败.
   */
  UNLOCK_FAILURE(1030, "Unlock failure"),
  /**
   * 订单已创建.
   */
  ORDER_EXISTS_CREATED(1031, "Order created"),
  /**
   * 拒绝接受.
   */
  REFUSE_ACCEPT(1032, "Refuse accept"),

  /**
   * 未找到记录信息.
   */
  NOT_FOUND_RECORD_INFO(1033, "Not found record info"),

  /**
   * 没有找到映射信息.
   */
  NOT_FOUND_MAPPING_INFO(1034, "Not found mapping info"),

  /**
   * 不可重复领取.
   */
  NON_REPEATABLE(1035, "Non-repeatable"),

  /**
   * 验证订单失败.
   */
  VALIDATION_ORDER_FAILED(1036, "Validation order failed"),

  /**
   * 未找到购买记录.
   */
  NO_PURCHASE_RECORD_FOUND(1037, "No purchase record found"),

  /**
   * 单据已过期.
   */
  RECEIPT_EXPIRED(1038, "Receipt expired"),

  /**
   * 调用第三方服务异常.
   */
  THIRD_PARTY_SERVER_ERROR(1039, "Third party server error"),

  /**
   * 没有找到服务可接受的语言.
   */
  NOT_FOUND_SERVER_ACCEPT_LANG_HEAD(1031, "Not found Server-Accept-Lang"),

  /**
   * 双方使用语言相同.
   */
  SAME_LANGUAGE(1032, "Same language"),

  /**
   * 不支持的翻译内容.
   */
  NOT_SUPPORT_TRANSLATE_CONTENT(1033, "Not support translate content"),

  /**
   * 提交失败.
   */
  SUBMIT_FAIL(1034, "Submission Failed"),

  /**
   * 订单未支付.
   */
  ORDER_UNPAID(1035, "Order unpaid"),

  /**
   * 操作失败.
   */
  OPERATING_FAILURE(1036, "operation failed"),

  /**
   * platform_version格式错误.
   */
  PLATFORM_VERSION_FORMAT_ERROR(1037, "Platform-Version format error"),

  /**
   * Origin_Platform格式错误.
   */
  ORIGIN_PLATFORM_ERROR(1038, "Origin-Platform format error"),

  /**
   * Platform_Channel错误.
   */
  PLATFORM_CHANNEL_ERROR(1039, "Platform-Channel format error"),

  /**
   * 版本升级.
   */
  UPGRADE_VERSION(2034, "Please upgrade version"),

  /**
   * 不接受请求.
   */
  NOT_ACCEPT_REQUEST(2035, "Do not accept requests"),
  /**
   * 没找到枚举设置.
   */
  NOT_FOUND_ENUM_SETTING(2035, "Not found enum setting"),
  /**
   * 领取失败.
   */
  RECEIVE_FAIL(2036, "Receive fail"),
  /**
   * 请求限流.
   */
  REQUEST_LIMITING(2037, "Request Limiting"),

  /**
   * 没有合适的内容.
   */
  NO_SUITABLE_CONTENT(2038, "No suitable content"),

  /**
   * 您所在的地区系统暂不支持.
   */
  NOT_SUPPORTED_REGION(2039, "Not supported by the system in your region"),

  /**
   * 配置错误.
   */
  CONFIGURATION_ERROR(2040, "Configuration error"),

  /**
   * 数量必须大于零.
   */
  QUANTITY_MUST_GT_ZERO(2040, "Value must be greater than zero"),

  /**
   * 国家没有开放.
   */
  COUNTRY_NOT_OPEN(2041, "The country is not open"),

  /**
   * 设备不可用.
   */
  DEVICE_UNAVAILABLE(2042, "Device unavailable"),

  /**
   * 权限不足.
   */
  INSUFFICIENT_PERMISSION(2043, "Insufficient permission"),

  /**
   * 已经是好友.
   */
  ALREADY_FRIENDS(2044, "Already friends"),

  /**
   * 注册设备达到上限.
   */
  REGISTERED_DEVICES_LIMIT(2045, "Registered devices reached the upper limit"),

  /**
   * 不可领取.
   */
  UNCLAIMABLE(2046, "Unclaimable"),

  /**
   * 不可重复发送.
   */
  NOT_REPEATABLE(2047, "Not repeatable"),

  /**
   * 信息填写不完整.
   */
  INCOMPLETE_INFORMATION(2048, "Incomplete information"),

  /**
   * 已有工会,无法新建.
   */
  FAMILY_EXISTENCE(2049, "You already have a family and cannot be created"),

  /**
   * 没有达到创建工会的条件.
   */
  NOT_QUALIFIED_CREATE_FAMILY(2050, "Does not meet the conditions for creating a family"),

  /**
   * 创始人不能退出工会.
   */
  FOUNDER_NOT_ALLOWED_EXIT_FAMILY(2051, "Family founders cannot withdraw"),

  /**
   * 没有工会信息，核对失败.
   */
  NOT_EXIST_FAMILY_INFO_DATA(2052, "No family information, verification failed"),

  /**
   * 信息已经处理.
   */
  MSG_REPEAT_PROCESSING(2053, "Information has been processed"),

  /**
   * 已存在工会，无法加入.
   */
  THERE_ARE_FAMILIES_ERROR(2054, "Family already exists, unable to join"),

  /**
   * 工会拒绝用户加入.
   */
  FAMILY_REFUSE_JOIN(2055, "The family has rejected your application to join"),

  /**
   * 工会满员，无法加入.
   */
  FAMILY_MEMBER_MAX(2056, "Family members have reached, unable to join new members"),

  /**
   * 工会管理员满员，无法授权.
   */
  FAMILY_MANAGE_MAX(2057, "The administrator is full, unable to add an administrator"),

  /**
   * 不能重复申请.
   */
  REPEAT_APPLICATION(2058, "Do not apply repeatedly, please wait patiently for the result"),

  /**
   * 没有达到领取要求.
   */
  CAN_NOT_RECEIVE(2059, "Did not meet the reward requirements"),

  /**
   * 不是代理.
   */
  NOT_AGENT_ERROR(2060, "Not an agent"),

  /**
   * 不是主播.
   */
  NOT_ANCHOR_ERROR(2061, "Not an anchor"),

  /**
   * 已经是代理或主播.
   */
  IS_AGENT_OR_ANCHOR(2062, "You have become an anchor or an agent"),

  /**
   * 财富等级不足30级.
   */
  INSUFFICIENT_LEVEL_30_ERROR(2063, "You can apply only if you have a wealth level of at least 30"),

  /**
   * 状态错误.
   */
  STATE_ERROR(2064, "State error"),

  /**
   * 请选择座驾.
   */
  CAR_SELECT_ERROR(2065, "Please choose a car"),

  /**
   * 数据已存在.
   */
  INFORMATION_EXISTS(2066, "Information already exists"),

  /**
   * 操作冲突.
   */
  OPERATION_CONFLICT(2067, "Operation conflict"),

  /**
   * 请选择系统平台.
   */
  SYSTEM_PLATFORM_IS_NULL(2068, "Please choose a system platform"),

  /**
   * 废弃API.
   */
  API_DEPRECATED(2068, "API Deprecated"),

  /**
   * 系统已关闭.
   */
  SYSTEM_IS_DOWN(2069, "System is down"),

  /**
   * 房间不存在.
   */
  ROOM_NOT_EXIST(2070, "Room does not exist"),

  /**
   * 重复提交.
   */
  REPEATED_SUBMIT(2071, "repeated submit"),

  /**
   * 没有上传收礼物的用户.
   */
  RECEIVER_IS_NULL_ERROR(2073, "Please select the user who will receive the gift"),

  /**
   * 系统不支持.
   */
  SYSTEM_DOES_NOT_SUPPORT(2074, "System does not support"),

  /**
   * 数据解析错误.
   */
  DATA_PARSING_ERROR(2075, "data parsing error"),

  /**
   * 系统不支持货币.
   */
  SYSTEM_NOT_SUPPORT_CURRENCY(2076, "System commodity currency is not supported"),

  /**
   * 请稍后再试.
   */
  PLEASE_TRY_LATER(2077, "There are currently many users, please try again later"),

  /**
   * 余额不足.
   */
  INSUFFICIENT_BALANCE_ERROR(2078, "Insufficient balance"),

  /**
   * 存在申请或者重复申请.
   */
  EXIST_OR_REPEAT_APPLY_ERROR(2079, "There is an existing invitation or duplicate application"),

  /**
   * 表情已拥有，无需重复购买.
   */
  EMOJI_EXIST_DONT_BUY_ERROR(2080, "You already own the emoji, no need to re-purchase"),

  /**
   * 财富与魅力等级过低，无法发布动态.
   */
  WEALTH_AND_CHARM_INSUFFICIENT_ERROR(2081,
      "Wealth and Charisma levels are not eligible for posting, please upgrade your level"),

  /**
   * 今日动态已达上限.
   */
  DYNAMIC_QUANTITY_LIMIT_ERROR(2082, "You have reached the maximum number of posts today"),

  /**
   * 资源不足.
   */
  LACK_OF_RESOURCES(2083, "lack of resources"),

  /**
   * 第三方信息错误.
   */
  THIRD_PARTY_ERROR(2084, "Incorrect third-party information"),

  /**
   * 创建订单失败.
   */
  CREATE_ORDER_ERROR(2085, "Failed to create order"),

  /**
   * 订单不存在或已过期.
   */
  ORDER_NOT_EXIST_OR_EXPIRED(2086, "Order does not exist or has expired"),

  /**
   * 重复通知.
   */
  REPEATED_NOTICE(2087, "repeated notice"),

  /**
   * 商品不存在.
   */
  PRODUCT_NOT_EXIST_ERROR(2088, "Product does not exist"),

  /**
   * 最多输入一百个字.
   */
  TEXT_LONGER_THAN_ERROR(2089, "Enter up to 100 characters"),

  /**
   * 已经是最高等级了.
   */
  LEVEL_LIMIT_ERROR(2090, "already the highest level"),

  /**
   * 月初月末两天属于主播，代理薪资结算时间，不能删除，也不能退出.
   */
  SETTLEMENT_TIME_NOT_DEL(2091, "This operation cannot be performed during special time periods"),

  /**
   * 在无效的时间段内操作.
   */
  INVALID_TIME_OPERATION_ERROR(2092, "Please operate within the specified time period"),

  /**
   * 序列号错误.
   */
  SERIALIZABLE_FAIL(2092, "serializable fail!"),

  /**
   * 锁定状态.
   */
  LOCK_STATUS(2093, "Lock status"),

  /**
   * 红包金币数量太少.
   */
  RED_PACKET_COINS_TOO_LITTLE(2094, "The number of red Packet gold coins is too small"),

  /**
   * 请下载aswat.
   */
  DOWNLOAD_ASWAT(2095, "Please download aswat"),
  /**
   * 系统维护中.
   */
  SYSTEM_MAINTENANCE(2096, "System maintenance"),

  /**
   * 非审核通过状态.
   */
  NOT_APPROVED_ERROR(2097, "Non-approved status"),

  /**
   * 无法跨区域发送个人红包.
   */
  NOT_CROSS_REGION_RED_PACKET_ERROR(2098, "Unable to send personal red packets across regions"),

  /**
   * 选择购买麦位数量类型错误.
   */
  ROOM_MIKE_NUM_TYPE_ERROR(2098, "Room mike number type  error"),

  /**
   * 请求IP获取失败.
   */
  REQUEST_IP_GET_FAIL(2099, "Failed to request IP acquisition"),

  /**
   * 已购买过该特殊麦位.
   */
  ROOM_MIKE_TYPE_EXIST_DONT_BUY_ERROR(2101,
      "You already own the room mike type, no need to re-purchase"),

  /**
   * 已购买过该麦位数量类型.
   */
  ROOM_MIKE_NUM_TYPE_EXIST_DONT_BUY_ERROR(2102,
      "You already own the room mike numbers, no need to re-purchase"),

  /**
   * 区域不支持.
   */
  REGION_NOT_SUPPORTED_ERROR(2103, "Both parties are not in the same region"),

  /**
   * 用户赠送道具已过期.
   */
  PROPS_OVERDUE_NOT_GIVE_AWAY(2104, "Props exceed the time limit not give away"),

  /**
   * 用户赠送的道具类型为贵族时 贵族数量大于等于2.
   */
  NOBLE_VIP_PROPS_NOT_ENOUGH(2105, "Noble VIP props not enough, not give away"),

  /**
   * 用户财富等级未大于 5.
   */
  USER_WEALTH_LEVEL_NOT_ENOUGH(2106, "User wealth level not enough, not give away"),

  /**
   * 用户背包主题不可赠送.
   */
  USER_KNAPSACK_THEME_NOT_GIVE_AWAY(2107, "User knapsack theme not give away"),

  /**
   * 用户道具已在使用不可赠送.
   */
  USER_PROPS_IS_USEING_NOT_GIVE_AWAY(2108, "User props is useing not give away"),

  /**
   * 没有放开.
   */
  NOT_OPEN_ERROR(2109, "not open"),

  /**
   * 已经达到了上限.
   */
  TOP_LIMIT_ERROR(2110, "We have reached the upper limit"),

  /**
   * 不能邀请用户,请联系管理员.
   */
  NOT_INVITE_USER_ERROR(2111, "Unable to invite him, please contact the administrator"),

  /**
   * 用户道具只能赠送一次.
   */
  USER_PROPS_ONLY_GIVEN_ONCE(2112, "User props not give away， only be given once"),

  /**
   * 经销商家卖家数量已达上限.
   */
  USER_FREIGHT_SELLER_LIMIT(2113, "The number of sellers and merchants has reached the maximum limit"),

  /**
   * 卖家已存在.
   */
  USER_FREIGHT_SELLER_EXISTS(2114, "Seller already exists"),

  /**
   * 不是卖家.
   */
  NOT_SELLER(2115, "Not a seller"),

  /**
   * 版本升级.
   */
  NEW_UPGRADE_VERSION(2116, "Please upgrade version"),

  /**
   * 告白机会无效.
   */
  USER_CONFESSION_CHANCE_INVALID(2117, "Invalid user confession opportunity"),

  /**
   * 抽奖次数不足.
   */
  USER_RECHARGE_DRAW_NOT_ENOUGH(2118, "Draw not enough"),

  /**
   * 抽奖频率太快.
   */
  USER_RECHARGE_DRAW_TOO_FAST(2119, "Draw too fast"),

  ;

  private final Integer code;

  private final String message;

  CommonErrorEnum(Integer code, String message) {
    this.code = code;
    this.message = message;
  }

  @Override
  public Integer getCode() {
    return code;
  }

  @Override
  public String getMessage() {
    return message;
  }

  @Override
  public String getErrorCodeName() {
    return null;
  }
}
