package com.red.circle.common.business.core.enums;

import com.red.circle.tool.core.collection.CollectionUtils;
import java.util.Map;
import java.util.Objects;
import lombok.Getter;

/**
 * 资料审批类型.
 *
 * <AUTHOR> on 2020/1/10 18:10
 */
@Getter
public enum DataApprovalTypeEnum {

  /**
   * 不处理.
   */
  NONE("NONE", null),

  /**
   * 用户昵称.
   */
  NICKNAME("用户昵称", Type.TEXT),

  /**
   * 用户头像.
   */
  AVATAR("用户头像", Type.IMAGE),

  /**
   * 照片墙.
   */
  PHOTO_WALL("照片墙", Type.IMAGE),

  /**
   * 房间昵称.
   */
  ROOM_NICKNAME("房间昵称", Type.TEXT),

  /**
   * 房间头像.
   */
  ROOM_AVATAR("房间头像", Type.IMAGE),

  /**
   * 房间通知公告.
   */
  ROOM_NOTICE("房间通知公告", Type.TEXT),

  /**
   * 个人资料签名描述.
   */
  PROFILE_DESC("个人资料签名", Type.TEXT),

  /**
   * 工会头像.
   */
  FAMILY_AVATAR("工会头像", Type.IMAGE),

  /**
   * 工会昵称.
   */
  FAMILY_NICKNAME("工会昵称", Type.TEXT),

  /**
   * 工会公告.
   */
  FAMILY_NOTICE("工会公告", Type.TEXT),

  /**
   * 举报动态.
   */
  DYNAMIC_REPORT("举报动态", Type.TEXT),

  /**
   * 动态内容.
   */
  DYNAMIC_CONTENT("动态内容", Type.IMAGE),

  /**
   * 团队头像.
   */
  TEAM_AVATAR("团队头像", Type.IMAGE),

  /**
   * 团队昵称.
   */
  TEAM_NICKNAME("团队昵称", Type.TEXT),
  ;

  private final String desc;
  private final Type type;

  DataApprovalTypeEnum(String desc, Type type) {
    this.desc = desc;
    this.type = type;
  }

  public static Boolean checkDynamic(DataApprovalTypeEnum type) {
    return Objects.equals(type, DYNAMIC_CONTENT) ||
        Objects.equals(type, DYNAMIC_REPORT);
  }

  public Map<DataApprovalTypeEnum, String> approvalVal(String val) {
    Map<DataApprovalTypeEnum, String> paramMap = CollectionUtils.newHashMap();
    paramMap.put(this, val);
    return paramMap;
  }

  public static DataApprovalTypeEnum getApprovalVal(String val) {
    for (DataApprovalTypeEnum ele : values()) {
      if(ele.name().equals(val)) return ele;
    }
    return null;
  }

  public enum Type {
    TEXT, IMAGE, VIDEO
  }

}
