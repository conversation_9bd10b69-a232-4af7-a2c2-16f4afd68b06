package com.red.circle.common.business.core.enums;

import com.google.common.collect.Lists;
import com.red.circle.component.core.enums.ISysOriginPlatform;
import com.red.circle.tool.core.text.StringPool;
import lombok.Getter;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;

/**
 * 系统来源.
 *
 * <AUTHOR> on 2020/11/25
 */
public enum SysOriginPlatformEnum implements ISysOriginPlatform {

  MARCIE("MARCIE",1),
  @Deprecated
  YAHLLA("Yahlla", 2),
  ASWAT("Aswat", 3),
  @Deprecated
  ASWAT_LITE("AswatLite", 4),
  TWO_FUN("2Fun", 5),
  YOLO("Yolo", 6),
  TARAB("Tarab",7),
  HALAR("Halar",8),
  LOTFUN("Lotfun",9),
  HOOKA("Hooka",10),
  ;

  /**
   * 语言房系统.
   */
  private static final List<SysOriginPlatformEnum> VOICE_SYSTEM = List.of(MARCIE, HOOKA);

  /**
   * 平台名称.
   */
  private final String originName;

  /**
   * 展示顺序.
   */
  @Getter
  private final int sort;

  SysOriginPlatformEnum(String originName, int sort) {
    this.originName = originName;
    this.sort = sort;
  }

  public boolean isVoiceSystem() {
    return true;
  }

  public static List<SysOriginPlatformEnum> getVoiceSystems() {
    return Lists.newArrayList(SysOriginPlatformEnum.values());
  }

  public static boolean isVoiceSystem(String sysOrigin) {
    return isVoiceSystem(toEnum(sysOrigin));
  }

  public static boolean isVoiceSystem(SysOriginPlatformEnum sysOrigin) {
    return true;
  }

  public static SysOriginPlatformEnum toEnum(String key) {
    try {
      return SysOriginPlatformEnum.valueOf(key);
    } catch (Exception ex) {
      // ignore
    }
    return null;
  }

  public String concatUnderscoreJoin(String val) {
    return this.name().concat("_").concat(val);
  }

  @Override
  public String getSysOrigin() {
    return this.name();
  }

  @Override
  public String getSysOriginName() {
    return this.originName;
  }

  public static Integer getSort(String key) {
    return Arrays.stream(SysOriginPlatformEnum.values())
        .filter(sysOriginPlatformEnum -> Objects.equals(sysOriginPlatformEnum.name(), key))
        .findFirst()
        .map(SysOriginPlatformEnum::getSort)
        .orElse(Integer.MAX_VALUE);
  }

  public static String getSysOriginName(String key) {
    return Arrays.stream(SysOriginPlatformEnum.values())
        .filter(sysOriginPlatformEnum -> Objects.equals(sysOriginPlatformEnum.name(), key))
        .findFirst()
        .map(SysOriginPlatformEnum::getSysOriginName)
        .orElse(StringPool.UNKNOWN);
  }

}
