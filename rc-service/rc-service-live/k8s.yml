---
apiVersion: apps/v1
kind: Deployment
metadata:
  annotations: {}
  labels:
    app: live
  name: live
  namespace: local
  resourceVersion: '83779749'
spec:
  progressDeadlineSeconds: 600
  replicas: 1
  revisionHistoryLimit: 3
  selector:
    matchLabels:
      app: live
  strategy:
    rollingUpdate:
      maxSurge: 25%
      maxUnavailable: 25%
    type: RollingUpdate
  template:
    metadata:
      annotations:
        kubectl.kubernetes.io/restartedAt: '2025-05-22T10:42:36+08:00'
      creationTimestamp: null
      labels:
        app: live
    spec:
      containers:
        - env:
            - name: SERVER_PORT
              value: '1900'
            - name: SERVER_PROFILE_ACTIVE
              value: dev
            - name: TRACE_PROTOCOL
              value: grpc
            - name: TRACE_ENDPOINT
              value: 'https://asxxxxxdev.ap-southeast-1.log.aliyuncs.com:10010'
            - name: TRACE_COMPRESSION
              value: gzip
            - name: TRACE_HEADERS
              value: >-
                x-sls-otel-project=aswat-app-trace-dev,x-sls-otel-instance-id=aswat-develop,x-sls-otel-ak-id=LTAI5xxxeKrE5x,x-sls-otel-ak-secret=3kKQWtxxxx6SzmHz
            - name: TRACE_HOST_NAME
              valueFrom:
                fieldRef:
                  apiVersion: v1
                  fieldPath: status.podIP
            - name: NACOS_HOST
              valueFrom:
                configMapKeyRef:
                  key: nacos.host
                  name: nacos
            - name: NACOS_PORT
              valueFrom:
                configMapKeyRef:
                  key: nacos.port
                  name: nacos
            - name: NACOS_USERNAME
              valueFrom:
                configMapKeyRef:
                  key: nacos.username
                  name: nacos
            - name: NACOS_PASSWORD
              valueFrom:
                configMapKeyRef:
                  key: nacos.password
                  name: nacos
          image: >-
            794038239327.dkr.ecr.ap-southeast-1.amazonaws.com/halar-dev:live-20250424v091149
          imagePullPolicy: IfNotPresent
          lifecycle:
            preStop:
              exec:
                command:
                  - /bin/bash
                  - '-c'
                  - >-
                    curl -X POST
                    http://localhost:1900/actuator/nacosservicederegister?key=CxILm9hA1b9hF3Hl
                    --header 'Content-Type:
                    application/vnd.spring-boot.actuator.v2+json;charset=UTF-8'
                  - sleep 30
          livenessProbe:
            failureThreshold: 2
            httpGet:
              path: /actuator/health
              port: 1900
              scheme: HTTP
            periodSeconds: 60
            successThreshold: 1
            timeoutSeconds: 20
          name: live
          ports:
            - containerPort: 1900
              protocol: TCP
          readinessProbe:
            failureThreshold: 2
            httpGet:
              path: /actuator/health
              port: 1900
              scheme: HTTP
            periodSeconds: 5
            successThreshold: 1
            timeoutSeconds: 2
          resources:
            limits:
              memory: 1Gi
            requests:
              memory: 500Mi
          startupProbe:
            failureThreshold: 60
            httpGet:
              path: /actuator/health
              port: 1900
              scheme: HTTP
            periodSeconds: 5
            successThreshold: 1
            timeoutSeconds: 5
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
      dnsPolicy: ClusterFirst
      imagePullSecrets:
        - name: aliyun-secret
      restartPolicy: Always
      schedulerName: default-scheduler
      securityContext: {}
      terminationGracePeriodSeconds: 120
status:
  availableReplicas: 1
  conditions:
    - lastTransitionTime: '2025-05-22T02:44:22Z'
      lastUpdateTime: '2025-05-22T02:44:22Z'
      message: Deployment has minimum availability.
      reason: MinimumReplicasAvailable
      status: 'True'
      type: Available
    - lastTransitionTime: '2025-02-26T02:38:46Z'
      lastUpdateTime: '2025-05-22T02:44:22Z'
      message: ReplicaSet "live-7c5cd968d7" has successfully progressed.
      reason: NewReplicaSetAvailable
      status: 'True'
      type: Progressing
  observedGeneration: 42
  readyReplicas: 1
  replicas: 1
  updatedReplicas: 1

---
apiVersion: v1
kind: Service
metadata:
  annotations: {}
  name: live
  namespace: local
  resourceVersion: '22868035'
spec:
  clusterIP: **************
  clusterIPs:
    - **************
  internalTrafficPolicy: Cluster
  ipFamilies:
    - IPv4
  ipFamilyPolicy: SingleStack
  ports:
    - port: 80
      protocol: TCP
      targetPort: 1900
  selector:
    app: live
  sessionAffinity: None
  type: ClusterIP
status:
  loadBalancer: {}

