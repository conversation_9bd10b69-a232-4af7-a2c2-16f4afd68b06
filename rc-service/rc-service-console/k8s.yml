---
apiVersion: apps/v1
kind: Deployment
metadata:
  annotations: {}
  labels:
    app: console
  name: console
  namespace: local
  resourceVersion: '92899554'
spec:
  progressDeadlineSeconds: 600
  replicas: 1
  revisionHistoryLimit: 2
  selector:
    matchLabels:
      app: console
  strategy:
    rollingUpdate:
      maxSurge: 25%
      maxUnavailable: 25%
    type: RollingUpdate
  template:
    metadata:
      annotations:
        kubectl.kubernetes.io/restartedAt: '2025-05-22T10:42:27+08:00'
      creationTimestamp: null
      labels:
        app: console
        pod-template-hash: 7865d8fd6d
    spec:
      containers:
        - env:
            - name: JVM_XMS
              value: 520m
            - name: JVM_XMX
              value: 520m
            - name: JVM_XMN
              value: 195m
            - name: SERVER_PORT
              value: '1300'
            - name: SERVER_PROFILE_ACTIVE
              value: dev
            - name: TRACE_PROTOCOL
              value: grpc
            - name: TRACE_ENDPOINT
              value: 'https://aswxxxxdev.ap-southeast-1.log.aliyuncs.com:10010'
            - name: TRACE_COMPRESSION
              value: gzip
            - name: TRACE_HEADERS
              value: >-
                x-sls-otel-project=aswat-app-trace-dev,x-sls-otel-instance-id=aswat-develop,x-sls-otel-ak-id=LTAI5xxxfeKrE5x,x-sls-otel-ak-secret=3kKQWtvxxx6SzmHz
            - name: TRACE_HOST_NAME
              valueFrom:
                fieldRef:
                  apiVersion: v1
                  fieldPath: status.podIP
            - name: NACOS_HOST
              valueFrom:
                configMapKeyRef:
                  key: nacos.host
                  name: nacos
            - name: NACOS_PORT
              valueFrom:
                configMapKeyRef:
                  key: nacos.port
                  name: nacos
            - name: NACOS_USERNAME
              valueFrom:
                configMapKeyRef:
                  key: nacos.username
                  name: nacos
            - name: NACOS_PASSWORD
              valueFrom:
                configMapKeyRef:
                  key: nacos.password
                  name: nacos
          image: >-
            794038239327.dkr.ecr.ap-southeast-1.amazonaws.com/halar-dev:console-20250423v021919
          imagePullPolicy: IfNotPresent
          lifecycle:
            preStop:
              exec:
                command:
                  - /bin/bash
                  - '-c'
                  - >-
                    curl -X POST
                    http://localhost:1300/actuator/nacosservicederegister?key=CxILm9hA1b9hF3Hl
                    --header 'Content-Type:
                    application/vnd.spring-boot.actuator.v2+json;charset=UTF-8'
                  - sleep 30
          livenessProbe:
            failureThreshold: 2
            httpGet:
              path: /console/actuator/health
              port: 1300
              scheme: HTTP
            periodSeconds: 5
            successThreshold: 1
            timeoutSeconds: 20
          name: console
          ports:
            - containerPort: 1300
              protocol: TCP
          readinessProbe:
            failureThreshold: 2
            httpGet:
              path: /console/actuator/health
              port: 1300
              scheme: HTTP
            periodSeconds: 5
            successThreshold: 1
            timeoutSeconds: 2
          resources:
            limits:
              memory: 1Gi
            requests:
              memory: 1Gi
          startupProbe:
            failureThreshold: 60
            httpGet:
              path: /console/actuator/health
              port: 1300
              scheme: HTTP
            periodSeconds: 5
            successThreshold: 1
            timeoutSeconds: 5
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
      dnsPolicy: ClusterFirst
      imagePullSecrets:
        - name: aliyun-secret
      restartPolicy: Always
      schedulerName: default-scheduler
      securityContext: {}
      terminationGracePeriodSeconds: 120
status:
  availableReplicas: 1
  conditions:
    - lastTransitionTime: '2025-04-21T09:23:26Z'
      lastUpdateTime: '2025-05-22T02:45:02Z'
      message: ReplicaSet "console-9c895879c" has successfully progressed.
      reason: NewReplicaSetAvailable
      status: 'True'
      type: Progressing
    - lastTransitionTime: '2025-06-17T00:48:24Z'
      lastUpdateTime: '2025-06-17T00:48:24Z'
      message: Deployment has minimum availability.
      reason: MinimumReplicasAvailable
      status: 'True'
      type: Available
  observedGeneration: 32
  readyReplicas: 1
  replicas: 1
  updatedReplicas: 1

---
apiVersion: v1
kind: Service
metadata:
  annotations: {}
  name: console
  namespace: local
  resourceVersion: '22868244'
spec:
  clusterIP: **************
  clusterIPs:
    - **************
  internalTrafficPolicy: Cluster
  ipFamilies:
    - IPv4
  ipFamilyPolicy: SingleStack
  ports:
    - port: 80
      protocol: TCP
      targetPort: 1300
  selector:
    app: console
  sessionAffinity: None
  type: ClusterIP
status:
  loadBalancer: {}

