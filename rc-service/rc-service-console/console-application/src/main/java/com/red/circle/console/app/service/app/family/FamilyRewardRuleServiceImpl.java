package com.red.circle.console.app.service.app.family;

import com.red.circle.framework.core.asserts.ResponseAssert;
import com.red.circle.framework.dto.PageResult;
import com.red.circle.other.inner.endpoint.family.FamilyRewardRuleClient;
import com.red.circle.other.inner.model.cmd.famliy.FamilyRewardRuleCmd;
import com.red.circle.other.inner.model.cmd.famliy.FamilyRewardRuleQryCmd;
import com.red.circle.other.inner.model.dto.famliy.FamilyRewardRuleDTO;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 工会奖励规则 服务实现类.
 * </p>
 *
 * <AUTHOR>
 * @since 2022-06-30
 */
@Service
@RequiredArgsConstructor
public class FamilyRewardRuleServiceImpl implements FamilyRewardRuleService {

  private final FamilyRewardRuleClient familyRewardRuleClient;


  @Override
  public void save(FamilyRewardRuleCmd familyRewardRuleParam) {
    ResponseAssert.requiredSuccess(familyRewardRuleClient.save(familyRewardRuleParam));
  }


  @Override
  public PageResult<FamilyRewardRuleDTO> pageData(FamilyRewardRuleQryCmd queryWhere) {

    return ResponseAssert.requiredSuccess(familyRewardRuleClient.pageData(queryWhere));
  }
}
