package com.red.circle.console.app.convertor.approval.impl;

import com.red.circle.console.app.convertor.approval.ApprovalFamilySettingDataAppConvertor;
import com.red.circle.console.app.dto.clienobject.approval.ApprovalFamilyCO;
import com.red.circle.other.inner.model.dto.approval.ApprovalFamilyDTO;
import org.springframework.stereotype.Component;

@Component
public class ApprovalFamilySettingDataAppConvertorImplV2 implements ApprovalFamilySettingDataAppConvertor {


        @Override
        public ApprovalFamilyCO toApprovalFamilyCO(ApprovalFamilyDTO approvalFamilyDTO) {
            if ( approvalFamilyDTO == null ) {
                return null;
            }

            ApprovalFamilyCO approvalFamilyCO = new ApprovalFamilyCO();

            approvalFamilyCO.setUserId( approvalFamilyDTO.getUserId() );
            approvalFamilyCO.setFamilyId( approvalFamilyDTO.getFamilyId() );
            approvalFamilyCO.setFamilyAccount( approvalFamilyDTO.getFamilyAccount() );
            approvalFamilyCO.setFamilyAvatar( approvalFamilyDTO.getFamilyAvatar() );
            approvalFamilyCO.setFamilyName( approvalFamilyDTO.getFamilyName() );
            approvalFamilyCO.setFamilyNotice( approvalFamilyDTO.getFamilyNotice() );
            approvalFamilyCO.setProfileDesc( approvalFamilyDTO.getProfileDesc() );
            approvalFamilyCO.setUpdateTime( approvalFamilyDTO.getUpdateTime() );
            approvalFamilyCO.setFamilyWhatapp( approvalFamilyDTO.getFamilyWhatapp() );
            approvalFamilyCO.setLeaderIdCardPhoto( approvalFamilyDTO.getLeaderIdCardPhoto() );
            return approvalFamilyCO;
        }

}
