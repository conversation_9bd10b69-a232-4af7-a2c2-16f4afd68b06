package com.red.circle.console.app.service.app.approval;

import com.red.circle.console.app.convertor.approval.ApprovalFamilySettingDataAppConvertor;
import com.red.circle.console.app.dto.clienobject.approval.ApprovalFamilyCO;
import com.red.circle.framework.core.asserts.ResponseAssert;
import com.red.circle.framework.dto.PageResult;
import com.red.circle.other.inner.endpoint.dynamic.ApprovalFamilySettingDataClient;
import com.red.circle.other.inner.model.cmd.approval.ApprovalFamilyCmd;
import com.red.circle.other.inner.model.cmd.approval.ApprovalProfileDescQryCmd;
import com.red.circle.other.inner.model.dto.approval.ApprovalFamilyDTO;
import com.red.circle.tool.core.collection.CollectionUtils;
import com.red.circle.other.inner.endpoint.user.user.UserExpandClient;
import com.red.circle.other.inner.endpoint.user.user.UserProfileClient;
import com.red.circle.other.inner.model.dto.user.UserExpandDTO;
import com.red.circle.other.inner.model.dto.user.UserProfileDTO;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

/**
 * 工会资料审批
 *
 * <AUTHOR> on 2024/1/20
 */
@Service
@RequiredArgsConstructor
public class ApprovalFamilySettingDataServiceImpl implements ApprovalFamilySettingDataService {

  private final UserExpandClient userExpandClient;
  private final UserProfileClient userProfileClient;
  private final ApprovalFamilySettingDataClient approvalFamilySettingDataClient;
  @Qualifier("approvalFamilySettingDataAppConvertorImplV2")
  private final ApprovalFamilySettingDataAppConvertor approvalFamilySettingDataAppConvertor;

  @Override
  public PageResult<ApprovalFamilyCO> pageFamilyApproval(ApprovalProfileDescQryCmd query) {

    PageResult<ApprovalFamilyDTO> pageResult = ResponseAssert.requiredSuccess(
        approvalFamilySettingDataClient.pageFamilyApproval(query));

    if (CollectionUtils.isEmpty(pageResult.getRecords())) {
      pageResult.convert(approvalFamilySettingDataAppConvertor::toApprovalFamilyCO);
    }

    Map<Long, UserProfileDTO> userMap = ResponseAssert.requiredSuccess(
        userProfileClient.mapByUserIds(getUserIds(pageResult)));

    Map<Long, UserExpandDTO> userExpandMap = ResponseAssert.requiredSuccess(
        userExpandClient.mapUserExpand(getUserIds(pageResult)));

    return pageResult.convert(approvalFamilyDTO -> {

      ApprovalFamilyCO approvalFamilyCO = approvalFamilySettingDataAppConvertor.toApprovalFamilyCO(
          approvalFamilyDTO);
      approvalFamilyCO.setUserBaseInfo(userMap.get(approvalFamilyDTO.getUserId()));
      approvalFamilyCO.setProfileDesc(
          Objects.nonNull(userExpandMap.get(approvalFamilyDTO.getUserId())) ? userExpandMap.get(
              approvalFamilyDTO.getUserId()).getSignature() : null);
      return approvalFamilyCO;
    });
  }

  private Set<Long> getUserIds(PageResult<ApprovalFamilyDTO> pageResult) {
    return pageResult.getRecords().stream().map(ApprovalFamilyDTO::getUserId)
        .collect(Collectors.toSet());
  }

  @Override
  public void notPass(ApprovalFamilyCmd cmd) {
    approvalFamilySettingDataClient.notPass(cmd);
  }
}
