package com.red.circle.console.app.scheduler;

import com.red.circle.component.redis.annotation.TaskCacheLock;
import com.red.circle.console.app.convertor.count.ImOperationAppConvertor;
import com.red.circle.console.infra.database.mongo.count.entity.CustomizeActiveIndex;
import com.red.circle.console.infra.database.mongo.count.entity.StatisticsDailyOperationActiveIndex;
import com.red.circle.console.infra.database.mongo.count.service.StatisticsDailyOperationActiveIndexService;
import com.red.circle.external.inner.endpoint.message.ImOperationClient;
import com.red.circle.external.inner.model.dto.OperationActiveIndexDTO;
import com.red.circle.tool.core.collection.CollectionUtils;
import com.red.circle.tool.core.date.TimestampUtils;
import com.red.circle.other.inner.endpoint.user.count.CountUserActiveIndexClient;
import java.util.List;
import java.util.Objects;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

/**
 * 运营活跃指标统计.
 *
 * <AUTHOR> on 2022/3/29
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class StatisticsOperationActiveIndexTask {

  private final ImOperationClient imOperationClient;
  private final ImOperationAppConvertor imOperationAppConvertor;
  private final CountUserActiveIndexClient countUserActiveIndexClient;
  private final StatisticsDailyOperationActiveIndexService dailyOperationActiveIndexService;

  /**
   * 每天凌晨8点执行.
   */
//  @Scheduled(cron = "0 0 8 * * ?", zone = "Asia/Shanghai")
  @TaskCacheLock(key = "STATISTICS_DAILY", expireSecond = 60 * 60)
  public void statisticsDaily() {
    statisticsDaily(3);
  }

  private void statisticsDaily(int retrySize) {
    if (retrySize <= 0) {
      log.warn("【运营指标】重试机会用完:{}", retrySize);
      return;
    }
    List<OperationActiveIndexDTO> operationActiveIndex = imOperationClient.getLatest30Days()
        .getBody();
    log.warn("operationActiveIndex {}", operationActiveIndex);
    if (CollectionUtils.isEmpty(operationActiveIndex)) {
      log.warn("【运营指标】可重试次数[{}]拉取腾讯云，没有拉去到数据", retrySize);
      statisticsDaily(--retrySize);
      return;
    }

    int hurrahQuantity = 3;
    for (OperationActiveIndexDTO activeIndex : operationActiveIndex) {
      log.warn("activeIndex {}", activeIndex);
      if (hurrahQuantity <= 0) {
        log.warn("【运营指标】连续检测N条指标已处理，结束指标入库");
        return;
      }
      if (Objects.isNull(activeIndex)) {
        log.warn("【运营指标】错误的指标数据解析");
        continue;
      }
      if (dailyOperationActiveIndexService.exists(activeIndex.getDate())) {
        --hurrahQuantity;
        log.warn("【运营指标】指标数据已存在，准备检测下一条可用检测机会:{}", hurrahQuantity);
        continue;
      }
      hurrahQuantity = 3;
      dailyOperationActiveIndexService.add(new StatisticsDailyOperationActiveIndex()
          .setDate(activeIndex.getDate())
          .setImActiveIndex(imOperationAppConvertor.toImActiveIndex(activeIndex))
          .setCustomizeActiveIndex(new CustomizeActiveIndex()
              .setAnchorActiveNum(countUserActiveIndexClient.getActiveAnchorCount(
                  activeIndex.getDate()).getBody())
              .setOrdinaryActiveNum(countUserActiveIndexClient.getActiveOrdinaryCount(
                  activeIndex.getDate()).getBody())
          ).setCreateTime(TimestampUtils.now())
      );
    }
  }

}
