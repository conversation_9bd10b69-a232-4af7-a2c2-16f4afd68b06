package com.red.circle.console.app.service.app.family;

import com.red.circle.console.app.convertor.family.FamilyAppConvertor;
import com.red.circle.console.app.dto.clienobject.family.FamilyMemberCO;
import com.red.circle.framework.core.asserts.ResponseAssert;
import com.red.circle.framework.dto.PageResult;
import com.red.circle.other.inner.endpoint.family.api.FamilyMemberClientApi;
import com.red.circle.other.inner.model.cmd.famliy.FamilyMemberQryCmd;
import com.red.circle.tool.core.collection.CollectionUtils;
import com.red.circle.other.inner.endpoint.user.user.UserProfileClient;
import com.red.circle.other.inner.model.dto.user.UserProfileDTO;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 工会成员表 服务实现类.
 * </p>
 *
 * <AUTHOR>
 * @since 2022-06-30
 */
@Service
@RequiredArgsConstructor
public class FamilyMemberServiceImpl implements FamilyMemberService {

  private final UserProfileClient userProfileClient;
  private final FamilyAppConvertor familyAppConvertor;
  private final FamilyMemberClientApi familyMemberClientApi;

  @Override
  public PageResult<FamilyMemberCO> pageData(FamilyMemberQryCmd queryWhere) {
    PageResult<FamilyMemberCO> pageResult = ResponseAssert.requiredSuccess(
        familyMemberClientApi.pageData(queryWhere)).convert(familyAppConvertor::toFamilyMemberCO);

    if (CollectionUtils.isEmpty(pageResult.getRecords())) {
      return pageResult;
    }

    Map<Long, UserProfileDTO> userMap = getUserMap(pageResult);

    return pageResult.convert(info -> {
      info.setUserBaseInfo(userMap.get(info.getMemberUserId()));
      return info;
    });
  }

  @Override
  public void delMember(Long familyId, Long memberId) {
    ResponseAssert.requiredSuccess(familyMemberClientApi.delMember(familyId, memberId));
  }

  private Map<Long, UserProfileDTO> getUserMap(PageResult<FamilyMemberCO> infoPage) {
    return ResponseAssert.requiredSuccess(userProfileClient.mapByUserIds(getUserIds(infoPage)));
  }


  private Set<Long> getUserIds(PageResult<FamilyMemberCO> infoPage) {
    return infoPage.getRecords().stream().map(FamilyMemberCO::getMemberUserId).collect(
        Collectors.toSet());
  }
}
