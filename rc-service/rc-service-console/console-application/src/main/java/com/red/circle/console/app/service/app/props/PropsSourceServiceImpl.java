package com.red.circle.console.app.service.app.props;

import com.red.circle.console.app.convertor.PropsAppConvertor;
import com.red.circle.console.app.dto.clienobject.props.PropsResourcesOpsCO;
import com.red.circle.console.app.dto.cmd.app.props.PropsSourceRecordSaveOrUpdateCmd;
import com.red.circle.framework.core.asserts.ResponseAssert;
import com.red.circle.framework.dto.PageResult;
import com.red.circle.other.inner.endpoint.material.props.PropsRedPacketSkinClient;
import com.red.circle.other.inner.endpoint.material.props.PropsSourceClient;
import com.red.circle.other.inner.enums.material.ConsolePropsTypeEnum;
import com.red.circle.other.inner.model.cmd.material.PropsSourceRecordQryCmd;
import com.red.circle.other.inner.model.dto.material.PropsRedPacketSkinDTO;
import com.red.circle.other.inner.model.dto.material.PropsResourcesDTO;
import com.red.circle.tool.core.collection.CollectionUtils;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 工会客户端 服务实现类.
 * </p>
 *
 * <AUTHOR>
 * @since 2022-06-30
 */
@Service
@RequiredArgsConstructor
public class PropsSourceServiceImpl implements PropsSourceService {

  @Qualifier("PropsAppConvertorNew")
  private final PropsAppConvertor propsAppConvertor;
  private final PropsSourceClient propsSourceClient;
  private final PropsRedPacketSkinClient propsRedPacketSkinClient;

  @Override
  public List<PropsResourcesDTO> listSysOrigin(String sysOrigin, String type) {
    return ResponseAssert.requiredSuccess(
        propsSourceClient.listSysOrigin(sysOrigin, type));
  }

  @Override
  public List<PropsResourcesDTO> listExcludeFamily(String sysOrigin, String type) {
    return ResponseAssert.requiredSuccess(propsSourceClient.listExcludeFamily(sysOrigin, type));
  }

  @Override
  public void offShelf(Long id, Boolean offShelf) {
    ResponseAssert.requiredSuccess(propsSourceClient.offShelfPropsResources(id, offShelf));
  }

  @Override
  public PageResult<PropsResourcesOpsCO> pageOps(PropsSourceRecordQryCmd qryCmd) {
    PageResult<PropsResourcesDTO> pageResult = ResponseAssert.requiredSuccess(
        propsSourceClient.pagePropsResourcesOps(qryCmd)
    );

    if (CollectionUtils.isEmpty(pageResult.getRecords())) {
      return pageResult.emptyRecords();
    }
    Map<Long, PropsRedPacketSkinDTO> redPacketSkinMap = ResponseAssert.requiredSuccess(
        propsRedPacketSkinClient.mapRedPacketSkinByIds(
            pageResult.getRecords().stream().map(PropsResourcesDTO::getId).collect(
                Collectors.toSet())));

    return pageResult.convert(propsResources -> {
          PropsResourcesOpsCO propsResourcesOpsCO = propsAppConvertor.toPropsResourcesOpsCO(propsResources);

          PropsRedPacketSkinDTO redPacketSkin = redPacketSkinMap.get(propsResources.getId());
          if (Objects.nonNull(redPacketSkin)) {
            propsResourcesOpsCO.setImNotOpenedUrl(redPacketSkin.getImNotOpenedUrl());
            propsResourcesOpsCO.setImOpenedUrl(redPacketSkin.getImOpenedUrl());
            propsResourcesOpsCO.setRoomNotOpenedUrl(redPacketSkin.getRoomNotOpenedUrl());
            propsResourcesOpsCO.setRoomOpenedUrl(redPacketSkin.getRoomOpenedUrl());
            propsResourcesOpsCO.setRoomSendCoverUrl(redPacketSkin.getRoomSendCoverUrl());
            propsResourcesOpsCO.setImSendCoverUrl(redPacketSkin.getImSendCoverUrl());
            propsResourcesOpsCO.setImOpenedUrlTwo(redPacketSkin.getImOpenedUrlTwo());
          }
          propsResourcesOpsCO.setTypeName(ConsolePropsTypeEnum.getDescValue(propsResources.getType()));
          return propsResourcesOpsCO;
        }
    );
  }

  @Override
  public void addOrUpdate(PropsSourceRecordSaveOrUpdateCmd cmd) {
    ResponseAssert.requiredSuccess(propsSourceClient.addOrUpdatePropsResources(
        propsAppConvertor.toPropsResourcesDTO(cmd)
    ));
  }

  @Override
  public PropsRedPacketSkinDTO getPropsRedPacketSkin(Long id) {
    return ResponseAssert.requiredSuccess(propsSourceClient.getPropsRedPacketSkin(id));
  }

}
