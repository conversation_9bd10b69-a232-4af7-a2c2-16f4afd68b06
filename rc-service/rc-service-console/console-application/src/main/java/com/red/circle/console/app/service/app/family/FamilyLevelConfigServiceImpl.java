package com.red.circle.console.app.service.app.family;

import com.red.circle.framework.core.asserts.ResponseAssert;
import com.red.circle.framework.dto.PageResult;
import com.red.circle.other.inner.endpoint.family.FamilyLevelConfigClient;
import com.red.circle.other.inner.model.cmd.famliy.FamilyLevelConfigCmd;
import com.red.circle.other.inner.model.cmd.famliy.FamilyLevelConfigQryCmd;
import com.red.circle.other.inner.model.dto.famliy.FamilyLevelConfigDTO;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 工会等级配置 服务实现类.
 * </p>
 *
 * <AUTHOR>
 * @since 2022-06-30
 */
@Service
@RequiredArgsConstructor
public class FamilyLevelConfigServiceImpl implements FamilyLevelConfigService {

  private final FamilyLevelConfigClient familyLevelConfigClient;


  @Override
  public PageResult<FamilyLevelConfigDTO> pageData(FamilyLevelConfigQryCmd queryWhere) {
    return ResponseAssert.requiredSuccess(familyLevelConfigClient.pageData(queryWhere));
  }

  @Override
  public void saveFamilyLevelConfig(FamilyLevelConfigCmd cmd) {
    ResponseAssert.requiredSuccess(familyLevelConfigClient.saveFamilyLevelConfig(cmd));
  }
}
