package com.red.circle.console.app.service.app.family;

import com.red.circle.framework.core.asserts.ResponseAssert;
import com.red.circle.framework.dto.PageResult;
import com.red.circle.other.inner.endpoint.family.FamilyCreateRulesClient;
import com.red.circle.other.inner.model.cmd.famliy.FamilyCreateRuleCmd;
import com.red.circle.other.inner.model.cmd.famliy.FamilyCreateRuleQryCmd;
import com.red.circle.other.inner.model.dto.famliy.FamilyCreateRuleDTO;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 工会创建要求(满足任意一个，那么用户就可以创建工会) 服务实现类.
 * </p>
 *
 * <AUTHOR>
 * @since 2022-06-30
 */
@Service
@RequiredArgsConstructor
public class FamilyCreateRulesServiceImpl implements FamilyCreateRulesService {

  private final FamilyCreateRulesClient familyCreateRulesClient;


  @Override
  public void save(FamilyCreateRuleCmd param) {
    ResponseAssert.requiredSuccess(familyCreateRulesClient.saveFamilyCreateRules(param));
  }

  @Override
  public PageResult<FamilyCreateRuleDTO> pageData(FamilyCreateRuleQryCmd queryWhere) {
    return ResponseAssert.requiredSuccess(familyCreateRulesClient.pageData(queryWhere));
  }
}
