package com.red.circle.console.adapter.app.family;

import com.red.circle.console.app.service.app.family.FamilyCreateRulesService;
import com.red.circle.framework.dto.PageResult;
import com.red.circle.framework.web.controller.BaseController;
import com.red.circle.other.inner.model.cmd.famliy.FamilyCreateRuleCmd;
import com.red.circle.other.inner.model.cmd.famliy.FamilyCreateRuleQryCmd;
import com.red.circle.other.inner.model.dto.famliy.FamilyCreateRuleDTO;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 工会创建规则表 前端控制器.
 *
 * <AUTHOR>
 * @since 2021-07-20
 */
@RequiredArgsConstructor
@RestController
@RequestMapping("/family/create/rule")
public class FamilyCreateRuleRestController extends BaseController {

  private final FamilyCreateRulesService familyCreateRulesService;

  /**
   * 分页.
   */
  @GetMapping("/page")
  public PageResult<FamilyCreateRuleDTO> pageData(FamilyCreateRuleQryCmd queryWhere) {
    return familyCreateRulesService.pageData(queryWhere);
  }

  /**
   * 添加、更新.
   */
  @PostMapping("/add-or-update")
  public void save(@RequestBody @Validated FamilyCreateRuleCmd param) {
    familyCreateRulesService.save(param);
  }


}
