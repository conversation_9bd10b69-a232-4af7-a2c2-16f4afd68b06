package com.red.circle.console.adapter.app.family;


import com.red.circle.console.app.service.app.family.FamilyLevelConfigService;
import com.red.circle.framework.dto.PageResult;
import com.red.circle.framework.web.controller.BaseController;
import com.red.circle.other.inner.model.cmd.famliy.FamilyLevelConfigCmd;
import com.red.circle.other.inner.model.cmd.famliy.FamilyLevelConfigQryCmd;
import com.red.circle.other.inner.model.dto.famliy.FamilyLevelConfigDTO;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 工会等级配置表 前端控制器.
 *
 * <AUTHOR>
 * @since 2021-07-20
 */
@RequiredArgsConstructor
@RestController
@RequestMapping("/family/level/config")
public class FamilyLevelConfigRestController extends BaseController {

  private final FamilyLevelConfigService familyLevelConfigService;

  /**
   * 分页.
   */
  @GetMapping("/page")
  public PageResult<FamilyLevelConfigDTO> pageData(
      FamilyLevelConfigQryCmd queryWhere) {
    return familyLevelConfigService.pageData(queryWhere);
  }

  /**
   * 添加、更新.
   */
  @PostMapping("/add-or-update")
  public void save(@RequestBody @Validated FamilyLevelConfigCmd param) {
    familyLevelConfigService.saveFamilyLevelConfig(param);
  }

}
