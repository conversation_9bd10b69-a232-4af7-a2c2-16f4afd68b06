package com.red.circle.console.adapter.app.family;

import com.red.circle.console.app.dto.clienobject.family.FamilyBaseInfoCO;
import com.red.circle.console.app.service.app.family.FamilyBaseInfoService;
import com.red.circle.framework.dto.PageResult;
import com.red.circle.framework.web.controller.BaseController;
import com.red.circle.other.inner.model.cmd.famliy.FamilyBaseInfoQryCmd;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 工会列表 前端控制器.
 *
 * <AUTHOR>
 * @since 2021-07-20
 */
@RequiredArgsConstructor
@RestController
@RequestMapping("/family")
public class FamilyBaseInfoRestController extends BaseController {

  private final FamilyBaseInfoService familyBaseInfoService;

  /**
   * 分页.
   */
  @GetMapping("/page")
  public PageResult<FamilyBaseInfoCO> pageData(
      FamilyBaseInfoQryCmd queryWhere) {
    return familyBaseInfoService.pageData(queryWhere);
  }

  /**
   * 解散工会.
   */
  @GetMapping("/del/{familyId}")
  public void delFamily(@PathVariable("familyId") Long familyId) {
    familyBaseInfoService.delFamily(familyId);
  }


}
