package com.red.circle.console.adapter.app.approval;

import com.red.circle.console.app.dto.clienobject.approval.ApprovalFamilyCO;
import com.red.circle.console.app.service.app.approval.ApprovalFamilySettingDataService;
import com.red.circle.console.infra.annotations.OpsOperationReqLog;
import com.red.circle.framework.dto.PageResult;
import com.red.circle.framework.web.controller.BaseController;
import com.red.circle.other.inner.model.cmd.approval.ApprovalFamilyCmd;
import com.red.circle.other.inner.model.cmd.approval.ApprovalProfileDescQryCmd;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 工会资料审批
 *
 * <AUTHOR> on 2021/7/28
 */
@RestController
@RequestMapping("/family/approval")
@AllArgsConstructor
public class ApprovalFamilyRestController extends BaseController {

  private final ApprovalFamilySettingDataService approvalFamilySettingDataService;

  /**
   * 工会资料审批列表分页
   */
  @GetMapping("/page")
  public PageResult<ApprovalFamilyCO> pageFamilyApproval(
      ApprovalProfileDescQryCmd query) {
    return approvalFamilySettingDataService.pageFamilyApproval(query);
  }

  @OpsOperationReqLog("工会资料审批不通过")
  @PostMapping("/not-pass")
  public void notPass(@RequestBody ApprovalFamilyCmd cmd) {
    approvalFamilySettingDataService.notPass(cmd);
  }

}
