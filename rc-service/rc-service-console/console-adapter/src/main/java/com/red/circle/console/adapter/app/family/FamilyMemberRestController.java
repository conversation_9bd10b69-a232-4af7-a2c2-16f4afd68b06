package com.red.circle.console.adapter.app.family;


import com.red.circle.console.app.dto.clienobject.family.FamilyMemberCO;
import com.red.circle.console.app.service.app.family.FamilyMemberService;
import com.red.circle.framework.dto.PageResult;
import com.red.circle.framework.web.controller.BaseController;
import com.red.circle.other.inner.model.cmd.famliy.FamilyMemberQryCmd;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 工会成员列表 前端控制器.
 *
 * <AUTHOR>
 * @since 2021-07-30
 */
@RequiredArgsConstructor
@RestController
@RequestMapping("/family/member")
public class FamilyMemberRestController extends BaseController {

  private final FamilyMemberService familyMemberService;

  /**
   * 分页.
   */
  @GetMapping("/page")
  public PageResult<FamilyMemberCO> pageData(
      FamilyMemberQryCmd queryWhere) {
    return familyMemberService.pageData(queryWhere);
  }

  /**
   * 移除工会成员.
   */
  @GetMapping("/del/{familyId}/{memberId}")
  public void delMember(@PathVariable("familyId") Long familyId,
      @PathVariable("memberId") Long memberId) {
    familyMemberService.delMember(familyId, memberId);
  }

}
