package com.red.circle.console.adapter.app.props;

import com.red.circle.common.business.core.ControllerRedisKeyConstant;
import com.red.circle.console.app.dto.clienobject.props.PropsResourcesOpsCO;
import com.red.circle.console.app.dto.cmd.app.props.PropsSourceRecordSaveOrUpdateCmd;
import com.red.circle.console.app.service.app.props.PropsSourceService;
import com.red.circle.console.infra.annotations.OpsOperationReqLog;
import com.red.circle.framework.core.asserts.ResponseAssert;
import com.red.circle.framework.core.response.ResponseErrorCode;
import com.red.circle.framework.dto.PageResult;
import com.red.circle.framework.web.controller.BaseController;
import com.red.circle.other.inner.model.cmd.material.PropsSourceRecordQryCmd;
import com.red.circle.other.inner.model.dto.material.PropsResourcesDTO;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Caching;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 道具资源记录.
 *
 * <AUTHOR>
 * @since 2021-06-05
 */
@RequiredArgsConstructor
@RestController
@RequestMapping("/props/source/record")
public class PropsSourceRecordRestController extends BaseController {

  private final PropsSourceService propsSourceService;

  /**
   * 道具资源记录.
   */
  @GetMapping("/page")
  public PageResult<PropsResourcesOpsCO> pageOps(
      PropsSourceRecordQryCmd qryCmd) {
    return propsSourceService.pageOps(qryCmd);
  }


  @OpsOperationReqLog(value = "添加-道具资源记录")
  @PostMapping("/add")
  public void add(@RequestBody @Validated PropsSourceRecordSaveOrUpdateCmd cmd) {
    propsSourceService.addOrUpdate(cmd);
  }

  @Caching(evict = {
      @CacheEvict(value = ControllerRedisKeyConstant.PROPS_STORE, key = "#cmd.controllerCacheKeyGold()"),
      @CacheEvict(value = ControllerRedisKeyConstant.PROPS_STORE, key = "#cmd.controllerCacheKeyDiamond()"),
      @CacheEvict(value = ControllerRedisKeyConstant.PROPS_STORE, key = "#cmd.controllerCacheKeyFree()")
  })
  @OpsOperationReqLog(value = "修改-道具资源记录")
  @PostMapping("/update")
  public void update(@RequestBody @Validated PropsSourceRecordSaveOrUpdateCmd cmd) {
    ResponseAssert.notNull(ResponseErrorCode.REQUEST_PARAMETER_ERROR, cmd.getId());
    propsSourceService.addOrUpdate(cmd);
  }


  @OpsOperationReqLog(value = "上下架-道具资源记录")
  @GetMapping("/off/shelf")
  public void offShelf(Long id, Boolean offShelf) {
    propsSourceService.offShelf(id, offShelf);
  }

  /**
   * 获取平台资源列表.
   */
  @GetMapping("/sys-origin/type/list")
  public List<PropsResourcesDTO> listSysOrigin(String sysOrigin, String type) {
    return propsSourceService.listSysOrigin(sysOrigin, type);
  }

  /**
   * 获取资源列表-排除工会资源.
   */
  @GetMapping("/sys-origin/type/list/exclude-family")
  public List<PropsResourcesDTO> listExcludeFamily(String sysOrigin,
      String type) {
    return propsSourceService.listExcludeFamily(sysOrigin, type);
  }

}
