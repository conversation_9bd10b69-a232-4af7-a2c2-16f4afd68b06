package com.red.circle.console.adapter.app.family;

import com.red.circle.console.app.service.app.family.FamilyRewardRuleService;
import com.red.circle.framework.dto.PageResult;
import com.red.circle.framework.web.controller.BaseController;
import com.red.circle.other.inner.model.cmd.famliy.FamilyRewardRuleCmd;
import com.red.circle.other.inner.model.cmd.famliy.FamilyRewardRuleQryCmd;
import com.red.circle.other.inner.model.dto.famliy.FamilyRewardRuleDTO;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 工会奖励规则表 前端控制器.
 *
 * <AUTHOR>
 * @since 2021-07-20
 */
@RequiredArgsConstructor
@RestController
@RequestMapping("/family/reward/rule")
public class FamilyRewardRuleRestController extends BaseController {

  private final FamilyRewardRuleService familyRewardRuleService;

  /**
   * 分页.
   */
  @GetMapping("/page")
  public PageResult<FamilyRewardRuleDTO> pageData(
      FamilyRewardRuleQryCmd queryWhere) {
    return familyRewardRuleService.pageData(queryWhere);
  }

  /**
   * 添加、更新.
   */
  @PostMapping("/add-or-update")
  public void save(@RequestBody @Validated FamilyRewardRuleCmd param) {
    familyRewardRuleService.save(param);
  }


}
