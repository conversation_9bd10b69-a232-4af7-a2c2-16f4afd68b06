package com.red.circle.console.app.service.app.family;

import com.red.circle.console.app.dto.clienobject.family.FamilyBaseInfoCO;
import com.red.circle.framework.dto.PageResult;
import com.red.circle.other.inner.model.cmd.famliy.FamilyBaseInfoQryCmd;

/**
 * <p>
 * 工会客户端 服务类.
 * </p>
 *
 * <AUTHOR>
 * @since 2022-06-30
 */
public interface FamilyBaseInfoService {

  PageResult<FamilyBaseInfoCO> pageData(FamilyBaseInfoQryCmd queryWhere);

  void delFamily(Long familyId);
}
