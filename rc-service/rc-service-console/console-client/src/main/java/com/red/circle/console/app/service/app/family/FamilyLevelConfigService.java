package com.red.circle.console.app.service.app.family;


import com.red.circle.framework.dto.PageResult;
import com.red.circle.other.inner.model.cmd.famliy.FamilyLevelConfigCmd;
import com.red.circle.other.inner.model.cmd.famliy.FamilyLevelConfigQryCmd;
import com.red.circle.other.inner.model.dto.famliy.FamilyLevelConfigDTO;

/**
 * <p>
 * 工会等级配置 服务类.
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-21
 */
public interface FamilyLevelConfigService {


  PageResult<FamilyLevelConfigDTO> pageData(FamilyLevelConfigQryCmd queryWhere);

  void saveFamilyLevelConfig(FamilyLevelConfigCmd cmd);
}
