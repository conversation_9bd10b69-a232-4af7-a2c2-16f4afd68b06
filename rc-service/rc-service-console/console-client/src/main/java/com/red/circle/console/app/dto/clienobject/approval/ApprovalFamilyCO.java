package com.red.circle.console.app.dto.clienobject.approval;

import com.red.circle.other.inner.model.dto.user.UserProfileDTO;
import java.io.Serializable;
import java.sql.Timestamp;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 工会审核
 *
 * <AUTHOR> on 2021/7/28
 */
@Data
@Accessors(chain = true)
public class ApprovalFamilyCO implements Serializable {

  private static final long serialVersionUID = 1L;

  /**
   * 用户id
   */
  private Long userId;

  /**
   * 工会ID
   */
  private Long familyId;

  /**
   * 工会账号
   */
  private Long familyAccount;

  /**
   * 用户基本信息
   */
  private UserProfileDTO userBaseInfo;

  /**
   * 工会头像
   */
  private String familyAvatar;

  /**
   * 工会名称.
   */
  private String familyName;

  /**
   * 工会公告.
   */
  private String familyNotice;

  /**
   * 个人资料描述
   */
  private String profileDesc;

  private String familyWhatapp;
  private String leaderIdCardPhoto;

  /**
   * 修改时间
   */
  private Timestamp updateTime;

}
