package com.red.circle.console.app.service.app.family;

import com.red.circle.console.app.dto.clienobject.family.FamilyMemberCO;
import com.red.circle.framework.dto.PageResult;
import com.red.circle.other.inner.model.cmd.famliy.FamilyMemberQryCmd;

/**
 * <p>
 * 工会成员表 服务类.
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-20
 */
public interface FamilyMemberService {

  PageResult<FamilyMemberCO> pageData(FamilyMemberQryCmd queryWhere);

  void delMember(Long familyId, Long memberId);

}
