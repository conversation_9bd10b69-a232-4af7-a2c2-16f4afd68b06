package com.red.circle.console.app.service.app.family;


import com.red.circle.framework.dto.PageResult;
import com.red.circle.other.inner.model.cmd.famliy.FamilyCreateRuleCmd;
import com.red.circle.other.inner.model.cmd.famliy.FamilyCreateRuleQryCmd;
import com.red.circle.other.inner.model.dto.famliy.FamilyCreateRuleDTO;

/**
 * <p>
 * 工会创建要求(满足任意一个，那么用户就可以创建工会) 服务类.
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-21
 */
public interface FamilyCreateRulesService {

  void save(FamilyCreateRuleCmd param);

  PageResult<FamilyCreateRuleDTO> pageData(FamilyCreateRuleQryCmd queryWhere);

}
