package com.red.circle.console.app.dto.clienobject.family;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.red.circle.other.inner.model.dto.user.UserProfileDTO;
import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.sql.Timestamp;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 工会.
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-20
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class FamilyBaseInfoCO implements Serializable {

  @Serial
  private static final long serialVersionUID = 1L;


  /**
   * 主键标识.
   */
  @JsonSerialize(using = ToStringSerializer.class)
  private Long id;

  /**
   * 来源系统.
   */
  private String sysOrigin;

  /**
   * 工会账号.
   */
  private Long familyAccount;

  /**
   * 工会头像.
   */
  private String familyAvatar;

  /**
   * 工会名称.
   */
  private String familyName;

  /**
   * 工会等级ID.
   */
  private Long familyLevelId;

  /**
   * 工会状态.
   */
  private String familyStatus;

  /**
   * 工会公告.
   */
  private String familyNotice;

  /**
   * 工会成员数量.
   */
  private Long memberCount;

  /**
   * 等级key.
   */
  private String levelKey;

  /**
   * 贡献值
   */
  private BigDecimal familyExp;

  /**
   * 工会族长信息
   */
  private UserProfileDTO userBaseInfo;

  /**
   * 创建时间.
   */
  private Timestamp createTime;

  /**
   * 修改时间.
   */
  private Timestamp updateTime;

  private Long createUser;

  private String familyWhatapp;
  private String leaderIdCardPhoto;
}
