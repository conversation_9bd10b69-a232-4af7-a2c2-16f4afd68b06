package com.red.circle.console.app.dto.clienobject.family;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.red.circle.other.inner.model.dto.user.UserProfileDTO;
import java.io.Serial;
import java.io.Serializable;
import java.sql.Timestamp;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 工会成员.
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-30
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class FamilyMemberCO implements Serializable {

  @Serial
  private static final long serialVersionUID = 1L;


  /**
   * 主键标识.
   */
  @JsonSerialize(using = ToStringSerializer.class)
  private Long id;

  /**
   * 工会ID.
   */
  @JsonSerialize(using = ToStringSerializer.class)
  private Long familyId;

  /**
   * 工会成员userId.
   */
  @JsonSerialize(using = ToStringSerializer.class)
  private Long memberUserId;

  /**
   * 角色.
   */
  private String memberRole;

  /**
   * 用户信息
   */
  private UserProfileDTO userBaseInfo;

  /**
   * 创建时间.
   */
  private Timestamp createTime;

  /**
   * 修改时间.
   */
  private Timestamp updateTime;

}
