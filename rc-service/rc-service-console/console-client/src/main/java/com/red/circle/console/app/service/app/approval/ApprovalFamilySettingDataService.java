package com.red.circle.console.app.service.app.approval;

import com.red.circle.console.app.dto.clienobject.approval.ApprovalFamilyCO;
import com.red.circle.framework.dto.PageResult;
import com.red.circle.other.inner.model.cmd.approval.ApprovalFamilyCmd;
import com.red.circle.other.inner.model.cmd.approval.ApprovalProfileDescQryCmd;

/**
 * 工会资料审批
 * <AUTHOR> on 2024/1/20
 */
public interface ApprovalFamilySettingDataService {

  PageResult<ApprovalFamilyCO> pageFamilyApproval(ApprovalProfileDescQryCmd query);

  void notPass(ApprovalFamilyCmd cmd);
}
