package com.red.circle.console.app.service.app.family;

import com.red.circle.framework.dto.PageResult;
import com.red.circle.other.inner.model.cmd.famliy.FamilyRewardRuleCmd;
import com.red.circle.other.inner.model.cmd.famliy.FamilyRewardRuleQryCmd;
import com.red.circle.other.inner.model.dto.famliy.FamilyRewardRuleDTO;

/**
 * <p>
 * 工会奖励规则 服务类.
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-20
 */
public interface FamilyRewardRuleService {

  void save(FamilyRewardRuleCmd familyRewardRuleParam);

  PageResult<FamilyRewardRuleDTO> pageData(FamilyRewardRuleQryCmd queryWhere);
}
