package com.red.circle;

import com.red.circle.component.redis.RedisAutoConfiguration;
import com.red.circle.framework.cloud.annotation.RedCircleCloudApplication;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.Md5Crypt;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.scheduling.annotation.EnableScheduling;

/**
 * Spring Boot Starter.
 *
 * <AUTHOR> on 2023/5/7
 */
@Slf4j
@MapperScan("com.red.circle.console.infra.database.rds.dao.*")
@EnableScheduling
@EnableDiscoveryClient
@EnableFeignClients
@SpringBootApplication(exclude = RedisAutoConfiguration.class)
public class ConsoleServiceApplication {

  public static void main(String[] args) {
    log.info("Begin to start Spring Boot Application");
    long startTime = System.currentTimeMillis();
    SpringApplication.run(ConsoleServiceApplication.class, args);
    long endTime = System.currentTimeMillis();
    log.info("End starting Spring Boot Application, Time used: " + (endTime - startTime));
  }


//  public static void main(String[] args) {
//    System.out.println(Md5Crypt.apr1Crypt("qddqdd2", "qdd"));
//  }
}
