<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">

  <modelVersion>4.0.0</modelVersion>
  <artifactId>external-infrastructure</artifactId>
  <description>基础层</description>
  <packaging>jar</packaging>

  <dependencies>

    <dependency>
      <groupId>com.red.circle</groupId>
      <artifactId>framework-cloud</artifactId>
    </dependency>

    <dependency>
      <groupId>com.red.circle</groupId>
      <artifactId>component-redis</artifactId>
    </dependency>


    <dependency>
      <groupId>com.red.circle</groupId>
      <artifactId>component-instant-message</artifactId>
    </dependency>

    <dependency>
      <groupId>com.red.circle</groupId>
      <artifactId>component-sms</artifactId>
    </dependency>

<!--    <dependency>-->
<!--      <groupId>com.red.circle</groupId>-->
<!--      <artifactId>component-oss</artifactId>-->
<!--    </dependency>-->

    <dependency>
      <groupId>com.red.circle</groupId>
      <artifactId>component-censor</artifactId>
    </dependency>

    <dependency>
      <groupId>com.red.circle</groupId>
      <artifactId>component-translation</artifactId>
    </dependency>

    <dependency>
      <groupId>com.red.circle</groupId>
      <artifactId>component-push</artifactId>
    </dependency>

  </dependencies>

  <parent>
    <artifactId>rc-service-external</artifactId>
    <groupId>com.red.circle</groupId>
    <version>${revision}</version>
    <relativePath>./../pom.xml</relativePath>
  </parent>
</project>
