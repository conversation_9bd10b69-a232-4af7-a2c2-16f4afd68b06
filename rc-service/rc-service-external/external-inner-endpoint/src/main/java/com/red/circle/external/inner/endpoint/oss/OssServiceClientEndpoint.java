package com.red.circle.external.inner.endpoint.oss;

import com.red.circle.external.inner.endpoint.oss.api.OssServiceClientApi;
import com.red.circle.external.inner.service.oss.CosServiceClientService;
import com.red.circle.framework.dto.ResultResponse;
import lombok.RequiredArgsConstructor;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * oss 服务.
 *
 * <AUTHOR> on 2023/10/15
 */
@Validated
@RestController
@RequestMapping(value = OssServiceClientApi.API_PREFIX, produces = MediaType.APPLICATION_JSON_VALUE)
@RequiredArgsConstructor
public class OssServiceClientEndpoint implements OssServiceClientApi {

    private final CosServiceClientService cosServiceClientService;

    //更换为腾讯云服务
    @Override
    public ResultResponse<Object> sts() {
        return ResultResponse.success(cosServiceClientService.sts());
    }

  @Override
  public ResultResponse<Void> remove(String url) {
    return null;
  }

  @Override
  public ResultResponse<String> getAccessUrl(String key) {
    return null;
  }

  @Override
  public ResultResponse<String> getVideoCover(String key) {
    return null;
  }

  @Override
  public ResultResponse<Void> processFileSaveAs(String source, String target, String styleType) {
    return null;
  }

  @Override
  public ResultResponse<String> processImgSaveAsCompressZoom(String source, String target, Integer height) {
    return null;
  }

  @Override
    public ResultResponse<String> processImgSaveAsCompressZoom(String source, Integer height) {
        // 调用服务层方法处理图片压缩
        return ResultResponse.success(source);
    }
}
