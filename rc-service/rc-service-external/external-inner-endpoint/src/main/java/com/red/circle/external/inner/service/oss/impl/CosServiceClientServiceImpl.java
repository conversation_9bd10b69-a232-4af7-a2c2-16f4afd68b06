package com.red.circle.external.inner.service.oss.impl;

import com.qcloud.cos.COSClient;
import com.qcloud.cos.ClientConfig;
import com.qcloud.cos.auth.BasicCOSCredentials;
import com.qcloud.cos.auth.BasicSessionCredentials;
import com.qcloud.cos.auth.COSCredentials;
import com.qcloud.cos.model.ObjectMetadata;
import com.qcloud.cos.model.PutObjectRequest;
import com.qcloud.cos.model.PutObjectResult;
import com.qcloud.cos.region.Region;
import com.red.circle.external.inner.service.oss.CosServiceClientService;
import com.tencent.cloud.CosStsClient;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import com.tencent.cloud.Response;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.util.HashMap;
import java.util.TreeMap;

/**
 * oss 服务api.
 *
 * <AUTHOR> on 2023/10/15
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class CosServiceClientServiceImpl implements CosServiceClientService {

    // 使用 ${} 占位符从配置文件中读取属性值
    @Value("${red-circle.oss.ten-cos.region}")
    private String region;
    @Value("${red-circle.oss.ten-cos.bucketName}")
    private String getBucketName;
    @Value("${red-circle.oss.ten-cos.access-key-id}")
    private String accessKeyId;
    @Value("${red-circle.oss.ten-cos.access-key-secret}")
    private String accessKeySecret;


    @Override
    public Object sts() {

        /**
         * 基本的临时密钥申请示例，适合对一个桶内的一批对象路径，统一授予一批操作权限
         */
        TreeMap<String, Object> config = new TreeMap<String, Object>();

        try {

            //打印一下参数
            log.info("region:{}", region);
            log.info("getBucketName:{}", getBucketName);
            log.info("accessKeyId:{}", accessKeyId);
            log.info("accessKeySecret:{}", accessKeySecret);

            // 云 api 密钥 SecretId
            config.put("secretId", accessKeyId);
            // 云 api 密钥 SecretKey
            config.put("secretKey", accessKeySecret);


            // 设置域名,可通过此方式设置内网域名
            //config.put("host", "sts.internal.tencentcloudapi.com");

            // 临时密钥有效时长，单位是秒
            config.put("durationSeconds", 1800);

            // 换成你的 bucket
//            config.put("bucket", "dev-yuyin-1352144764");
            config.put("bucket", getBucketName);
            // 换成 bucket 所在地区
//            config.put("region", "ap-beijing");
            config.put("region", region);

            // 可以通过 allowPrefixes 指定前缀数组, 例子： a.jpg 或者 a/* 或者 * (使用通配符*存在重大安全风险, 请谨慎评估使用)
            config.put("allowPrefixes", new String[]{
                    "/console/*",
                    "/app/*",

            });

            // 密钥的权限列表。简单上传和分片需要以下的权限，其他权限列表请看 https://cloud.tencent.com/document/product/436/31923
            String[] allowActions = new String[]{
                    // 简单上传
                    "name/cos:PutObject",
                    "name/cos:PostObject",
                    // 分片上传
                    "name/cos:InitiateMultipartUpload",
                    "name/cos:ListMultipartUploads",
                    "name/cos:ListParts",
                    "name/cos:UploadPart",
                    "name/cos:CompleteMultipartUpload"
            };
            config.put("allowActions", allowActions);

            Response response = CosStsClient.getCredential(config);
            System.out.println(response.credentials.tmpSecretId);
            System.out.println(response.credentials.tmpSecretKey);
            System.out.println(response.credentials.sessionToken);
            return new HashMap<>() {{
                put("accessKeyId", response.credentials.tmpSecretId);
                put("accessKeySecret", response.credentials.tmpSecretKey);
                put("securityToken", response.credentials.sessionToken);

            }};
        } catch (Exception e) {
            e.printStackTrace();
            throw new IllegalArgumentException("no valid secret !");
        }

    }

    @Override
    public String upload(InputStream inputStream, String fileName) {


        // SECRETID 和 SECRETKEY 请登录访问管理控制台 https://console.cloud.tencent.com/cam/capi 进行查看和管理
        String secretId = accessKeyId;//用户的 SecretId，建议使用子账号密钥，授权遵循最小权限指引，降低使用风险。子账号密钥获取可参见 https://cloud.tencent.com/document/product/598/37140
        String secretKey = accessKeySecret;//用户的 SecretKey，建议使用子账号密钥，授权遵循最小权限指引，降低使用风险。子账号密钥获取可参见 https://cloud.tencent.com/document/product/598/37140
        COSCredentials cred = new BasicCOSCredentials(secretId, secretKey);
// 2 设置 bucket 的地域, COS 地域的简称请参阅 https://cloud.tencent.com/document/product/436/6224
// clientConfig 中包含了设置 region, https(默认 http), 超时, 代理等 set 方法, 使用可参阅源码或者常见问题 Java SDK 部分
        Region region = new Region("ap-beijing");
        ClientConfig clientConfig = new ClientConfig(region);
// 3 生成 cos 客户端
        COSClient cosClient = new COSClient(cred, clientConfig);
        // 创建 ObjectMetadata 对象，设置文件内容长度
        ObjectMetadata objectMetadata = new ObjectMetadata();
        try {
            objectMetadata.setContentLength(inputStream.available());
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
// 指定文件将要存放的存储桶
        String bucketName = "dev-yuyin-1352144764";
// 指定文件上传到 COS 上的路径，即对象键。例如对象键为 folder/picture.jpg，则表示将文件 picture.jpg 上传到 folder 路径下
        PutObjectRequest putObjectRequest = new PutObjectRequest(bucketName, fileName, inputStream, objectMetadata);
        PutObjectResult putObjectResult = cosClient.putObject(putObjectRequest);
        System.out.println(putObjectResult);
        // 调用 COS 接口之前必须保证本进程存在一个 COSClient 实例，如果没有则创建

//        System.out.println(cosClient.getObjectUrl(bucketName, fileName));
        return cosClient.getObjectUrl(bucketName, fileName).toString();
    }

//    @Override
//    public void remove(String url) {
//        ossService.remove(url);
//    }
//
//    @Override
//    public String getAccessUrl(String key) {
//        return ossService.getAccessUrl(key);
//    }
//
//    @Override
//    public String getVideoCover(String key) {
//        return ossService.getVideoCover(key);
//    }
//
//    @Override
//    public void processFileSaveAs(String source, String target, String styleType) {
//        ossService.processFileSaveAs(source, target, styleType);
//    }
//
//    @Override
//    public String processImgSaveAsCompressZoom(String source, String target, int height) {
//        return ossService.processImgSaveAsCompressZoom(source, target, height);
//    }
//
//    @Override
//    public String processImgSaveAsCompressZoom(String source, int height) {
//        return ossService.processImgSaveAsCompressZoom(source, height);
//    }

}
