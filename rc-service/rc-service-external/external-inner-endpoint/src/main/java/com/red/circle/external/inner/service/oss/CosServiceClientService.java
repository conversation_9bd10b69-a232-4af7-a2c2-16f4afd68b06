package com.red.circle.external.inner.service.oss;

import java.io.InputStream;

/**
 * oss 服务api.
 *
 * <AUTHOR> on 2023/10/15
 */
public interface CosServiceClientService {

  /**
   * sts临时令牌.
   */
  Object sts();

  String upload(InputStream inputStream, String fileName);

//  /**
//   * 移除文件.
//   */
//  void remove(String url);
//
//  /**
//   * 获取访问连接.
//   */
//  String getAccessUrl(String key);
//
//  /**
//   * 获取视频封面图.
//   *
//   * @param key 文件key
//   * @return 全链接
//   */
//  String getVideoCover(String key);
//
//
//  /**
//   * 图片处理后另存为持久化.
//   *
//   * @param source    原图地址
//   * @param target    处理后转存地址
//   * @param styleType 处理类型格式
//   */
//  void processFileSaveAs(String source, String target, String styleType);
//
//  /**
//   * 图片处理压缩.
//   *
//   * @return 图片地址
//   */
//  String processImgSaveAsCompressZoom(final String source, String target, int height);
//
//  /**
//   * 图片处理压缩.
//   *
//   * @return 图片地址
//   */
//  String processImgSaveAsCompressZoom(final String source, int height);

}
