---
apiVersion: apps/v1
kind: Deployment
metadata:
  annotations: {}
  labels:
    app: external
  name: external
  namespace: local
  resourceVersion: '90569852'
spec:
  progressDeadlineSeconds: 600
  replicas: 1
  revisionHistoryLimit: 3
  selector:
    matchLabels:
      app: external
  strategy:
    rollingUpdate:
      maxSurge: 25%
      maxUnavailable: 25%
    type: RollingUpdate
  template:
    metadata:
      annotations:
        kubectl.kubernetes.io/restartedAt: '2025-06-10T17:51:28+08:00'
      creationTimestamp: null
      labels:
        app: external
    spec:
      containers:
        - env:
            - name: SERVER_PORT
              value: '1200'
            - name: SERVER_PROFILE_ACTIVE
              value: dev
            - name: TRACE_PROTOCOL
              value: grpc
            - name: TRACE_ENDPOINT
              value: 'https://asaxxx-dev.ap-southeast-1.log.aliyuncs.com:10010'
            - name: TRACE_COMPRESSION
              value: gzip
            - name: TRACE_HEADERS
              value: >-
                x-sls-otel-project=aswat-app-trace-dev,x-sls-otel-instance-id=aswat-develop,x-sls-otel-ak-id=LTAIxxxxKrE5x,x-sls-otel-ak-secret=3kKQWtvxxxxxm6SzmHz
            - name: TRACE_HOST_NAME
              valueFrom:
                fieldRef:
                  apiVersion: v1
                  fieldPath: status.podIP
            - name: NACOS_HOST
              valueFrom:
                configMapKeyRef:
                  key: nacos.host
                  name: nacos
            - name: NACOS_PORT
              valueFrom:
                configMapKeyRef:
                  key: nacos.port
                  name: nacos
            - name: NACOS_USERNAME
              valueFrom:
                configMapKeyRef:
                  key: nacos.username
                  name: nacos
            - name: NACOS_PASSWORD
              valueFrom:
                configMapKeyRef:
                  key: nacos.password
                  name: nacos
          image: >-
            794038239327.dkr.ecr.ap-southeast-1.amazonaws.com/halar-dev:external-20250603v084201
          imagePullPolicy: IfNotPresent
          lifecycle:
            preStop:
              exec:
                command:
                  - /bin/bash
                  - '-c'
                  - >-
                    curl -X POST
                    http://localhost:1200/actuator/nacosservicederegister?key=CxILm9hA1b9hF3Hl
                    --header 'Content-Type:
                    application/vnd.spring-boot.actuator.v2+json;charset=UTF-8'
                  - sleep 30
          livenessProbe:
            failureThreshold: 10
            initialDelaySeconds: 180
            periodSeconds: 3
            successThreshold: 1
            tcpSocket:
              port: 1200
            timeoutSeconds: 30
          name: external
          ports:
            - containerPort: 1200
              protocol: TCP
          readinessProbe:
            failureThreshold: 10
            initialDelaySeconds: 60
            periodSeconds: 3
            successThreshold: 1
            tcpSocket:
              port: 1200
            timeoutSeconds: 3
          resources:
            limits:
              memory: 1536Mi
            requests:
              memory: 500Mi
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
      dnsPolicy: ClusterFirst
      imagePullSecrets:
        - name: aliyun-secret
      restartPolicy: Always
      schedulerName: default-scheduler
      securityContext: {}
      terminationGracePeriodSeconds: 120
status:
  availableReplicas: 1
  conditions:
    - lastTransitionTime: '2025-05-10T07:38:34Z'
      lastUpdateTime: '2025-05-10T07:38:34Z'
      message: Deployment has minimum availability.
      reason: MinimumReplicasAvailable
      status: 'True'
      type: Available
    - lastTransitionTime: '2024-10-08T08:19:31Z'
      lastUpdateTime: '2025-06-10T09:52:29Z'
      message: ReplicaSet "external-54f94c4588" has successfully progressed.
      reason: NewReplicaSetAvailable
      status: 'True'
      type: Progressing
  observedGeneration: 36
  readyReplicas: 1
  replicas: 1
  updatedReplicas: 1

---
apiVersion: v1
kind: Service
metadata:
  annotations: {}
  name: external
  namespace: local
  resourceVersion: '22869407'
spec:
  clusterIP: *************
  clusterIPs:
    - *************
  internalTrafficPolicy: Cluster
  ipFamilies:
    - IPv4
  ipFamilyPolicy: SingleStack
  ports:
    - port: 80
      protocol: TCP
      targetPort: 1200
  selector:
    app: external
  sessionAffinity: None
  type: ClusterIP
status:
  loadBalancer: {}

