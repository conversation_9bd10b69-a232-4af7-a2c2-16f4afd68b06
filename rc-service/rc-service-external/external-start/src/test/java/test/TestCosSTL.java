//package test;
//
//import com.qcloud.cos.COSClient;
//import com.qcloud.cos.ClientConfig;
//import com.qcloud.cos.auth.BasicSessionCredentials;
//import com.qcloud.cos.model.PutObjectRequest;
//import com.qcloud.cos.model.PutObjectResult;
//import com.qcloud.cos.region.Region;
//import org.junit.Test;
//
//import java.io.File;
//
//public class TestCosSTL {
//
//    /**
//     * 基本的临时密钥申请示例，适合对一个桶内的一批对象路径，统一授予一批操作权限
//     */
////    @Test
////    public void testGetCredential() {
////        TreeMap<String, Object> config = new TreeMap<String, Object>();
////
////        try {
////
////            // 云 api 密钥 SecretId
////            config.put("secretId", "AKIDCF3cuvzrTpvmNhlY4JHzRdaGgrlQpxpR");
////            // 云 api 密钥 SecretKey
////            config.put("secretKey","OWNxOZU8VcWAYgY9TXKq54nHUR7XgDWa");
////
////
////
////            // 设置域名,可通过此方式设置内网域名
////            //config.put("host", "sts.internal.tencentcloudapi.com");
////
////            // 临时密钥有效时长，单位是秒
////            config.put("durationSeconds", 1800);
////
////            // 换成你的 bucket
////            config.put("bucket", "dev-yuyin-1352144764");
////            // 换成 bucket 所在地区
////            config.put("region", "ap-beijing");
////
////            // 可以通过 allowPrefixes 指定前缀数组, 例子： a.jpg 或者 a/* 或者 * (使用通配符*存在重大安全风险, 请谨慎评估使用)
////            config.put("allowPrefixes", new String[] {
////                    "exampleobject",
////                    "exampleobject2"
////            });
////
////            // 密钥的权限列表。简单上传和分片需要以下的权限，其他权限列表请看 https://cloud.tencent.com/document/product/436/31923
////            String[] allowActions = new String[] {
////                    // 简单上传
////                    "name/cos:PutObject",
////                    "name/cos:PostObject",
////                    // 分片上传
////                    "name/cos:InitiateMultipartUpload",
////                    "name/cos:ListMultipartUploads",
////                    "name/cos:ListParts",
////                    "name/cos:UploadPart",
////                    "name/cos:CompleteMultipartUpload"
////            };
////            config.put("allowActions", allowActions);
////
////            Response response = CosStsClient.getCredential(config);
////            System.out.println(response.credentials.tmpSecretId);
////            System.out.println(response.credentials.tmpSecretKey);
////            System.out.println(response.credentials.sessionToken);
////        } catch (Exception e) {
////            e.printStackTrace();
////            throw new IllegalArgumentException("no valid secret !");
////        }
////    }
//
//    @Test
//    public void testUpload() {
//        // 1 传入获取到的临时密钥 (tmpSecretId, tmpSecretKey, sessionToken)
//        String tmpSecretId = "AKIDJOwGASDWEvbVeZVTKCp7JoM2Ft9HC_UOsyQs8GJguy95JEd4EvRHDa8PxgNr7Wva";
//        String tmpSecretKey = "MIWuJFryBpbMAqDvcqJvqrwXbX+hf+5WURRAcJOuTjA=";
//        String sessionToken = "1Mbbez44edUSqg5Yt4yDj3rVlqoUQela51e07558af348caa7084c4f6c9006f8dgTvl19EZCkopEDaqkxprADWbeya0S9ntWze441-NcBekTTpEUgiqSpYRE-qqitW7EiNzbK7xl4zDBbYWismgRI-3izguNHW_KDraK5ZilNDEF-lF0sEqWo738Joi-f5vaSFLYhf3ebKKE1QgHl2jkKWw4z5T3T3PNPAhaLX8YYzsc9YGrCtkv7BXH2pCRW95AwL1NoX2mS0yYY83JiyZECzV5LV-Rutb-TcICTxg-nV0MFCyjoZacysYCwuFkhRMAPx494IKpqJROhqnCkKrDiHqCPMWgqTPVRCIYhsNwO3zDH9Yf4szKPzBxV98NHG7JsZQRvKUu2RqqMhJw2gZL_tUebwSAgrD2H-YftpgK7MUNTRC9BFxK7S8i3F8JQA9rRlKGzpJnepYlZRn45ZJRSw3re48cw6EFawIAzmrImUxv9Id_coDrSQCY4pI6Ks9Yo49rBbPO-wTpenEF0J4lM4Ub8EZnhAgaPISVFWXWn9jEfroY8mbZro1jjMcio1F8X6FFlz7ZjcjQtCdAKindvgNrPcrwslntICfV1gRoSDvRv3lgf7WHGZtGoSvaNK4pEbLL0TJVwJ6DlGW_W-jsQ";
//        BasicSessionCredentials cred = new BasicSessionCredentials(tmpSecretId, tmpSecretKey, sessionToken);
//// 2 设置 bucket 的地域, COS 地域的简称请参阅 https://cloud.tencent.com/document/product/436/6224
//// clientConfig 中包含了设置 region, https(默认 http), 超时, 代理等 set 方法, 使用可参阅源码或者常见问题 Java SDK 部分
//        Region region = new Region("ap-beijing");
//        ClientConfig clientConfig = new ClientConfig(region);
//// 3 生成 cos 客户端
//        COSClient cosClient = new COSClient(cred, clientConfig);
//
//        // 指定要上传的文件
//        File localFile = new File("/Users/<USER>/new-work/qdd/android/app/src/main/res/mipmap-xxxhdpi/ic_launcher.png");
//// 指定文件将要存放的存储桶
//        String bucketName = "dev-yuyin-1352144764";
//// 指定文件上传到 COS 上的路径，即对象键。例如对象键为 folder/picture.jpg，则表示将文件 picture.jpg 上传到 folder 路径下
//        String key = "console/picture.jpg";
//        PutObjectRequest putObjectRequest = new PutObjectRequest(bucketName, key, localFile);
//        PutObjectResult putObjectResult = cosClient.putObject(putObjectRequest);
//        System.out.println(putObjectResult);
//
//    }
//
//}
