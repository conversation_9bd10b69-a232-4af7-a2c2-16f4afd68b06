//package test;
//
//import com.qcloud.cos.COSClient;
//import com.qcloud.cos.ClientConfig;
//import com.qcloud.cos.auth.BasicSessionCredentials;
//import com.qcloud.cos.model.PutObjectRequest;
//import com.qcloud.cos.model.PutObjectResult;
//import com.red.circle.ExternalServiceApplication;
//import com.red.circle.component.oss.aliyun.props.AliYunOssEndpointProperties;
//import com.tencent.cloud.CosStsClient;
//import com.tencent.cloud.Response;
//import
//        com. qcloud. cos. region. Region;
//import org.junit.Test;
//import org.junit.runner.RunWith;
//import org.springframework.beans.factory.annotation.Value;
////import org.springframework.boot.test.context.SpringBootTest;
////import org.springframework.test.context.junit4.SpringRunner;
////import org.springframework.web.context.annotation.ApplicationScope;
//
//import java.io.File;
//import java.io.FileInputStream;
//import java.util.TreeMap;
////@RunWith(SpringRunner.class)
////@SpringBootTest(classes = ExternalServiceApplication.class)
//public class TestJava {
//
////    @Value("red-circle.oss.aliYun")
//    private  AliYunOssEndpointProperties stsProperties ;
//
//    /**
//     * 基本的临时密钥申请示例，适合对一个桶内的一批对象路径，统一授予一批操作权限
//     */
//    @Test
//    public void testGetCredential() {
//
//
//        TreeMap<String, Object> config = new TreeMap<String, Object>();
//
//        try {
//
//            // 云 api 密钥 SecretId
//            config.put("secretId", stsProperties.getAccessKeyId());
////            config.put("secretId", "AKIDCF3cuvzrTpvmNhlY4JHzRdaGgrlQpxpR");
//            // 云 api 密钥 SecretKey
//            config.put("secretKey", stsProperties.getAccessKeySecret());
////            config.put("secretKey","OWNxOZU8VcWAYgY9TXKq54nHUR7XgDWa");
//
//
//
//            // 设置域名,可通过此方式设置内网域名
//            //config.put("host", "sts.internal.tencentcloudapi.com");
//
//            // 临时密钥有效时长，单位是秒
//            config.put("durationSeconds", 1800);
//
//            // 换成你的 bucket
////            config.put("bucket", "dev-yuyin-1352144764");
//            config.put("bucket", stsProperties.getBucketName());
//            // 换成 bucket 所在地区
////            config.put("region", "ap-beijing");
//            config.put("region", stsProperties.getEndpoint());
//
//            // 可以通过 allowPrefixes 指定前缀数组, 例子： a.jpg 或者 a/* 或者 * (使用通配符*存在重大安全风险, 请谨慎评估使用)
//            config.put("allowPrefixes", new String[] {
//                    "/console/*",
//
//            });
//
//            // 密钥的权限列表。简单上传和分片需要以下的权限，其他权限列表请看 https://cloud.tencent.com/document/product/436/31923
//            String[] allowActions = new String[] {
//                    // 简单上传
//                    "name/cos:PutObject",
//                    "name/cos:PostObject",
//                    // 分片上传
//                    "name/cos:InitiateMultipartUpload",
//                    "name/cos:ListMultipartUploads",
//                    "name/cos:ListParts",
//                    "name/cos:UploadPart",
//                    "name/cos:CompleteMultipartUpload"
//            };
//            config.put("allowActions", allowActions);
//
//            Response response = CosStsClient.getCredential(config);
//            System.out.println(response.credentials.tmpSecretId);
//            System.out.println(response.credentials.tmpSecretKey);
//            System.out.println(response.credentials.sessionToken);
//        } catch (Exception e) {
//            e.printStackTrace();
//            throw new IllegalArgumentException("no valid secret !");
//        }
//    }
//
//
//
//}
