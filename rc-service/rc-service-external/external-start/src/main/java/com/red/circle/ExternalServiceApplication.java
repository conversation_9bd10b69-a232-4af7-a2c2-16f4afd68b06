package com.red.circle;


import com.red.circle.component.redis.RedisAutoConfiguration;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.scheduling.annotation.EnableScheduling;

/**
 * Spring Boot Starter.
 *
 * <AUTHOR> on 2023/5/BadgeGroup7
 */
@Slf4j
@EnableScheduling
@EnableDiscoveryClient
@EnableFeignClients
@SpringBootApplication(exclude = RedisAutoConfiguration.class)
public class ExternalServiceApplication {

  public static void main(String[] args) {
    log.info("Begin to start Spring Boot Application");
    long startTime = System.currentTimeMillis();
    SpringApplication.run(ExternalServiceApplication.class, args);
    long endTime = System.currentTimeMillis();
    log.info("End starting Spring Boot Application, Time used: " + (endTime - startTime));
  }

//  public static void main(String[] args) {
//    // 1 传入获取到的临时密钥 (tmpSecretId, tmpSecretKey, sessionToken)
//    String tmpSecretId = "SECRETID";
//    String tmpSecretKey = "SECRETKEY";
//    String sessionToken = "TOKEN";
//    BasicSessionCredentials cred = new BasicSessionCredentials(tmpSecretId, tmpSecretKey, sessionToken);
//// 2 设置 bucket 的地域
//// clientConfig 中包含了设置 region, https(默认 http), 超时, 代理等 set 方法, 使用可参见源码或者常见问题 Java SDK 部分
//    Region region =  new Region("ap-beijing"); //COS_REGION 参数：配置成存储桶 bucket 的实际地域，例如 ap-beijing，更多 COS 地域的简称请参见 https://cloud.tencent.com/document/product/436/6224
//    ClientConfig clientConfig = new ClientConfig(region);
//// 3 生成 cos 客户端
//    COSClient cosClient = new COSClient(cred, clientConfig);
//  }

}
