<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">

  <modelVersion>4.0.0</modelVersion>
  <artifactId>external-start</artifactId>
  <packaging>jar</packaging>

  <dependencies>
    <dependency>
      <groupId>com.red.circle</groupId>
      <artifactId>external-adapter</artifactId>
    </dependency>

    <dependency>
      <groupId>com.red.circle</groupId>
      <artifactId>external-inner-endpoint</artifactId>
      <exclusions>
        <exclusion>
          <groupId>com.red.circle</groupId>
          <artifactId>component-oss</artifactId>
        </exclusion>
      </exclusions>
    </dependency>
<!--      <dependency>-->
<!--          <groupId>com.qcloud</groupId>-->
<!--          <artifactId>cos_api</artifactId>-->
<!--          <version>5.6.227</version>-->
<!--          <scope>compile</scope>-->
<!--      </dependency>-->
<!--    <dependency>-->
<!--      <groupId>com.qcloud</groupId>-->
<!--      <artifactId>cos-sts_api</artifactId>-->
<!--      <version>3.1.0</version>-->
<!--    </dependency>-->
<!--    <dependency>-->
<!--        <groupId>junit</groupId>-->
<!--        <artifactId>junit</artifactId>-->
<!--        <version>4.13.2</version>-->
<!--        <scope>test</scope>-->
<!--    </dependency>-->
<!--    <dependency>-->
<!--        <groupId>org.springframework.boot</groupId>-->
<!--        <artifactId>spring-boot-starter-test</artifactId>-->
<!--        <scope>test</scope>-->
<!--    </dependency>-->
  </dependencies>

  <build>
    <finalName>${parent.artifactId}-${revision}</finalName>
    <plugins>
      <plugin>
        <artifactId>spring-boot-maven-plugin</artifactId>
        <groupId>org.springframework.boot</groupId>
        <executions>
          <execution>
            <goals>
              <goal>repackage</goal>
            </goals>
          </execution>
        </executions>
      </plugin>
    </plugins>
  </build>

  <parent>
    <artifactId>rc-service-external</artifactId>
    <groupId>com.red.circle</groupId>
    <version>${revision}</version>
    <relativePath>./../pom.xml</relativePath>
  </parent>

</project>
