package com.red.circle.external.adapter.app.oss;//package com.red.circle.external.adapter.app.oss;

import com.red.circle.external.inner.service.oss.CosServiceClientService;
import com.red.circle.external.response.OssErrorCode;
import com.red.circle.external.utils.FileUtils;
import com.red.circle.framework.dto.ResultResponse;
import com.red.circle.framework.web.annotation.IgnoreResultResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.UUID;

/**
 * 阿里云,OSS存储.
 *
 * <AUTHOR> on 2020/9/7
 * @eo.api-type http
 * @eo.groupName 第三方服务.OSS存储
 * @eo.path /external/oss
 */
@Slf4j
@RequiredArgsConstructor
@RestController
@RequestMapping(value = "/oss", produces = MediaType.APPLICATION_JSON_VALUE)
public class CosRestController {

//  private final AliYunStsService aliYunStsService;
  private final CosServiceClientService ossService;

  /**
   * sts临时令牌.
   *
   * @eo.name sts临时令牌.
   * @eo.url /sts
   * @eo.method get
   * @eo.request-type formdata
   */
  @IgnoreResultResponse
  @GetMapping("/sts")
  public Object sts() {
    return ossService.sts();
  }

  /**
   * 上传图片.
   *
   * @eo.name 上传图片.
   * @eo.url /upload
   * @eo.method Post
   */
  @IgnoreResultResponse
  @PostMapping("/upload")
  public ResultResponse<String> upload(@RequestPart("file") MultipartFile file) {
    try {
      if (file.isEmpty()) {
        return ResultResponse.failure(OssErrorCode.FILE_IS_NULL);
      }
      if (file.getSize() > 10 * 1024 * 1024) {
        return ResultResponse.failure(OssErrorCode.FILE_SIZE_GREATER_THAN_10M);
      }
      String name = file.getOriginalFilename().toLowerCase();
      if(!FileUtils.fileContentType(name)) {
        return ResultResponse.failure(OssErrorCode.FILE_TYPE_NOT_SUPPORTED);
      }
      log.warn(file.getOriginalFilename());
      log.warn(file.getName());
      log.warn(file.getOriginalFilename());
      String fileName = "app/avatar/" +UUID.randomUUID() + FileUtils.getSuffix(file.getOriginalFilename());;
      ;
      return ResultResponse.success(ossService.upload(file.getInputStream(), fileName));
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      return ResultResponse.failure(OssErrorCode.NETWORK_ANOMALY);
    }
  }



}
