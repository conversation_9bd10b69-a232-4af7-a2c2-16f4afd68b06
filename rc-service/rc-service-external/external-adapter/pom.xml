<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">

  <modelVersion>4.0.0</modelVersion>
  <artifactId>external-adapter</artifactId>
  <description>适配层，对外提供服务</description>
  <packaging>jar</packaging>

  <dependencies>
    <dependency>
      <groupId>com.red.circle</groupId>
      <artifactId>external-application</artifactId>
    </dependency>
    <dependency>
      <groupId>com.red.circle</groupId>
      <artifactId>external-inner-endpoint</artifactId>
    </dependency>
  </dependencies>

  <parent>
    <artifactId>rc-service-external</artifactId>
    <groupId>com.red.circle</groupId>
    <version>${revision}</version>
    <relativePath>./../pom.xml</relativePath>
  </parent>

</project>
