package com.red.circle.wallet.endpoint.family;

import com.red.circle.common.business.dto.cmd.app.AppExtCommand;
import com.red.circle.framework.core.response.ResultResponse;
import com.red.circle.framework.dto.PageResult;
import com.red.circle.wallet.app.dto.clientobject.family.FamilyDiamondRunningWaterCO;
import com.red.circle.wallet.app.dto.clientobject.family.FamilyGoldRunningWaterCO;
import com.red.circle.wallet.app.dto.clientobject.family.FamilyWalletBalanceCO;
import com.red.circle.wallet.app.dto.cmd.family.FamilyDiamondExchangeCmd;
import com.red.circle.wallet.app.dto.cmd.family.FamilyRunningWaterQryCmd;
import com.red.circle.wallet.app.service.family.FamilyWalletAppService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import java.math.BigDecimal;
import javax.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 工会钱包控制器.
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/family/wallet")
@Tag(name = "工会钱包", description = "工会钱包相关接口")
public class FamilyWalletController {

  private final FamilyWalletAppService familyWalletAppService;

  @GetMapping("/balance")
  @Operation(summary = "获取工会钱包余额", description = "获取当前用户所属工会的钻石和金币余额")
  public ResultResponse<FamilyWalletBalanceCO> getFamilyWalletBalance(AppExtCommand cmd) {
    return ResultResponse.success(familyWalletAppService.getFamilyWalletBalance(cmd));
  }

  @PostMapping("/diamond/exchange")
  @Operation(summary = "钻石兑换金币", description = "工会长将工会钻石兑换为金币")
  public ResultResponse<BigDecimal> exchangeDiamondToGold(@Valid @RequestBody FamilyDiamondExchangeCmd cmd) {
    return ResultResponse.success(familyWalletAppService.exchangeDiamondToGold(cmd));
  }

  @GetMapping("/diamond/running-water")
  @Operation(summary = "查询工会钻石流水", description = "分页查询工会钻石流水记录")
  public ResultResponse<PageResult<FamilyDiamondRunningWaterCO>> pageFamilyDiamondRunningWater(
      FamilyRunningWaterQryCmd cmd) {
    return ResultResponse.success(familyWalletAppService.pageFamilyDiamondRunningWater(cmd));
  }

  @GetMapping("/gold/running-water")
  @Operation(summary = "查询工会金币流水", description = "分页查询工会金币流水记录")
  public ResultResponse<PageResult<FamilyGoldRunningWaterCO>> pageFamilyGoldRunningWater(
      FamilyRunningWaterQryCmd cmd) {
    return ResultResponse.success(familyWalletAppService.pageFamilyGoldRunningWater(cmd));
  }

}
