package com.red.circle.wallet.app.service.family;

import java.math.BigDecimal;

/**
 * 工会钱包服务.
 *
 * <AUTHOR>
 */
public interface FamilyWalletService {

  /**
   * 处理成员收礼物的钻石分配.
   *
   * @param userId 用户ID
   * @param familyId 工会ID
   * @param sysOrigin 系统来源
   * @param giftGoldValue 礼物金币价值(分)
   */
  void processGiftDiamondDistribution(Long userId, Long familyId, String sysOrigin, Long giftGoldValue);

  /**
   * 工会钻石兑换金币.
   *
   * @param familyId 工会ID
   * @param operatorUserId 操作用户ID
   * @param sysOrigin 系统来源
   * @param diamondAmount 钻石数量
   * @return 兑换得到的金币数量(分)
   */
  Long exchangeDiamondToGold(Long familyId, Long operatorUserId, String sysOrigin, BigDecimal diamondAmount);

  /**
   * 获取工会钻石余额.
   *
   * @param familyId 工会ID
   * @param sysOrigin 系统来源
   * @return 钻石余额
   */
  BigDecimal getFamilyDiamondBalance(Long familyId, String sysOrigin);

  /**
   * 获取工会金币余额.
   *
   * @param familyId 工会ID
   * @param sysOrigin 系统来源
   * @return 金币余额(分)
   */
  Long getFamilyGoldBalance(Long familyId, String sysOrigin);

  /**
   * 检查用户是否为工会长.
   *
   * @param userId 用户ID
   * @param familyId 工会ID
   * @return 是否为工会长
   */
  boolean isFamilyLeader(Long userId, Long familyId);

}
