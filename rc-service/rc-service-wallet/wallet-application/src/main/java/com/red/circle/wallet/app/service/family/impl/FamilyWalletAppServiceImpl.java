package com.red.circle.wallet.app.service.family.impl;

import com.red.circle.common.business.dto.cmd.app.AppExtCommand;
import com.red.circle.framework.core.asserts.ResponseAssert;
import com.red.circle.framework.dto.PageResult;
import com.red.circle.wallet.app.dto.clientobject.family.FamilyDiamondRunningWaterCO;
import com.red.circle.wallet.app.dto.clientobject.family.FamilyGoldRunningWaterCO;
import com.red.circle.wallet.app.dto.clientobject.family.FamilyWalletBalanceCO;
import com.red.circle.wallet.app.dto.cmd.family.FamilyDiamondExchangeCmd;
import com.red.circle.wallet.app.dto.cmd.family.FamilyRunningWaterQryCmd;
import com.red.circle.wallet.app.service.family.FamilyWalletAppService;
import com.red.circle.wallet.app.service.family.FamilyWalletService;
import com.red.circle.wallet.domain.convertor.FamilyWalletConvertor;
import com.red.circle.wallet.infra.database.rds.service.family.FamilyDiamondRunningWaterService;
import com.red.circle.wallet.infra.database.rds.service.family.FamilyGoldRunningWaterService;
import java.math.BigDecimal;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 工会钱包应用服务实现.
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class FamilyWalletAppServiceImpl implements FamilyWalletAppService {

  private final FamilyWalletService familyWalletService;
  private final FamilyDiamondRunningWaterService familyDiamondRunningWaterService;
  private final FamilyGoldRunningWaterService familyGoldRunningWaterService;
  private final FamilyWalletConvertor familyWalletConvertor;

  @Override
  public FamilyWalletBalanceCO getFamilyWalletBalance(AppExtCommand cmd) {
    // 获取用户的工会ID
    Long familyId = familyWalletService.getUserFamilyId(cmd.requiredReqUserId());
    ResponseAssert.notNull("用户不属于任何工会", familyId);

    // 获取工会钻石和金币余额
    BigDecimal diamondBalance = familyWalletService.getFamilyDiamondBalance(familyId, cmd.requireReqSysOrigin());
    Long goldBalancePenny = familyWalletService.getFamilyGoldBalance(familyId, cmd.requireReqSysOrigin());

    // 转换为元
    BigDecimal goldBalance = new BigDecimal(goldBalancePenny).divide(new BigDecimal("100"), 2, BigDecimal.ROUND_DOWN);

    return new FamilyWalletBalanceCO()
        .setFamilyId(familyId)
        .setDiamondBalance(diamondBalance)
        .setGoldBalance(goldBalance)
        .setGoldBalancePenny(goldBalancePenny);
  }

  @Override
  public BigDecimal exchangeDiamondToGold(FamilyDiamondExchangeCmd cmd) {
    // 获取用户的工会ID
    Long familyId = familyWalletService.getUserFamilyId(cmd.requiredReqUserId());
    ResponseAssert.notNull("用户不属于任何工会", familyId);

    // 检查用户是否为工会长
    ResponseAssert.isTrue("只有工会长可以进行钻石兑换操作", 
        familyWalletService.isFamilyLeader(cmd.requiredReqUserId(), familyId));

    // 执行兑换
    Long goldAmountPenny = familyWalletService.exchangeDiamondToGold(
        familyId, cmd.requiredReqUserId(), cmd.requireReqSysOrigin(), cmd.getDiamondAmount());

    // 转换为元返回
    return new BigDecimal(goldAmountPenny).divide(new BigDecimal("100"), 2, BigDecimal.ROUND_DOWN);
  }

  @Override
  public PageResult<FamilyDiamondRunningWaterCO> pageFamilyDiamondRunningWater(FamilyRunningWaterQryCmd cmd) {
    // 获取用户的工会ID
    Long familyId = familyWalletService.getUserFamilyId(cmd.requiredReqUserId());
    ResponseAssert.notNull("用户不属于任何工会", familyId);

    // 解析时间范围
    java.time.LocalDateTime startTime = parseDateTime(cmd.getStartDate(), true);
    java.time.LocalDateTime endTime = parseDateTime(cmd.getEndDate(), false);

    // 查询流水
    com.baomidou.mybatisplus.extension.plugins.pagination.Page<com.red.circle.wallet.infra.database.rds.entity.family.FamilyDiamondRunningWater> page =
        familyDiamondRunningWaterService.pageRunningWater(
            familyId, cmd.getType(), cmd.getMemberUserId(),
            startTime, endTime, cmd.getPageNum(), cmd.getPageSize());

    // 转换结果
    return PageResult.of(page.getCurrent(), page.getSize(), page.getTotal(),
        familyWalletConvertor.toFamilyDiamondRunningWaterCOList(page.getRecords()));
  }

  @Override
  public PageResult<FamilyGoldRunningWaterCO> pageFamilyGoldRunningWater(FamilyRunningWaterQryCmd cmd) {
    // 获取用户的工会ID
    Long familyId = familyWalletService.getUserFamilyId(cmd.requiredReqUserId());
    ResponseAssert.notNull("用户不属于任何工会", familyId);

    // 解析时间范围
    java.time.LocalDateTime startTime = parseDateTime(cmd.getStartDate(), true);
    java.time.LocalDateTime endTime = parseDateTime(cmd.getEndDate(), false);

    // 查询流水
    com.baomidou.mybatisplus.extension.plugins.pagination.Page<com.red.circle.wallet.infra.database.rds.entity.family.FamilyGoldRunningWater> page =
        familyGoldRunningWaterService.pageRunningWater(
            familyId, cmd.getType(), cmd.getMemberUserId(),
            startTime, endTime, cmd.getPageNum(), cmd.getPageSize());

    // 转换结果
    return PageResult.of(page.getCurrent(), page.getSize(), page.getTotal(),
        familyWalletConvertor.toFamilyGoldRunningWaterCOList(page.getRecords()));
  }

  /**
   * 解析日期时间.
   */
  private java.time.LocalDateTime parseDateTime(String dateStr, boolean isStart) {
    if (dateStr == null || dateStr.trim().isEmpty()) {
      return null;
    }

    try {
      java.time.LocalDate date = java.time.LocalDate.parse(dateStr);
      return isStart ? date.atStartOfDay() : date.atTime(23, 59, 59);
    } catch (Exception e) {
      log.warn("日期解析失败: {}", dateStr, e);
      return null;
    }
  }

}
