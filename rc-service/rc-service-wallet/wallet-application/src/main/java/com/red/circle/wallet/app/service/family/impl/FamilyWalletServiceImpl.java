package com.red.circle.wallet.app.service.family.impl;

import com.red.circle.common.business.core.enums.ReceiptType;
import com.red.circle.common.business.enums.DiamondOrigin;
import com.red.circle.other.infra.database.cache.service.family.FamilyWeeklyStatsService;
import com.red.circle.other.infra.database.rds.entity.family.AnchorWeeklyPolicy;
import com.red.circle.other.infra.database.rds.entity.family.FamilyMemberInfo;
import com.red.circle.other.infra.database.rds.service.family.FamilyMemberInfoService;
import com.red.circle.other.infra.enums.family.FamilyRoleEnum;
import com.red.circle.wallet.app.common.DiamondRegionCommon;
import com.red.circle.wallet.app.dto.clientobject.DiamondConfigCO;
import com.red.circle.wallet.app.service.family.FamilyWalletService;
import com.red.circle.wallet.infra.database.rds.service.family.FamilyDiamondBalanceService;
import com.red.circle.wallet.infra.database.rds.service.family.FamilyDiamondRunningWaterService;
import com.red.circle.wallet.infra.database.rds.service.family.FamilyGoldBalanceService;
import com.red.circle.wallet.infra.database.rds.service.family.FamilyGoldRunningWaterService;
import com.red.circle.wallet.inner.endpoint.wallet.WalletDiamondClient;
import com.red.circle.wallet.inner.model.cmd.DiamondReceiptCmd;
import java.math.BigDecimal;
import java.math.RoundingMode;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 工会钱包服务实现.
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class FamilyWalletServiceImpl implements FamilyWalletService {

  private final FamilyWeeklyStatsService familyWeeklyStatsService;
  private final FamilyDiamondBalanceService familyDiamondBalanceService;
  private final FamilyGoldBalanceService familyGoldBalanceService;
  private final FamilyDiamondRunningWaterService familyDiamondRunningWaterService;
  private final FamilyGoldRunningWaterService familyGoldRunningWaterService;
  private final WalletDiamondClient walletDiamondClient;
  private final DiamondRegionCommon diamondRegionCommon;
  private final FamilyMemberInfoService familyMemberInfoService;

  @Override
  @Transactional(rollbackFor = Exception.class)
  public void processGiftDiamondDistribution(Long userId, Long familyId, String sysOrigin, Long giftGoldValue) {
    // 1. 更新用户本周收礼统计
    Long weeklyTotal = familyWeeklyStatsService.addWeeklyGiftGold(familyId, userId, giftGoldValue);

    // 2. 根据累计金额查找匹配的政策档位
    AnchorWeeklyPolicy policy = familyWeeklyStatsService.findMatchingPolicy(weeklyTotal);

    if (policy == null) {
      log.debug("未匹配到政策，不进行钻石分配: userId={}, familyId={}, weeklyTotal={}",
          userId, familyId, weeklyTotal);
      return;
    }

    // 3. 计算钻石分配
    // 礼物金币转钻石：giftGoldValue(分) * goldToDiamondRatio / 100 / 100 (分转元)
    BigDecimal giftGoldInYuan = new BigDecimal(giftGoldValue).divide(new BigDecimal("100"), 2, RoundingMode.DOWN);
    BigDecimal totalDiamond = giftGoldInYuan.multiply(policy.getGoldToDiamondRatio()).divide(new BigDecimal("100"), 2, RoundingMode.DOWN);

    BigDecimal anchorDiamond = totalDiamond.multiply(policy.getAnchorCommissionRatio()).divide(new BigDecimal("100"), 2, RoundingMode.DOWN);
    BigDecimal familyDiamond = totalDiamond.multiply(policy.getFamilyCommissionRatio()).divide(new BigDecimal("100"), 2, RoundingMode.DOWN);

    log.info("钻石分配计算: userId={}, familyId={}, 礼物金币={}分, 总钻石={}, 主播钻石={}, 工会钻石={}",
        userId, familyId, giftGoldValue, totalDiamond, anchorDiamond, familyDiamond);

    // 4. 分配钻石给个人
    if (anchorDiamond.compareTo(BigDecimal.ZERO) > 0) {
      walletDiamondClient.changeBalance(new DiamondReceiptCmd()
          .setUserId(userId)
          .setOrigin(DiamondOrigin.ACCEPT_LUCKY_GIFTS)
          .setCustomizeOriginDesc("收到礼物钻石奖励(" + policy.getAnchorCommissionRatio() + "%)")
          .setType(ReceiptType.INCOME)
          .setAmount(anchorDiamond));
    }

    // 5. 分配钻石给工会
    if (familyDiamond.compareTo(BigDecimal.ZERO) > 0) {
      familyDiamondBalanceService.addBalance(familyId, sysOrigin, familyDiamond);

      // 记录流水
      familyDiamondRunningWaterService.recordGiftCommission(
          familyId, userId, sysOrigin, giftGoldValue, anchorDiamond, familyDiamond);
    }
  }

  @Override
  @Transactional(rollbackFor = Exception.class)
  public Long exchangeDiamondToGold(Long familyId, Long operatorUserId, String sysOrigin, BigDecimal diamondAmount) {
    // 1. 检查余额是否足够
    if (!familyDiamondBalanceService.hasEnoughBalance(familyId, sysOrigin, diamondAmount)) {
      throw new RuntimeException("工会钻石余额不足");
    }

    // 2. 获取区域兑换比例 (复用个人钱包逻辑)
    DiamondConfigCO config = diamondRegionCommon.getDiamondRegionConfig(operatorUserId);

    // 3. 计算金币数量 (钻石 * 比例 * 100，转换为分)
    BigDecimal goldInYuan = diamondAmount.multiply(config.getDiamondEqGold());
    Long goldAmount = goldInYuan.multiply(new BigDecimal("100")).longValue();

    // 4. 扣除工会钻石
    familyDiamondBalanceService.deductBalance(familyId, sysOrigin, diamondAmount);

    // 5. 增加工会金币
    familyGoldBalanceService.addBalance(familyId, sysOrigin, goldAmount);

    // 6. 记录流水
    familyDiamondRunningWaterService.recordExchangeToGold(familyId, operatorUserId, sysOrigin, diamondAmount);
    familyGoldRunningWaterService.recordDiamondExchange(familyId, operatorUserId, sysOrigin, diamondAmount, goldAmount);

    log.info("工会钻石兑换金币: familyId={}, operatorUserId={}, 钻石={}, 金币={}分",
        familyId, operatorUserId, diamondAmount, goldAmount);

    return goldAmount;
  }

  @Override
  public BigDecimal getFamilyDiamondBalance(Long familyId, String sysOrigin) {
    return familyDiamondBalanceService.getBalance(familyId, sysOrigin);
  }

  @Override
  public Long getFamilyGoldBalance(Long familyId, String sysOrigin) {
    return familyGoldBalanceService.getBalance(familyId, sysOrigin);
  }

  @Override
  public boolean isFamilyLeader(Long userId, Long familyId) {
    FamilyMemberInfo memberInfo = familyMemberInfoService.getFamilyMemberByUserId(userId);

    if (memberInfo == null || !familyId.equals(memberInfo.getFamilyId())) {
      return false;
    }

    // 检查是否为工会长 (ADMIN角色)
    return FamilyRoleEnum.ADMIN.name().equals(memberInfo.getMemberRole());
  }

  /**
   * 获取用户的工会ID.
   *
   * @param userId 用户ID
   * @return 工会ID，如果用户不属于任何工会返回null
   */
  public Long getUserFamilyId(Long userId) {
    return familyMemberInfoService.getFamilyIdByUserId(userId);
  }

}
