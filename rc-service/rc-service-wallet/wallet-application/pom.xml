<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>
  <artifactId>wallet-application</artifactId>
  <description>应用层，服务实现</description>
  <packaging>jar</packaging>

  <dependencies>

    <dependency>
      <groupId>com.red.circle</groupId>
      <artifactId>wallet-client</artifactId>
    </dependency>

    <dependency>
      <groupId>com.red.circle</groupId>
      <artifactId>wallet-infrastructure</artifactId>
    </dependency>
      <dependency>
          <groupId>com.red.circle</groupId>
          <artifactId>other-infrastructure</artifactId>
          <version>1.0.0-local</version>
          <scope>compile</scope>
      </dependency>

  </dependencies>

  <parent>
    <artifactId>rc-service-wallet</artifactId>
    <groupId>com.red.circle</groupId>
    <version>${revision}</version>
    <relativePath>./../pom.xml</relativePath>
  </parent>

</project>
