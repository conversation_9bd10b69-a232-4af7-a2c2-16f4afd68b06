package com.red.circle.wallet.domain.convertor;

import com.red.circle.framework.core.convertor.ConvertorModel;
import com.red.circle.wallet.app.dto.clientobject.family.FamilyDiamondRunningWaterCO;
import com.red.circle.wallet.app.dto.clientobject.family.FamilyGoldRunningWaterCO;
import com.red.circle.wallet.infra.database.rds.entity.family.FamilyDiamondRunningWater;
import com.red.circle.wallet.infra.database.rds.entity.family.FamilyGoldRunningWater;
import java.math.BigDecimal;
import java.util.List;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

/**
 * 工会钱包转换器.
 *
 * <AUTHOR>
 */
@Mapper(componentModel = ConvertorModel.SPRING)
public interface FamilyWalletConvertor {

  /**
   * 转换钻石流水实体为CO.
   */
  @Mapping(target = "giftGoldValue", expression = "java(convertPennyToYuan(entity.getGiftGoldValue()))")
  @Mapping(target = "typeDesc", expression = "java(getTypeDesc(entity.getType()))")
  FamilyDiamondRunningWaterCO toFamilyDiamondRunningWaterCO(FamilyDiamondRunningWater entity);

  /**
   * 转换钻石流水实体列表为CO列表.
   */
  List<FamilyDiamondRunningWaterCO> toFamilyDiamondRunningWaterCOList(List<FamilyDiamondRunningWater> entities);

  /**
   * 转换金币流水实体为CO.
   */
  @Mapping(target = "goldAmount", expression = "java(convertPennyToYuan(entity.getGoldAmount()))")
  @Mapping(target = "goldAmountPenny", source = "goldAmount")
  @Mapping(target = "typeDesc", expression = "java(getTypeDesc(entity.getType()))")
  FamilyGoldRunningWaterCO toFamilyGoldRunningWaterCO(FamilyGoldRunningWater entity);

  /**
   * 转换金币流水实体列表为CO列表.
   */
  List<FamilyGoldRunningWaterCO> toFamilyGoldRunningWaterCOList(List<FamilyGoldRunningWater> entities);

  /**
   * 分转元.
   */
  default BigDecimal convertPennyToYuan(Long penny) {
    if (penny == null) {
      return BigDecimal.ZERO;
    }
    return new BigDecimal(penny).divide(new BigDecimal("100"), 2, BigDecimal.ROUND_DOWN);
  }

  /**
   * 获取类型描述.
   */
  default String getTypeDesc(Integer type) {
    if (type == null) {
      return "";
    }
    return type == 0 ? "收入" : "支出";
  }

}
