package com.red.circle.wallet.adapter.app;

import com.red.circle.common.business.dto.cmd.AppExtCommand;
import com.red.circle.framework.dto.PageResult;
import com.red.circle.framework.web.controller.BaseController;
import com.red.circle.wallet.app.dto.clientobject.family.FamilyDiamondRunningWaterCO;
import com.red.circle.wallet.app.dto.clientobject.family.FamilyGoldRunningWaterCO;
import com.red.circle.wallet.app.dto.clientobject.family.FamilyWalletBalanceCO;
import com.red.circle.wallet.app.dto.cmd.family.FamilyDiamondExchangeCmd;
import com.red.circle.wallet.app.dto.cmd.family.FamilyRunningWaterQryCmd;
import com.red.circle.wallet.app.service.family.FamilyWalletAppService;
import java.math.BigDecimal;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 工会钱包相关接口.
 *
 * @eo.api-type http
 * @eo.groupName 钱包服务.工会钱包
 * @eo.path /wallet/family
 */
@Slf4j
@RequiredArgsConstructor
@RestController
@RequestMapping("/wallet/family")
@Validated
public class FamilyWalletRestController extends BaseController {

  private final FamilyWalletAppService familyWalletAppService;

  /**
   * 获取工会钱包余额.
   *
   * @eo.api-name 获取工会钱包余额
   * @eo.api-desc 获取当前用户所属工会的钻石和金币余额
   * @eo.api-path /wallet/family/balance
   * @eo.api-method GET
   */
  @GetMapping("/balance")
  public FamilyWalletBalanceCO getFamilyWalletBalance(AppExtCommand cmd) {
    return familyWalletAppService.getFamilyWalletBalance(cmd);
  }

  /**
   * 钻石兑换金币.
   *
   * @eo.api-name 钻石兑换金币
   * @eo.api-desc 工会长将工会钻石兑换为金币
   * @eo.api-path /wallet/family/diamond/exchange
   * @eo.api-method POST
   */
  @PostMapping(value = "/diamond/exchange", consumes = MediaType.APPLICATION_JSON_VALUE)
  public BigDecimal exchangeDiamondToGold(@Validated @RequestBody FamilyDiamondExchangeCmd cmd) {
    return familyWalletAppService.exchangeDiamondToGold(cmd);
  }

  /**
   * 查询工会钻石流水.
   *
   * @eo.api-name 查询工会钻石流水
   * @eo.api-desc 分页查询工会钻石流水记录
   * @eo.api-path /wallet/family/diamond/running-water
   * @eo.api-method GET
   */
  @GetMapping("/diamond/running-water")
  public PageResult<FamilyDiamondRunningWaterCO> pageFamilyDiamondRunningWater(
      FamilyRunningWaterQryCmd cmd) {
    return familyWalletAppService.pageFamilyDiamondRunningWater(cmd);
  }

  /**
   * 查询工会金币流水.
   *
   * @eo.api-name 查询工会金币流水
   * @eo.api-desc 分页查询工会金币流水记录
   * @eo.api-path /wallet/family/gold/running-water
   * @eo.api-method GET
   */
  @GetMapping("/gold/running-water")
  public PageResult<FamilyGoldRunningWaterCO> pageFamilyGoldRunningWater(
      FamilyRunningWaterQryCmd cmd) {
    return familyWalletAppService.pageFamilyGoldRunningWater(cmd);
  }

}
