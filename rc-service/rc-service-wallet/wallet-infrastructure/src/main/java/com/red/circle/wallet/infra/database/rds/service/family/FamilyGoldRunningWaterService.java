package com.red.circle.wallet.infra.database.rds.service.family;

import com.baomidou.mybatisplus.extension.service.IService;
import com.red.circle.wallet.infra.database.rds.entity.family.FamilyGoldRunningWater;
import java.math.BigDecimal;

/**
 * 工会金币流水Service.
 *
 * <AUTHOR>
 */
public interface FamilyGoldRunningWaterService extends IService<FamilyGoldRunningWater> {

  /**
   * 记录钻石兑换金币流水.
   *
   * @param familyId 工会ID
   * @param operatorUserId 操作用户ID
   * @param sysOrigin 系统来源
   * @param diamondAmount 钻石数量
   * @param goldAmount 金币数量(分)
   * @return 流水记录
   */
  FamilyGoldRunningWater recordDiamondExchange(Long familyId, Long operatorUserId, String sysOrigin,
      BigDecimal diamondAmount, Long goldAmount);

  /**
   * 记录分发给成员流水.
   *
   * @param familyId 工会ID
   * @param memberUserId 成员用户ID
   * @param operatorUserId 操作用户ID
   * @param sysOrigin 系统来源
   * @param goldAmount 金币数量(分)
   * @return 流水记录
   */
  FamilyGoldRunningWater recordDistributeToMember(Long familyId, Long memberUserId, Long operatorUserId, 
      String sysOrigin, Long goldAmount);

  /**
   * 记录成员提现钻石转入金币流水.
   *
   * @param familyId 工会ID
   * @param memberUserId 成员用户ID
   * @param sysOrigin 系统来源
   * @param diamondAmount 钻石数量
   * @param goldAmount 金币数量(分)
   * @return 流水记录
   */
  FamilyGoldRunningWater recordMemberWithdrawDiamond(Long familyId, Long memberUserId, String sysOrigin,
      BigDecimal diamondAmount, Long goldAmount);

  /**
   * 分页查询工会金币流水.
   *
   * @param familyId 工会ID
   * @param type 类型 0.收入 1.支出
   * @param memberUserId 成员用户ID
   * @param startTime 开始时间
   * @param endTime 结束时间
   * @param pageNum 页码
   * @param pageSize 页大小
   * @return 流水分页结果
   */
  com.baomidou.mybatisplus.extension.plugins.pagination.Page<FamilyGoldRunningWater> pageRunningWater(
      Long familyId, Integer type, Long memberUserId,
      java.time.LocalDateTime startTime, java.time.LocalDateTime endTime,
      int pageNum, int pageSize);

}
