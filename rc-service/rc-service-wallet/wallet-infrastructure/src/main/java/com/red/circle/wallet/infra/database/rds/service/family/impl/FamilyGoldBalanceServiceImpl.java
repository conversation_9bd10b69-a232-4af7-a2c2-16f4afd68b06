package com.red.circle.wallet.infra.database.rds.service.family.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.red.circle.wallet.infra.database.rds.entity.family.FamilyGoldBalance;
import com.red.circle.wallet.infra.database.rds.mapper.family.FamilyGoldBalanceMapper;
import com.red.circle.wallet.infra.database.rds.service.family.FamilyGoldBalanceService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 工会金币余额Service实现.
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class FamilyGoldBalanceServiceImpl extends ServiceImpl<FamilyGoldBalanceMapper, FamilyGoldBalance>
    implements FamilyGoldBalanceService {

  @Override
  public Long getBalance(Long familyId, String sysOrigin) {
    FamilyGoldBalance balance = lambdaQuery()
        .eq(FamilyGoldBalance::getFamilyId, familyId)
        .eq(FamilyGoldBalance::getSysOrigin, sysOrigin)
        .one();
    
    return balance != null ? balance.getBalance() : 0L;
  }

  @Override
  @Transactional(rollbackFor = Exception.class)
  public boolean addBalance(Long familyId, String sysOrigin, Long amount) {
    if (amount <= 0) {
      return false;
    }

    // 查找现有记录
    FamilyGoldBalance balance = lambdaQuery()
        .eq(FamilyGoldBalance::getFamilyId, familyId)
        .eq(FamilyGoldBalance::getSysOrigin, sysOrigin)
        .one();

    if (balance == null) {
      // 创建新记录
      balance = new FamilyGoldBalance()
          .setFamilyId(familyId)
          .setSysOrigin(sysOrigin)
          .setBalance(amount);
      return save(balance);
    } else {
      // 更新现有记录
      return lambdaUpdate()
          .eq(FamilyGoldBalance::getFamilyId, familyId)
          .eq(FamilyGoldBalance::getSysOrigin, sysOrigin)
          .setSql("balance = balance + " + amount)
          .update();
    }
  }

  @Override
  @Transactional(rollbackFor = Exception.class)
  public boolean deductBalance(Long familyId, String sysOrigin, Long amount) {
    if (amount <= 0) {
      return false;
    }

    // 检查余额是否足够
    if (!hasEnoughBalance(familyId, sysOrigin, amount)) {
      log.warn("工会金币余额不足, familyId: {}, sysOrigin: {}, 需要: {}, 当前: {}", 
          familyId, sysOrigin, amount, getBalance(familyId, sysOrigin));
      return false;
    }

    // 扣除余额
    return lambdaUpdate()
        .eq(FamilyGoldBalance::getFamilyId, familyId)
        .eq(FamilyGoldBalance::getSysOrigin, sysOrigin)
        .setSql("balance = balance - " + amount)
        .update();
  }

  @Override
  public boolean hasEnoughBalance(Long familyId, String sysOrigin, Long amount) {
    Long currentBalance = getBalance(familyId, sysOrigin);
    return currentBalance >= amount;
  }

}
