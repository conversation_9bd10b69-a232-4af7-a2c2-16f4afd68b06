package com.red.circle.wallet.infra.database.rds.service.family;

import com.baomidou.mybatisplus.extension.service.IService;
import com.red.circle.wallet.infra.database.rds.entity.family.FamilyDiamondRunningWater;
import java.math.BigDecimal;

/**
 * 工会钻石流水Service.
 *
 * <AUTHOR>
 */
public interface FamilyDiamondRunningWaterService extends IService<FamilyDiamondRunningWater> {

  /**
   * 记录成员收礼提成流水.
   *
   * @param familyId 工会ID
   * @param memberUserId 成员用户ID
   * @param sysOrigin 系统来源
   * @param giftGoldValue 礼物金币价值(分)
   * @param memberDiamondAmount 个人获得钻石数量
   * @param familyDiamondAmount 工会获得钻石数量
   * @return 流水记录
   */
  FamilyDiamondRunningWater recordGiftCommission(Long familyId, Long memberUserId, String sysOrigin,
      Long giftGoldValue, BigDecimal memberDiamondAmount, BigDecimal familyDiamondAmount);

  /**
   * 记录钻石兑换金币流水.
   *
   * @param familyId 工会ID
   * @param operatorUserId 操作用户ID
   * @param sysOrigin 系统来源
   * @param diamondAmount 钻石数量
   * @return 流水记录
   */
  FamilyDiamondRunningWater recordExchangeToGold(Long familyId, Long operatorUserId, String sysOrigin,
      BigDecimal diamondAmount);

  /**
   * 记录钻石提现流水.
   *
   * @param familyId 工会ID
   * @param operatorUserId 操作用户ID
   * @param sysOrigin 系统来源
   * @param diamondAmount 钻石数量
   * @return 流水记录
   */
  FamilyDiamondRunningWater recordWithdrawDiamond(Long familyId, Long operatorUserId, String sysOrigin,
      BigDecimal diamondAmount);

}
