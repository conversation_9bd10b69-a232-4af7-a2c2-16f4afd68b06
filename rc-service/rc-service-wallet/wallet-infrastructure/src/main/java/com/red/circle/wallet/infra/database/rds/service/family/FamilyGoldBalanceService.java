package com.red.circle.wallet.infra.database.rds.service.family;

import com.baomidou.mybatisplus.extension.service.IService;
import com.red.circle.wallet.infra.database.rds.entity.family.FamilyGoldBalance;

/**
 * 工会金币余额Service.
 *
 * <AUTHOR>
 */
public interface FamilyGoldBalanceService extends IService<FamilyGoldBalance> {

  /**
   * 获取工会金币余额(分).
   *
   * @param familyId 工会ID
   * @param sysOrigin 系统来源
   * @return 金币余额(分)
   */
  Long getBalance(Long familyId, String sysOrigin);

  /**
   * 增加工会金币余额.
   *
   * @param familyId 工会ID
   * @param sysOrigin 系统来源
   * @param amount 增加数量(分)
   * @return 是否成功
   */
  boolean addBalance(Long familyId, String sysOrigin, Long amount);

  /**
   * 扣除工会金币余额.
   *
   * @param familyId 工会ID
   * @param sysOrigin 系统来源
   * @param amount 扣除数量(分)
   * @return 是否成功
   */
  boolean deductBalance(Long familyId, String sysOrigin, Long amount);

  /**
   * 检查余额是否足够.
   *
   * @param familyId 工会ID
   * @param sysOrigin 系统来源
   * @param amount 需要的数量(分)
   * @return 是否足够
   */
  boolean hasEnoughBalance(Long familyId, String sysOrigin, Long amount);

}
