package com.red.circle.wallet.infra.database.rds.service.family.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.red.circle.wallet.infra.database.rds.entity.family.FamilyDiamondBalance;
import com.red.circle.wallet.infra.database.rds.mapper.family.FamilyDiamondBalanceMapper;
import com.red.circle.wallet.infra.database.rds.service.family.FamilyDiamondBalanceService;
import java.math.BigDecimal;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 工会钻石余额Service实现.
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class FamilyDiamondBalanceServiceImpl extends ServiceImpl<FamilyDiamondBalanceMapper, FamilyDiamondBalance>
    implements FamilyDiamondBalanceService {

  @Override
  public BigDecimal getBalance(Long familyId, String sysOrigin) {
    FamilyDiamondBalance balance = lambdaQuery()
        .eq(FamilyDiamondBalance::getFamilyId, familyId)
        .eq(FamilyDiamondBalance::getSysOrigin, sysOrigin)
        .one();
    
    return balance != null ? balance.getBalance() : BigDecimal.ZERO;
  }

  @Override
  @Transactional(rollbackFor = Exception.class)
  public boolean addBalance(Long familyId, String sysOrigin, BigDecimal amount) {
    if (amount.compareTo(BigDecimal.ZERO) <= 0) {
      return false;
    }

    // 查找现有记录
    FamilyDiamondBalance balance = lambdaQuery()
        .eq(FamilyDiamondBalance::getFamilyId, familyId)
        .eq(FamilyDiamondBalance::getSysOrigin, sysOrigin)
        .one();

    if (balance == null) {
      // 创建新记录
      balance = new FamilyDiamondBalance()
          .setFamilyId(familyId)
          .setSysOrigin(sysOrigin)
          .setBalance(amount);
      return save(balance);
    } else {
      // 更新现有记录
      return lambdaUpdate()
          .eq(FamilyDiamondBalance::getFamilyId, familyId)
          .eq(FamilyDiamondBalance::getSysOrigin, sysOrigin)
          .setSql("balance = balance + " + amount)
          .update();
    }
  }

  @Override
  @Transactional(rollbackFor = Exception.class)
  public boolean deductBalance(Long familyId, String sysOrigin, BigDecimal amount) {
    if (amount.compareTo(BigDecimal.ZERO) <= 0) {
      return false;
    }

    // 检查余额是否足够
    if (!hasEnoughBalance(familyId, sysOrigin, amount)) {
      log.warn("工会钻石余额不足, familyId: {}, sysOrigin: {}, 需要: {}, 当前: {}", 
          familyId, sysOrigin, amount, getBalance(familyId, sysOrigin));
      return false;
    }

    // 扣除余额
    return lambdaUpdate()
        .eq(FamilyDiamondBalance::getFamilyId, familyId)
        .eq(FamilyDiamondBalance::getSysOrigin, sysOrigin)
        .setSql("balance = balance - " + amount)
        .update();
  }

  @Override
  public boolean hasEnoughBalance(Long familyId, String sysOrigin, BigDecimal amount) {
    BigDecimal currentBalance = getBalance(familyId, sysOrigin);
    return currentBalance.compareTo(amount) >= 0;
  }

}
