package com.red.circle.wallet.infra.enums;

/**
 * <AUTHOR> on 2023/10/12
 */
public enum LogTopic {

  /**
   * 钱包金币资产.
   */
  WALLET_GOLD_ASSET_PROD("30e80b29-9fbd-4630-a45d-398146b14ad5"),
  WALLET_GOLD_ASSET_DEVELOP("30e80b29-9fbd-4630-a45d-398146b14ad5");

  private final String topic;

  LogTopic(String topic) {
    this.topic = topic;
  }

  public String getWalletTopic(boolean isProd) {
    if (isProd) {
      return WALLET_GOLD_ASSET_PROD.topic;
    }
    return WALLET_GOLD_ASSET_DEVELOP.topic;
  }

}
