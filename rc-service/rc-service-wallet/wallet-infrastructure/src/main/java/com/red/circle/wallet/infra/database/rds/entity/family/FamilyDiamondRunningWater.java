package com.red.circle.wallet.infra.database.rds.entity.family;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serial;
import java.math.BigDecimal;

import com.red.circle.framework.mybatis.entity.TimestampBaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 工会钻石流水表.
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("family_diamond_running_water")
public class FamilyDiamondRunningWater extends TimestampBaseEntity {

  @Serial
  private static final long serialVersionUID = 1L;

  /**
   * 主键ID.
   */
  @TableId(value = "id", type = IdType.ASSIGN_ID)
  private Long id;

  /**
   * 工会ID.
   */
  @TableField("family_id")
  private Long familyId;

  /**
   * 关联成员用户ID.
   */
  @TableField("member_user_id")
  private Long memberUserId;

  /**
   * 系统来源.
   */
  @TableField("sys_origin")
  private String sysOrigin;

  /**
   * 来源类型.
   */
  @TableField("origin")
  private String origin;

  /**
   * 来源描述.
   */
  @TableField("origin_desc")
  private String originDesc;

  /**
   * 类型 0.收入 1.支出.
   */
  @TableField("type")
  private Integer type;

  /**
   * 礼物金币价值(分).
   */
  @TableField("gift_gold_value")
  private Long giftGoldValue;

  /**
   * 个人获得钻石数量.
   */
  @TableField("member_diamond_amount")
  private BigDecimal memberDiamondAmount;

  /**
   * 工会钻石变动数量.
   */
  @TableField("family_diamond_amount")
  private BigDecimal familyDiamondAmount;

}
