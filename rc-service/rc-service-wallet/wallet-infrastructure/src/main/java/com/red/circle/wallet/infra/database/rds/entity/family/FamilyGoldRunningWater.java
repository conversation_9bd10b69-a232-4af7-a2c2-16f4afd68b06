package com.red.circle.wallet.infra.database.rds.entity.family;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.red.circle.framework.mybatis.entity.TimestampBaseEntity;
import java.io.Serial;
import java.math.BigDecimal;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 工会金币流水表.
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("family_gold_running_water")
public class FamilyGoldRunningWater extends TimestampBaseEntity {

  @Serial
  private static final long serialVersionUID = 1L;

  /**
   * 主键ID.
   */
  @TableId(value = "id", type = IdType.ASSIGN_ID)
  private Long id;

  /**
   * 工会ID.
   */
  @TableField("family_id")
  private Long familyId;

  /**
   * 关联成员用户ID.
   */
  @TableField("member_user_id")
  private Long memberUserId;

  /**
   * 系统来源.
   */
  @TableField("sys_origin")
  private String sysOrigin;

  /**
   * 来源类型.
   */
  @TableField("origin")
  private String origin;

  /**
   * 来源描述.
   */
  @TableField("origin_desc")
  private String originDesc;

  /**
   * 类型 0.收入 1.支出.
   */
  @TableField("type")
  private Integer type;

  /**
   * 钻石数量(兑换时).
   */
  @TableField("diamond_amount")
  private BigDecimal diamondAmount;

  /**
   * 金币变动数量(分).
   */
  @TableField("gold_amount")
  private Long goldAmount;

}
