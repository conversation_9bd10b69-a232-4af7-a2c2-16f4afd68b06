package com.red.circle.wallet.infra.database.rds.service.family.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.red.circle.common.business.enums.FamilyGoldOrigin;
import com.red.circle.wallet.infra.database.rds.entity.family.FamilyGoldRunningWater;
import com.red.circle.wallet.infra.database.rds.mapper.family.FamilyGoldRunningWaterMapper;
import com.red.circle.wallet.infra.database.rds.service.family.FamilyGoldRunningWaterService;
import java.math.BigDecimal;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 工会金币流水Service实现.
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class FamilyGoldRunningWaterServiceImpl extends ServiceImpl<FamilyGoldRunningWaterMapper, FamilyGoldRunningWater>
    implements FamilyGoldRunningWaterService {

  @Override
  public FamilyGoldRunningWater recordDiamondExchange(Long familyId, Long operatorUserId, String sysOrigin,
      BigDecimal diamondAmount, Long goldAmount) {
    
    FamilyGoldRunningWater water = new FamilyGoldRunningWater()
        .setFamilyId(familyId)
        .setMemberUserId(operatorUserId)
        .setSysOrigin(sysOrigin)
        .setOrigin(FamilyGoldOrigin.DIAMOND_EXCHANGE.name())
        .setOriginDesc(FamilyGoldOrigin.DIAMOND_EXCHANGE.getDesc())
        .setType(0) // 收入
        .setDiamondAmount(diamondAmount)
        .setGoldAmount(goldAmount);

    save(water);
    log.info("记录工会钻石兑换金币流水: familyId={}, operatorUserId={}, diamondAmount={}, goldAmount={}", 
        familyId, operatorUserId, diamondAmount, goldAmount);
    
    return water;
  }

  @Override
  public FamilyGoldRunningWater recordDistributeToMember(Long familyId, Long memberUserId, Long operatorUserId, 
      String sysOrigin, Long goldAmount) {
    
    FamilyGoldRunningWater water = new FamilyGoldRunningWater()
        .setFamilyId(familyId)
        .setMemberUserId(memberUserId)
        .setSysOrigin(sysOrigin)
        .setOrigin(FamilyGoldOrigin.DISTRIBUTE_TO_MEMBER.name())
        .setOriginDesc(FamilyGoldOrigin.DISTRIBUTE_TO_MEMBER.getDesc())
        .setType(1) // 支出
        .setGoldAmount(-goldAmount); // 负数表示支出

    save(water);
    log.info("记录工会金币分发给成员流水: familyId={}, memberUserId={}, operatorUserId={}, goldAmount={}", 
        familyId, memberUserId, operatorUserId, goldAmount);
    
    return water;
  }

  @Override
  public FamilyGoldRunningWater recordMemberWithdrawDiamond(Long familyId, Long memberUserId, String sysOrigin,
      BigDecimal diamondAmount, Long goldAmount) {
    
    FamilyGoldRunningWater water = new FamilyGoldRunningWater()
        .setFamilyId(familyId)
        .setMemberUserId(memberUserId)
        .setSysOrigin(sysOrigin)
        .setOrigin(FamilyGoldOrigin.MEMBER_WITHDRAW_DIAMOND.name())
        .setOriginDesc(FamilyGoldOrigin.MEMBER_WITHDRAW_DIAMOND.getDesc())
        .setType(0) // 收入
        .setDiamondAmount(diamondAmount)
        .setGoldAmount(goldAmount);

    save(water);
    log.info("记录成员提现钻石转入工会金币流水: familyId={}, memberUserId={}, diamondAmount={}, goldAmount={}", 
        familyId, memberUserId, diamondAmount, goldAmount);
    
    return water;
  }

  @Override
  public com.baomidou.mybatisplus.extension.plugins.pagination.Page<FamilyGoldRunningWater> pageRunningWater(
      Long familyId, Integer type, Long memberUserId,
      java.time.LocalDateTime startTime, java.time.LocalDateTime endTime,
      int pageNum, int pageSize) {

    com.baomidou.mybatisplus.extension.plugins.pagination.Page<FamilyGoldRunningWater> page =
        new com.baomidou.mybatisplus.extension.plugins.pagination.Page<>(pageNum, pageSize);

    return lambdaQuery()
        .eq(FamilyGoldRunningWater::getFamilyId, familyId)
        .eq(type != null, FamilyGoldRunningWater::getType, type)
        .eq(memberUserId != null, FamilyGoldRunningWater::getMemberUserId, memberUserId)
        .ge(startTime != null, FamilyGoldRunningWater::getCreateTime, startTime)
        .le(endTime != null, FamilyGoldRunningWater::getCreateTime, endTime)
        .orderByDesc(FamilyGoldRunningWater::getCreateTime)
        .page(page);
  }

}
