package com.red.circle.wallet.infra.enums;

import org.apache.commons.lang3.StringUtils;

/**
 * 货运金币兑美元比例
 *
 * <AUTHOR> on 2021/10/27
 */
public enum FreightGoldToUsdRatioEnum {

  /**
   * Aswat
   */
  ASWAT(830),

  /**
   * Yahlla.
   */
  YAHLLA(1100),

  /**
   * YOLO.
   */
  YOLO(6000),
  MARCIE(6000);

  private final int ratio;

  FreightGoldToUsdRatioEnum(int ratio) {
    this.ratio = ratio;
  }

  public static int getRatio(String sysOrigin) {
    if (StringUtils.isBlank(sysOrigin)) {
      return 1000;
    }

    try {
      FreightGoldToUsdRatioEnum ratioEnum = FreightGoldToUsdRatioEnum.valueOf(sysOrigin);
      return ratioEnum.getRatio();
    } catch (Exception ex) {
      // ignore
    }
    return 1000;
  }

  public int getRatio() {
    return ratio;
  }

}
