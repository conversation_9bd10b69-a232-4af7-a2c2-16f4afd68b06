package com.red.circle.wallet.infra.database.rds.service.family;

import com.baomidou.mybatisplus.extension.service.IService;
import com.red.circle.wallet.infra.database.rds.entity.family.FamilyDiamondBalance;
import java.math.BigDecimal;

/**
 * 工会钻石余额Service.
 *
 * <AUTHOR>
 */
public interface FamilyDiamondBalanceService extends IService<FamilyDiamondBalance> {

  /**
   * 获取工会钻石余额.
   *
   * @param familyId 工会ID
   * @param sysOrigin 系统来源
   * @return 钻石余额
   */
  BigDecimal getBalance(Long familyId, String sysOrigin);

  /**
   * 增加工会钻石余额.
   *
   * @param familyId 工会ID
   * @param sysOrigin 系统来源
   * @param amount 增加数量
   * @return 是否成功
   */
  boolean addBalance(Long familyId, String sysOrigin, BigDecimal amount);

  /**
   * 扣除工会钻石余额.
   *
   * @param familyId 工会ID
   * @param sysOrigin 系统来源
   * @param amount 扣除数量
   * @return 是否成功
   */
  boolean deductBalance(Long familyId, String sysOrigin, BigDecimal amount);

  /**
   * 检查余额是否足够.
   *
   * @param familyId 工会ID
   * @param sysOrigin 系统来源
   * @param amount 需要的数量
   * @return 是否足够
   */
  boolean hasEnoughBalance(Long familyId, String sysOrigin, BigDecimal amount);

}
