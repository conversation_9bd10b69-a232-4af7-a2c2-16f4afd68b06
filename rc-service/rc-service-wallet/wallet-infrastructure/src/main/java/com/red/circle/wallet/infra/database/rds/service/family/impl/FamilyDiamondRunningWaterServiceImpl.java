package com.red.circle.wallet.infra.database.rds.service.family.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.red.circle.common.business.enums.FamilyDiamondOrigin;
import com.red.circle.wallet.infra.database.rds.entity.family.FamilyDiamondRunningWater;
import com.red.circle.wallet.infra.database.rds.mapper.family.FamilyDiamondRunningWaterMapper;
import com.red.circle.wallet.infra.database.rds.service.family.FamilyDiamondRunningWaterService;
import java.math.BigDecimal;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 工会钻石流水Service实现.
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class FamilyDiamondRunningWaterServiceImpl extends ServiceImpl<FamilyDiamondRunningWaterMapper, FamilyDiamondRunningWater>
    implements FamilyDiamondRunningWaterService {

  @Override
  public FamilyDiamondRunningWater recordGiftCommission(Long familyId, Long memberUserId, String sysOrigin,
      Long giftGoldValue, BigDecimal memberDiamondAmount, BigDecimal familyDiamondAmount) {
    
    FamilyDiamondRunningWater water = new FamilyDiamondRunningWater()
        .setFamilyId(familyId)
        .setMemberUserId(memberUserId)
        .setSysOrigin(sysOrigin)
        .setOrigin(FamilyDiamondOrigin.MEMBER_GIFT_COMMISSION.name())
        .setOriginDesc(FamilyDiamondOrigin.MEMBER_GIFT_COMMISSION.getDesc())
        .setType(0) // 收入
        .setGiftGoldValue(giftGoldValue)
        .setMemberDiamondAmount(memberDiamondAmount)
        .setFamilyDiamondAmount(familyDiamondAmount);

    save(water);
    log.info("记录工会钻石收礼提成流水: familyId={}, memberUserId={}, giftGoldValue={}, familyDiamondAmount={}", 
        familyId, memberUserId, giftGoldValue, familyDiamondAmount);
    
    return water;
  }

  @Override
  public FamilyDiamondRunningWater recordExchangeToGold(Long familyId, Long operatorUserId, String sysOrigin,
      BigDecimal diamondAmount) {
    
    FamilyDiamondRunningWater water = new FamilyDiamondRunningWater()
        .setFamilyId(familyId)
        .setMemberUserId(operatorUserId)
        .setSysOrigin(sysOrigin)
        .setOrigin(FamilyDiamondOrigin.EXCHANGE_TO_GOLD.name())
        .setOriginDesc(FamilyDiamondOrigin.EXCHANGE_TO_GOLD.getDesc())
        .setType(1) // 支出
        .setFamilyDiamondAmount(diamondAmount.negate()); // 负数表示支出

    save(water);
    log.info("记录工会钻石兑换金币流水: familyId={}, operatorUserId={}, diamondAmount={}", 
        familyId, operatorUserId, diamondAmount);
    
    return water;
  }

  @Override
  public FamilyDiamondRunningWater recordWithdrawDiamond(Long familyId, Long operatorUserId, String sysOrigin,
      BigDecimal diamondAmount) {
    
    FamilyDiamondRunningWater water = new FamilyDiamondRunningWater()
        .setFamilyId(familyId)
        .setMemberUserId(operatorUserId)
        .setSysOrigin(sysOrigin)
        .setOrigin(FamilyDiamondOrigin.WITHDRAW_DIAMOND.name())
        .setOriginDesc(FamilyDiamondOrigin.WITHDRAW_DIAMOND.getDesc())
        .setType(1) // 支出
        .setFamilyDiamondAmount(diamondAmount.negate()); // 负数表示支出

    save(water);
    log.info("记录工会钻石提现流水: familyId={}, operatorUserId={}, diamondAmount={}", 
        familyId, operatorUserId, diamondAmount);
    
    return water;
  }

  @Override
  public com.baomidou.mybatisplus.extension.plugins.pagination.Page<FamilyDiamondRunningWater> pageRunningWater(
      Long familyId, Integer type, Long memberUserId,
      java.time.LocalDateTime startTime, java.time.LocalDateTime endTime,
      int pageNum, int pageSize) {

    com.baomidou.mybatisplus.extension.plugins.pagination.Page<FamilyDiamondRunningWater> page =
        new com.baomidou.mybatisplus.extension.plugins.pagination.Page<>(pageNum, pageSize);

    return lambdaQuery()
        .eq(FamilyDiamondRunningWater::getFamilyId, familyId)
        .eq(type != null, FamilyDiamondRunningWater::getType, type)
        .eq(memberUserId != null, FamilyDiamondRunningWater::getMemberUserId, memberUserId)
        .ge(startTime != null, FamilyDiamondRunningWater::getCreateTime, startTime)
        .le(endTime != null, FamilyDiamondRunningWater::getCreateTime, endTime)
        .orderByDesc(FamilyDiamondRunningWater::getCreateTime)
        .page(page);
  }

}
