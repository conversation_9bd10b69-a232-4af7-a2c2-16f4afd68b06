package com.red.circle.wallet.app.dto.cmd.family;

import com.red.circle.common.business.dto.cmd.app.AppExtCommand;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 工会流水查询命令.
 *
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class FamilyRunningWaterQryCmd extends AppExtCommand {

  /**
   * 流水类型 diamond-钻石流水 gold-金币流水.
   */
  private String waterType;

  /**
   * 操作类型 0-收入 1-支出.
   */
  private Integer type;

  /**
   * 成员用户ID.
   */
  private Long memberUserId;

  /**
   * 开始时间 yyyy-MM-dd.
   */
  private String startDate;

  /**
   * 结束时间 yyyy-MM-dd.
   */
  private String endDate;

}
