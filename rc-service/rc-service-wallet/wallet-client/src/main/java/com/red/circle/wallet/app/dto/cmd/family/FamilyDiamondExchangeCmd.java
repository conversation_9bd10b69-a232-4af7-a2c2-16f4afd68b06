package com.red.circle.wallet.app.dto.cmd.family;

import com.red.circle.common.business.dto.cmd.app.AppExtCommand;
import java.math.BigDecimal;
import javax.validation.constraints.DecimalMin;
import javax.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 工会钻石兑换金币命令.
 *
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class FamilyDiamondExchangeCmd extends AppExtCommand {

  /**
   * 钻石数量.
   */
  @NotNull(message = "钻石数量不能为空")
  @DecimalMin(value = "0.01", message = "钻石数量必须大于0")
  private BigDecimal diamondAmount;

}
