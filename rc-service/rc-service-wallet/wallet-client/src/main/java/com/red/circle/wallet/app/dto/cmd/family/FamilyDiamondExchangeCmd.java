package com.red.circle.wallet.app.dto.cmd.family;

import com.red.circle.common.business.dto.cmd.AppExtCommand;
import java.math.BigDecimal;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 工会钻石兑换金币命令.
 *
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class FamilyDiamondExchangeCmd extends AppExtCommand {

  /**
   * 钻石数量.
   */
  private BigDecimal diamondAmount;

}
