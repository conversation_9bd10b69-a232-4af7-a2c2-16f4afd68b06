package com.red.circle.wallet.app.service.family;

import com.red.circle.common.business.dto.cmd.AppExtCommand;
import com.red.circle.framework.dto.PageResult;
import com.red.circle.wallet.app.dto.clientobject.family.FamilyDiamondRunningWaterCO;
import com.red.circle.wallet.app.dto.clientobject.family.FamilyGoldRunningWaterCO;
import com.red.circle.wallet.app.dto.clientobject.family.FamilyWalletBalanceCO;
import com.red.circle.wallet.app.dto.cmd.family.FamilyDiamondExchangeCmd;
import com.red.circle.wallet.app.dto.cmd.family.FamilyRunningWaterQryCmd;
import java.math.BigDecimal;

/**
 * 工会钱包应用服务.
 *
 * <AUTHOR>
 */
public interface FamilyWalletAppService {

  /**
   * 获取工会钱包余额.
   *
   * @param cmd 请求命令
   * @return 工会钱包余额
   */
  FamilyWalletBalanceCO getFamilyWalletBalance(AppExtCommand cmd);

  /**
   * 工会钻石兑换金币.
   *
   * @param cmd 兑换命令
   * @return 兑换得到的金币数量(元)
   */
  BigDecimal exchangeDiamondToGold(FamilyDiamondExchangeCmd cmd);

  /**
   * 分页查询工会钻石流水.
   *
   * @param cmd 查询命令
   * @return 钻石流水分页结果
   */
  PageResult<FamilyDiamondRunningWaterCO> pageFamilyDiamondRunningWater(FamilyRunningWaterQryCmd cmd);

  /**
   * 分页查询工会金币流水.
   *
   * @param cmd 查询命令
   * @return 金币流水分页结果
   */
  PageResult<FamilyGoldRunningWaterCO> pageFamilyGoldRunningWater(FamilyRunningWaterQryCmd cmd);

}
