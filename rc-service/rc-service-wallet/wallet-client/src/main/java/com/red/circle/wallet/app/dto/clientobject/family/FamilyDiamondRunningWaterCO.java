package com.red.circle.wallet.app.dto.clientobject.family;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.red.circle.framework.dto.ClientObject;
import java.io.Serial;
import java.math.BigDecimal;
import java.sql.Timestamp;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 工会钻石流水CO.
 *
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class FamilyDiamondRunningWaterCO extends ClientObject {

  @Serial
  private static final long serialVersionUID = 1L;

  /**
   * 流水ID.
   */
  @JsonSerialize(using = ToStringSerializer.class)
  private Long id;

  /**
   * 工会ID.
   */
  @JsonSerialize(using = ToStringSerializer.class)
  private Long familyId;

  /**
   * 关联成员用户ID.
   */
  @JsonSerialize(using = ToStringSerializer.class)
  private Long memberUserId;

  /**
   * 成员昵称.
   */
  private String memberNickname;

  /**
   * 成员头像.
   */
  private String memberAvatar;

  /**
   * 来源类型.
   */
  private String origin;

  /**
   * 来源描述.
   */
  private String originDesc;

  /**
   * 类型 0.收入 1.支出.
   */
  private Integer type;

  /**
   * 类型描述.
   */
  private String typeDesc;

  /**
   * 礼物金币价值(元).
   */
  private BigDecimal giftGoldValue;

  /**
   * 个人获得钻石数量.
   */
  private BigDecimal memberDiamondAmount;

  /**
   * 工会钻石变动数量.
   */
  private BigDecimal familyDiamondAmount;

  /**
   * 创建时间.
   */
  private Timestamp createTime;

}
