package com.red.circle.wallet.app.dto.clientobject.family;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.red.circle.framework.dto.ClientObject;
import java.io.Serial;
import java.math.BigDecimal;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 工会钱包余额CO.
 *
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class FamilyWalletBalanceCO extends ClientObject {

  @Serial
  private static final long serialVersionUID = 1L;

  /**
   * 工会ID.
   */
  @JsonSerialize(using = ToStringSerializer.class)
  private Long familyId;

  /**
   * 工会名称.
   */
  private String familyName;

  /**
   * 钻石余额.
   */
  private BigDecimal diamondBalance;

  /**
   * 金币余额(元).
   */
  private BigDecimal goldBalance;

  /**
   * 金币余额(分).
   */
  @JsonSerialize(using = ToStringSerializer.class)
  private Long goldBalancePenny;

}
