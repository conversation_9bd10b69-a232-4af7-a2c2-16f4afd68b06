package com.red.circle.wallet.app.asserts;


import com.red.circle.framework.dto.IResponseErrorCode;
import com.red.circle.framework.dto.ResErrorCode;

/**
 * 工会钱包错误码 范围：7000~7100.
 *
 * <AUTHOR>
 */
@ResErrorCode(describe = "工会钱包相关", minCode = 7000, maxCode = 7100)
public enum FamilyWalletErrorCode implements IResponseErrorCode {

  /**
   * 用户不属于任何工会.
   */
  USER_NOT_IN_FAMILY(7001, "User does not belong to any family"),

  /**
   * 只有工会长可以进行此操作.
   */
  ONLY_FAMILY_LEADER_CAN_OPERATE(7002, "Only family leader can perform this operation"),

  /**
   * 工会钻石余额不足.
   */
  FAMILY_DIAMOND_BALANCE_NOT_ENOUGH(7003, "Family diamond balance is not enough"),

  /**
   * 工会金币余额不足.
   */
  FAMILY_GOLD_BALANCE_NOT_ENOUGH(7004, "Family gold balance is not enough"),

  /**
   * 兑换金额必须大于0.
   */
  EXCHANGE_AMOUNT_MUST_GREATER_THAN_ZERO(7005, "Exchange amount must be greater than zero");

  private final int code;
  private final String message;

  FamilyWalletErrorCode(int code, String message) {
    this.code = code;
    this.message = message;
  }

  @Override
  public Integer getCode() {
    return code;
  }

  @Override
  public String getMessage() {
    return message;
  }

  @Override
  public String getErrorCodeName() {
    return "";
  }

}
