package com.red.circle.other.app.dto.clientobject.family;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.red.circle.framework.dto.ClientObject;
import java.io.Serial;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 工会宝箱奖励规则.
 * </p>
 *
 * <AUTHOR>
 * @since 2021-08-06
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class FamilyRewardRuleCO extends ClientObject {

  @Serial
  private static final long serialVersionUID = 1L;

  /**
   * 规则ID
   */
  @JsonSerialize(using = ToStringSerializer.class)
  private Long id;

  /**
   * 要求经验值.
   */
  private Integer goalExp;

  /**
   * 要求经验值.
   */
  private String goalExpStr;

  /**
   * 奖励金币数量.
   */
  private Integer rewardQuantity;

  /**
   * 奖励金币数量.
   */
  private String rewardQuantityStr;

  /**
   * 进度条百分比
   */
  private Double percentageExp;

}
