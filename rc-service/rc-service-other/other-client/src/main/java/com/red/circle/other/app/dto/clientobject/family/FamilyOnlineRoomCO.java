package com.red.circle.other.app.dto.clientobject.family;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.red.circle.framework.dto.ClientObject;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 我的工会.
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-20
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class FamilyOnlineRoomCO extends ClientObject {

  /**
   * 工会id.
   */
  @JsonSerialize(using = ToStringSerializer.class)
  private Long familyId;

  /**
   * 工会账号.
   */
  private Long familyAccount;

  /**
   * 工会头像.
   */
  private String familyAvatar;

  /**
   * 工会名称.
   */
  private String familyName;

  /**
   * 工会公告.
   */
  private String familyNotice;

  /**
   * 工会当前经验值
   */
  private Integer currentExp;

  /**
   * 工会当前成员数.
   */
  private Integer currentMember;

  /**
   * 工会等级key.
   */
  private String levelKey;

  /**
   * 等级经验值
   */
  private Integer levelExp;

  /**
   * 头像框.
   */
  private String avatarFrameCover;

  /**
   * 头像框效果.
   */
  private String avatarFrameSvg;

  /**
   * 徽章.
   */
  private String badgeCover;

  /**
   * 徽章效果.
   */
  private String badgeSvg;

  /**
   * 最大成员数.
   */
  private Integer maxMember;

  /**
   * 等级背景图.
   */
  private String levelBackgroundPicture;

}
