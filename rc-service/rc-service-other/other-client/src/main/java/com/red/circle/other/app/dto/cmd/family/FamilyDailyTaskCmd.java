package com.red.circle.other.app.dto.cmd.family;

import com.red.circle.common.business.dto.cmd.AppExtCommand;
import java.io.Serial;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 工会每日任务.
 *
 * <AUTHOR>
 * @since 2021-07-26
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class FamilyDailyTaskCmd extends AppExtCommand {

  @Serial
  private static final long serialVersionUID = 1L;

  /**
   * 任务Key.
   */
  private String taskType;

}
