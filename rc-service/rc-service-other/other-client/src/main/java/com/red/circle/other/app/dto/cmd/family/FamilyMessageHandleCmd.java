package com.red.circle.other.app.dto.cmd.family;

import com.red.circle.common.business.dto.cmd.AppExtCommand;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.io.Serial;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 处理工会消息.
 *
 * <AUTHOR>
 * @since 2021-07-20
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class FamilyMessageHandleCmd extends AppExtCommand {

  @Serial
  private static final long serialVersionUID = 1L;

  /**
   * 工会消息ID.
   *
   * @eo.required
   */
  @NotNull(message = "familyMessageId required.")
  private Long familyMessageId;

  /**
   * 业务事件.
   */
  @NotBlank(message = "event required.")
  private String event;

}
