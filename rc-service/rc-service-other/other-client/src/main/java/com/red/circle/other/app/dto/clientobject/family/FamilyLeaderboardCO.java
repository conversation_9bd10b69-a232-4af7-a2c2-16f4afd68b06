package com.red.circle.other.app.dto.clientobject.family;

import java.io.Serializable;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 工会排行榜 周榜、月榜、自己工会信息.
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-24
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class FamilyLeaderboardCO implements Serializable {

  private static final long serialVersionUID = 1L;
  /**
   * 周-自己工会贡献榜单.
   */
  private FamilyExpCO weekData;
  /**
   * 月-自己工会贡献榜单.
   */
  private FamilyExpCO monthData;
  /**
   * 周-工会贡献榜单.
   */
  private List<FamilyExpCO> weekDataList;
  /**
   * 月-工会贡献榜单.
   */
  private List<FamilyExpCO> monthDataList;


}
