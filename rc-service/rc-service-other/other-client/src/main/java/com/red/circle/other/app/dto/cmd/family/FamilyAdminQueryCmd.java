package com.red.circle.other.app.dto.cmd.family;

import com.red.circle.common.business.dto.cmd.AppExtCommand;
import java.io.Serial;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 根据工会ID返回这个工会的族长.
 *
 * <AUTHOR>
 * @since 2021-07-23
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class FamilyAdminQueryCmd extends AppExtCommand {

  @Serial
  private static final long serialVersionUID = 1L;

  /**
   * 工会ID.
   */
  private Long familyId;
}
