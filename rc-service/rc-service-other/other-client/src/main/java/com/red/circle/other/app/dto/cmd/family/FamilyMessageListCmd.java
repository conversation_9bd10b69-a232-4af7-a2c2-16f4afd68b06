package com.red.circle.other.app.dto.cmd.family;

import com.red.circle.common.business.dto.cmd.AppExtCommand;
import java.io.Serial;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 工会消息列表.
 *
 * <AUTHOR>
 * @since 2021-07-23
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class FamilyMessageListCmd extends AppExtCommand {

  @Serial
  private static final long serialVersionUID = 1L;

  /**
   * 最后一个消息ID.
   */
  private Long lastId;

}
