package com.red.circle.other.app.dto.cmd.family;

import com.red.circle.common.business.dto.cmd.AppExtCommand;
import jakarta.validation.constraints.NotNull;
import java.io.Serial;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 领取我的工会奖励.
 *
 * <AUTHOR>
 * @since 2021-08-06
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ReceiveFamilyAwardCmd extends AppExtCommand {

  @Serial
  private static final long serialVersionUID = 1L;

  /**
   * 奖励规则ID.
   */
  @NotNull(message = "awardRuleId required.")
  private Long awardRuleId;

}
