package com.red.circle.other.app.dto.clientobject.family;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 工会贡献值.
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-24
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class FamilyExpCO implements Serializable {

  private static final long serialVersionUID = 1L;

  /**
   * 经验值
   */
  private String exp;

  /**
   * 工会ID
   */
  @JsonSerialize(using = ToStringSerializer.class)
  private Long familyId;

  /**
   * 工会头像.
   */
  private String familyAvatar;

  /**
   * 工会名称.
   */
  private String familyName;

  /**
   * 工会公告.
   */
  private String familyNotice;

  /**
   * 等级KEY
   */
  private String levelKey;

  /**
   * 头像框.
   */
  private String avatarFrameCover;

  /**
   * 头像框效果.
   */
  private String avatarFrameSvg;

}
