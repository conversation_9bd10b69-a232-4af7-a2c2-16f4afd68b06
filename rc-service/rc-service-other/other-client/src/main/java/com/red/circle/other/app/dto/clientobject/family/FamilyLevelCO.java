package com.red.circle.other.app.dto.clientobject.family;

import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 工会等级.
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-20
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
public class FamilyLevelCO implements Serializable {

  private static final long serialVersionUID = 1L;

  /**
   * 等级key
   */
  private String levelKey;

  /**
   * 等级经验值
   */
  private Integer levelExp;

  /**
   * 头像框.
   */
  private String avatarFrameCover;

  /**
   * 头像框效果.
   */
  private String avatarFrameSvg;

  /**
   * 徽章.
   */
  private String badgeCover;

  /**
   * 徽章效果.
   */
  private String badgeSvg;

  /**
   * 礼物.
   */
  private String giftCover;

  /**
   * 礼物效果.
   */
  private String giftSvg;

  /**
   * 最大成员数.
   */
  private Integer maxMember;

  /**
   * 最大管理员数.
   */
  private Integer maxManager;

  /**
   * 等级背景图.
   */
  private String levelBackgroundPicture;

  /**
   * 等级类型(BLACK_IRON.黑铁 BRONZE.青铜 SILVER.白银 GOLD.黄金 PLATINUM.铂金).
   */
  private String levelType;

  /**
   * 等级顺序.
   */
  private Integer sort;

}
