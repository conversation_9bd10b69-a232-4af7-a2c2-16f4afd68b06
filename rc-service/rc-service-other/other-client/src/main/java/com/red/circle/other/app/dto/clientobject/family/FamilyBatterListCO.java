package com.red.circle.other.app.dto.clientobject.family;

import java.io.Serializable;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 工会batter.
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-10
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class FamilyBatterListCO implements Serializable {

  private static final long serialVersionUID = 1L;

  private List<FamilyBatterCO> nowRanking;

  private List<FamilyBatterCO> totalRanking;


}
