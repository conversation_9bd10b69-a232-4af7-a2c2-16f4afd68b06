package com.red.circle.other.app.service.family;


import com.red.circle.common.business.dto.cmd.AppExtCommand;
import com.red.circle.other.app.dto.clientobject.family.FamilyBaseInfoCO;
import com.red.circle.other.app.dto.clientobject.family.FamilyCreateRulesCO;
import com.red.circle.other.app.dto.clientobject.family.FamilyLeaderboardCO;
import com.red.circle.other.app.dto.clientobject.family.FamilyLevelParentCacheCO;
import com.red.circle.other.app.dto.clientobject.family.FamilyMemberCO;
import com.red.circle.other.app.dto.clientobject.family.FamilyMsgListCO;
import com.red.circle.other.app.dto.clientobject.family.FamilyRewardCO;
import com.red.circle.other.app.dto.clientobject.family.FamilyUserInfoCO;
import com.red.circle.other.app.dto.clientobject.room.RoomVoiceProfileCO;
import com.red.circle.other.app.dto.cmd.family.ApplyJoinFamilyCmd;
import com.red.circle.other.app.dto.cmd.family.FamilyAdminQueryCmd;
import com.red.circle.other.app.dto.cmd.family.FamilyBaseInfoQueryCmd;
import com.red.circle.other.app.dto.cmd.family.FamilyCreateCmd;
import com.red.circle.other.app.dto.cmd.family.FamilyDailyTaskCmd;
import com.red.circle.other.app.dto.cmd.family.FamilyGrantUserRoleCmd;
import com.red.circle.other.app.dto.cmd.family.FamilyGroupChatPayCheckCmd;
import com.red.circle.other.app.dto.cmd.family.FamilyGroupChatQueryCmd;
import com.red.circle.other.app.dto.cmd.family.FamilyInfoEditCmd;
import com.red.circle.other.app.dto.cmd.family.FamilyMemberListCmd;
import com.red.circle.other.app.dto.cmd.family.FamilyMemberListQueryCmd;
import com.red.circle.other.app.dto.cmd.family.FamilyMessageHandleCmd;
import com.red.circle.other.app.dto.cmd.family.FamilyMessageListCmd;
import com.red.circle.other.app.dto.cmd.family.FamilyOnlineRoomQueryCmd;
import com.red.circle.other.app.dto.cmd.family.FamilyRemoveUserCmd;
import com.red.circle.other.app.dto.cmd.family.FamilyUserInfoQueryCmd;
import com.red.circle.other.app.dto.cmd.family.ReceiveFamilyAwardCmd;
import java.math.BigDecimal;
import java.util.List;

/**
 * <p>
 * Family .
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-24
 */
public interface FamilyService {

  /**
   * 创建工会.
   */
  Long create(FamilyCreateCmd cmd);

  /**
   * 编辑工会信息.
   */
  void edit(FamilyInfoEditCmd cmd);

  /**
   * 工会族长信息(与工会成员信息 数据结构一致).
   */
  FamilyMemberCO getAdmin(FamilyAdminQueryCmd cmd);

  /**
   * 工会成员信息.
   */
  List<FamilyMemberCO> listMember(FamilyMemberListQueryCmd cmd);

  /**
   * 工会基本信息.
   */
  FamilyBaseInfoCO getBaseInfo(FamilyBaseInfoQueryCmd cmd);


  /**
   * 授权用户工会权限.
   */
  void grantUserRole(FamilyGrantUserRoleCmd cmd);

  /**
   * 工会排行榜 周榜、月榜、自己工会信息.
   */
  FamilyLeaderboardCO getLeaderboard(AppExtCommand cmd);

  /**
   * 全部等级配置集合.
   */
  List<FamilyLevelParentCacheCO> listLevelConfig(AppExtCommand cmd);

  /**
   * 工会消息处理.
   */
  void messageHandle(FamilyMessageHandleCmd cmd);

  /**
   * 工会消息列表.
   */
  List<FamilyMsgListCO> listMessage(FamilyMessageListCmd cmd);

  /**
   * 工会在线房间.
   */
  List<RoomVoiceProfileCO> listOnlineRoom(FamilyOnlineRoomQueryCmd cmd);

  /**
   * 将移除工会用户.
   */
  void removeUser(FamilyRemoveUserCmd cmd);

  /**
   * 用户主动退出工会.
   */
  void userExit(AppExtCommand cmd);

  /**
   * 成员在工会中的基本信息.
   */
  FamilyUserInfoCO memberUserInfo(FamilyUserInfoQueryCmd cmd);

  /**
   * 申请加入工会.
   */
  void applyJoin(ApplyJoinFamilyCmd cmd);

  /**
   * 触发工会每日任务
   */
  void triggerDailyTask(FamilyDailyTaskCmd cmd);

  /**
   * 创建工会规则
   */
  FamilyCreateRulesCO familyCreateRule(AppExtCommand cmd);

  /**
   * 我的工会宝箱奖励.
   */
  FamilyRewardCO myFamilyReward(AppExtCommand cmd);

  /**
   * 领取工会奖励.
   */
  void receiveFamilyAward(ReceiveFamilyAwardCmd cmd);

  /**
   * 解锁-工会群聊.
   */
  BigDecimal purchasingFamilyGroupChat(FamilyGroupChatQueryCmd cmd);

  /**
   * 解锁-工会群聊.
   */
  void dismissFamilyGroupChat(FamilyGroupChatQueryCmd cmd);

  /**
   * 工会群聊是否解锁.
   */
  Boolean getFamilyGroupChatIsPay(FamilyGroupChatPayCheckCmd cmd);

  /**
   * 工会成员信息-不分页.
   */
  List<FamilyMemberCO> getMembers(FamilyMemberListCmd cmd);
}
