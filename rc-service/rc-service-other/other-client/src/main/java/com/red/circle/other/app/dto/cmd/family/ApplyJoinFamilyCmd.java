package com.red.circle.other.app.dto.cmd.family;

import com.red.circle.common.business.dto.cmd.AppExtCommand;
import jakarta.validation.constraints.NotNull;
import java.io.Serial;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 申请加入工会.
 *
 * <AUTHOR>
 * @since 2021-07-26
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ApplyJoinFamilyCmd extends AppExtCommand {

  @Serial
  private static final long serialVersionUID = 1L;

  /**
   * 工会ID.
   *
   * @eo.required
   */
  @NotNull(message = "familyId required.")
  private Long familyId;

}
