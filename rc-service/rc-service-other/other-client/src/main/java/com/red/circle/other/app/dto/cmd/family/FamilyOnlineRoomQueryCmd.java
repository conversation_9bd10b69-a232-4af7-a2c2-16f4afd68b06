package com.red.circle.other.app.dto.cmd.family;

import com.red.circle.common.business.dto.cmd.AppExtCommand;
import jakarta.validation.constraints.NotNull;
import java.io.Serial;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 获得在线房间列表.
 *
 * <AUTHOR>
 * @since 2021-07-23
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class FamilyOnlineRoomQueryCmd extends AppExtCommand {

  @Serial
  private static final long serialVersionUID = 1L;

  /**
   * 工会ID.
   *
   * @eo.required
   */
  @NotNull(message = "familyId required.")
  private Long familyId;


}
