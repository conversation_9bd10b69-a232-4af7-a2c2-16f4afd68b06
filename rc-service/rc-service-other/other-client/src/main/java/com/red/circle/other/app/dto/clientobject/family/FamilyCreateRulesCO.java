package com.red.circle.other.app.dto.clientobject.family;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 工会创建要求(满足任意一个，那么用户就可以创建工会).
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-21
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class FamilyCreateRulesCO implements Serializable {

  @Serial
  private static final long serialVersionUID = 1L;

  /**
   * 支付糖果.
   */
  private BigDecimal payCandy;

  /**
   * 用户财富等级.
   */
  private Integer userWealthLevel;

}
