package com.red.circle.other.app.dto.clientobject.family;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import java.io.Serializable;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 工会batter.
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-10
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class FamilyBatterCO implements Serializable {

  private static final long serialVersionUID = 1L;

  /**
   * 工会ID
   */
  @JsonSerialize(using = ToStringSerializer.class)
  private Long familyId;

  /**
   * 贡献值
   */
  private Integer exp;

  /**
   * 贡献值
   */
  private String expStr;

  /**
   * 工会头像.
   */
  private String familyAvatar;

  /**
   * 工会名称.
   */
  private String familyName;

  /**
   * 成员.
   */
  private List<FamilyMemberCO> memberList;

}
