package com.red.circle.other.app.dto.cmd.family;

import com.red.circle.common.business.dto.cmd.AppExtCommand;
import jakarta.validation.constraints.NotNull;
import java.io.Serial;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 用户在工会中的基础信息查询.
 *
 * <AUTHOR>
 * @since 2021-07-20
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class FamilyUserInfoQueryCmd extends AppExtCommand {

  @Serial
  private static final long serialVersionUID = 1L;

  /**
   * 用户ID.
   *
   * @eo.required
   */
  @NotNull(message = "userId required.")
  private Long userId;

}
