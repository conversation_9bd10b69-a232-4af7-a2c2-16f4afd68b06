package com.red.circle.other.app.dto.cmd.family;

import com.red.circle.common.business.dto.cmd.AppExtCommand;
import java.io.Serial;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 修改工会信息.
 *
 * <AUTHOR>
 * @since 2021-07-22
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class FamilyInfoEditCmd extends AppExtCommand {

  @Serial
  private static final long serialVersionUID = 1L;

  /**
   * 工会头像.
   */
  private String familyAvatar;

  /**
   * 工会名称.
   */
  private String familyName;

  /**
   * 工会公告.
   */
  private String familyNotice;

}
