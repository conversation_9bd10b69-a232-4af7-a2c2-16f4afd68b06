package com.red.circle.other.app.dto.clientobject.family;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.red.circle.framework.dto.ClientObject;
import java.io.Serial;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 工会成员.
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-20
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
public class FamilyMemberCO extends ClientObject {

  @Serial
  private static final long serialVersionUID = 1L;

  /**
   * 工会ID.
   */
  @JsonSerialize(using = ToStringSerializer.class)
  private Long familyId;

  /**
   * 成员Id.
   */
  @JsonSerialize(using = ToStringSerializer.class)
  private Long memberId;

  /**
   * 成员UserId.
   */
  @JsonSerialize(using = ToStringSerializer.class)
  private Long memberUserId;

  /**
   * 成员头像
   */
  private String memberAvatar;

  /**
   * 成员昵称
   */
  private String memberNickname;

  /**
   * 成员年龄
   */
  private Integer memberAge;

  /**
   * 成员性别:0 女，1 男
   */
  private Boolean memberSex;

  /**
   * 成员贡献值(已格式化)
   */
  private String memberExp;

  /**
   * 成员角色
   */
  private String memberRole;

}
