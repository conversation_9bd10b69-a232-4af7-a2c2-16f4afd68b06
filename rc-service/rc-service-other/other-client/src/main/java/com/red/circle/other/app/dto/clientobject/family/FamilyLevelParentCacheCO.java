package com.red.circle.other.app.dto.clientobject.family;

import java.io.Serializable;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 工会等级.
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-31
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
public class FamilyLevelParentCacheCO implements Serializable {

  private static final long serialVersionUID = 1L;

  /**
   * 等级类型(BLACK_IRON.黑铁 BRONZE.青铜 SILVER.白银 GOLD.黄金 PLATINUM.铂金).
   */
  private String levelType;

  /**
   * 序号.
   */
  private Integer sort;

  /**
   * 等级集合
   */
  private List<FamilyLevelCO> level;

}
