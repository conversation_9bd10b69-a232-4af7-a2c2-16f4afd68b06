package com.red.circle.other.app.dto.clientobject.family;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.red.circle.framework.dto.ClientObject;
import java.io.Serial;
import java.math.BigDecimal;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 工会基本信息.
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-20
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class FamilyBaseInfoCO extends ClientObject {

  @Serial
  private static final long serialVersionUID = 1L;

  /**
   * 工会id.
   */
  @JsonSerialize(using = ToStringSerializer.class)
  private Long familyId;

  /**
   * 工会账号.
   */
  private Long familyAccount;

  /**
   * 工会头像.
   */
  private String familyAvatar;

  /**
   * 工会名称.
   */
  private String familyName;

  /**
   * 工会公告.
   */
  private String familyNotice;

  /**
   * 工会当前经验值
   */
  @JsonSerialize(using = ToStringSerializer.class)
  private Long currentExp;

  /**
   * 工会当前成员数.
   */
  private Integer currentMember;

  /**
   * 工会等级key.
   */
  private String levelKey;

  /**
   * 等级经验值
   */
  @JsonSerialize(using = ToStringSerializer.class)
  private Long levelExp;

  /**
   * 头像框.
   */
  private String avatarFrameCover;

  /**
   * 头像框效果.
   */
  private String avatarFrameSvg;

  /**
   * 徽章.
   */
  private String badgeCover;

  /**
   * 徽章效果.
   */
  private String badgeSvg;

  /**
   * 最大成员数.
   */
  private Integer maxMember;

  /**
   * 等级背景图.
   */
  private String levelBackgroundPicture;


  /**
   * 工会群聊付费解锁金额.
   */
  private BigDecimal amount;


  private String familyWhatapp;
  private String leaderIdCardPhoto;

}
