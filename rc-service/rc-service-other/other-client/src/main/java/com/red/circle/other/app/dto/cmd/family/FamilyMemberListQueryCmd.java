package com.red.circle.other.app.dto.cmd.family;

import com.red.circle.common.business.dto.cmd.AppExtCommand;
import java.io.Serial;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 获得工会成员列表.
 *
 * <AUTHOR>
 * @since 2021-07-23
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class FamilyMemberListQueryCmd extends AppExtCommand {

  @Serial
  private static final long serialVersionUID = 1L;

  /**
   * 工会ID.
   */
  private Long familyId;

  /**
   * 页码(务必从 0 开始)
   */
  private Integer pageNo;

  /**
   * 每页数量
   */
  private Integer pageSize;

}
