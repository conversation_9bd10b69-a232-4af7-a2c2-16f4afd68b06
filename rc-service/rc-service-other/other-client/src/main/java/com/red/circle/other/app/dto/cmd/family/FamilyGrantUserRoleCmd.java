package com.red.circle.other.app.dto.cmd.family;

import com.red.circle.common.business.dto.cmd.AppExtCommand;
import java.io.Serial;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 授权用户工会权限.
 *
 * <AUTHOR>
 * @since 2021-07-21
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class FamilyGrantUserRoleCmd extends AppExtCommand {

  @Serial
  private static final long serialVersionUID = 1L;

  /**
   * 被授权成员ID.
   */
  private Long authorizedMemberId;

  /**
   * 被授权成员ID.
   */
  private String roleKey;

}
