package com.red.circle.other.app.dto.cmd.family;

import com.red.circle.common.business.dto.cmd.AppExtCommand;
import jakarta.validation.constraints.NotBlank;
import java.io.Serial;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 用户工会基础信息查询.
 *
 * <AUTHOR>
 * @since 2021-07-20
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class FamilyCreateCmd extends AppExtCommand {

  @Serial
  private static final long serialVersionUID = 1L;

  /**
   * 工会头像.
   */
  private String familyAvatar;

  /**
   * 工会名称.
   *
   * @eo.required
   */
  @NotBlank(message = "familyName required.")
  private String familyName;

  /**
   * 工会公告.
   */
  private String familyNotice;


  private String familyWhatapp;
  private String leaderIdCardPhoto;

}
