package com.red.circle.other.app.dto.clientobject.family;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.red.circle.framework.dto.ClientObject;
import java.io.Serial;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 工会宝箱奖励规则.
 * </p>
 *
 * <AUTHOR>
 * @since 2021-08-06
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class FamilyRewardCO extends ClientObject {

  @Serial
  private static final long serialVersionUID = 1L;

  /**
   * 用户经验值.
   */
  @JsonSerialize(using = ToStringSerializer.class)
  private Long memberExp;

  /**
   * 用户离下一宝箱所差经验值.
   */
  @JsonSerialize(using = ToStringSerializer.class)
  private Long memberLackExp;

  /**
   * 离下一宝箱奖励可获得的奖励.
   */
  @JsonSerialize(using = ToStringSerializer.class)
  private Long rewardQuantity;

  /**
   * 本周开始结束时间
   */
  private String weekStartEnd;

  /**
   * 奖励规则
   */
  private List<FamilyRewardRuleCO> rewardRuleList;


}
