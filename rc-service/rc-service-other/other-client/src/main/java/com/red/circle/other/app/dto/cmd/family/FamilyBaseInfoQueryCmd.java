package com.red.circle.other.app.dto.cmd.family;

import com.red.circle.common.business.dto.cmd.AppExtCommand;
import java.io.Serial;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 工会基础信息查询.
 *
 * <AUTHOR>
 * @since 2021-07-20
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class FamilyBaseInfoQueryCmd extends AppExtCommand {

  @Serial
  private static final long serialVersionUID = 1L;

  /**
   * 工会ID
   */
  private Long familyId;

}
