package com.red.circle.other.app.manager.activity.award;

import com.alibaba.nacos.common.utils.JacksonUtils;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.red.circle.common.business.core.enums.SysOriginPlatformEnum;
import com.red.circle.common.business.enums.SendPropsOrigin;
import com.red.circle.other.app.convertor.material.GiftAppConvertor;
import com.red.circle.other.app.convertor.sys.ActivityAppConvertor;
import com.red.circle.other.app.dto.clientobject.activity.ActivityResourceCO;
import com.red.circle.other.app.dto.clientobject.gift.GiftConfigCO;
import com.red.circle.other.domain.gateway.props.ActivitySourceGroupGateway;
import com.red.circle.other.domain.gateway.user.ability.UserRegionGateway;
import com.red.circle.other.infra.common.activity.PropsActivitySendCommon;
import com.red.circle.other.infra.common.activity.send.SendRewardGroup;
import com.red.circle.other.infra.database.cache.service.user.CacheEnumConfigManagerService;
import com.red.circle.other.infra.database.mongo.entity.activity.AgentActivityCount;
import com.red.circle.other.infra.database.mongo.entity.activity.WeekStarGiftCount;
import com.red.circle.other.infra.database.mongo.entity.user.count.WeekCpValueCount;
import com.red.circle.other.infra.database.mongo.entity.user.count.WeekFriendshipCardCount;
import com.red.circle.other.infra.database.mongo.entity.user.region.SysRegionConfig;
import com.red.circle.other.infra.database.mongo.service.activity.AgentActivityCountService;
import com.red.circle.other.infra.database.mongo.service.activity.WeekStarGiftCountService;
import com.red.circle.other.infra.database.mongo.service.team.team.TeamProfileService;
import com.red.circle.other.infra.database.mongo.service.user.count.WeekCpValueCountService;
import com.red.circle.other.infra.database.mongo.service.user.count.WeekFriendshipCardCountService;
import com.red.circle.other.infra.database.mongo.service.user.region.SysRegionConfigService;
import com.red.circle.other.infra.database.rds.entity.activity.ActivityFriendshipCardHistory;
import com.red.circle.other.infra.database.rds.entity.activity.PropsActivityRuleConfig;
import com.red.circle.other.infra.database.rds.enums.OtherConfigEnum;
import com.red.circle.other.infra.database.rds.service.activity.ActivityFriendshipCardChampionService;
import com.red.circle.other.infra.database.rds.service.activity.ActivityFriendshipCardHistoryService;
import com.red.circle.other.infra.database.rds.service.activity.PropsActivityRuleConfigService;
import com.red.circle.other.infra.database.rds.service.gift.GiftConfigService;
import com.red.circle.other.infra.database.rds.service.sys.WeekStarGiftService;
import com.red.circle.other.infra.database.rds.service.sys.WeekStarGroupService;
import com.red.circle.other.infra.utils.DateFormatEnum;
import com.red.circle.other.infra.utils.ZonedDateTimeUtils;
import com.red.circle.other.inner.enums.activity.PropsActivityTypeEnum;
import com.red.circle.other.inner.enums.activity.WeekStarGiftTypeEnum;
import com.red.circle.other.inner.enums.config.EnumConfigKey;
import com.red.circle.other.inner.model.dto.activity.rule.CommonContent;
import com.red.circle.other.inner.model.dto.activity.rule.FriendshipCardContent;
import com.red.circle.tool.core.collection.CollectionUtils;
import com.red.circle.tool.core.date.LocalDateTimeUtils;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.ImmutablePair;
import org.apache.commons.lang3.tuple.ImmutableTriple;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.time.ZonedDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 每周奖励统一发送.
 *
 * <AUTHOR> on 2021/10/18
 */
@Slf4j
@Component
@AllArgsConstructor
public class WeeklyRewardsSentManager {

  private final ActivityAppConvertor activityMapper;
  private final PropsActivitySendCommon sendPropsManager;
  private final GiftConfigService giftConfigService;
  private final TeamProfileService teamProfileService;
  private final GiftAppConvertor appGiftConfigMapper;
  private final WeekStarGiftService weekStarGiftService;
  private final WeekStarGroupService weekStarGroupService;
  private final SysRegionConfigService readUserRegionManager;
  private final WeekCpValueCountService weekCpValueCountService;
  private final WeekStarGiftCountService weekStarGiftCountService;
  private final AgentActivityCountService agentActivityCountService;
  private final CacheEnumConfigManagerService cacheEnumConfigManagerService;
  private final ActivitySourceGroupGateway activitySourceGroupGateway;
  private final WeekFriendshipCardCountService weekFriendshipCardCountService;
  private final PropsActivityRuleConfigService propsActivityRuleConfigService;
  private final ActivityFriendshipCardChampionService activityFriendshipCardChampionService;
  private final ActivityFriendshipCardHistoryService activityFriendshipCardHistoryService;

  /**
   * 每周一发送奖励业务 - 已禁用：所有周奖励都已关闭.
   */
  public void weeklyRewardSend() {

    log.warn("[execute] 每周奖励统一发送 start:{} - 已禁用所有奖励", LocalDateTimeUtils.nowFormat("yyyy-MM-dd"));

    // 已禁用：所有周奖励都已关闭，只保留充值和币商获取金币/钻石
    /*
    SysOriginPlatformEnum.getVoiceSystems().forEach(sysOriginPlatformEnum -> {

      if (cacheEnumConfigManagerService.getValBool(OtherConfigEnum.SUSPEND_CONSUMPTION,
          sysOriginPlatformEnum.name())) {
        return;
      }

      // 特殊关系卡
      //processSendRewardFriendshipCard(sysOriginPlatformEnum);

      // 上周周星奖励
      sendLastWeekStar(sysOriginPlatformEnum);

      // CP
      processSendRewardCp(sysOriginPlatformEnum, null);

      // 代理活动 - 周
      //processSendRewardAgentActivity(sysOriginPlatformEnum, "WEEK", null);

    });
    */

    log.warn("[execute] 每周奖励统一发送 end:{} - 已禁用所有奖励", LocalDateTimeUtils.nowFormat("yyyy-MM-dd"));
  }

  /**
   * 发送代理活动奖励.
   */
  public void processSendRewardAgentActivity(SysOriginPlatformEnum origin, String group,
      String regionCode) {

    Integer nowDate = ZonedDateTimeUtils.nowAsiaRiyadhToInt();

    log.warn("[execute] {}-发送代理周活动奖励开始:{}", group, nowDate);

    try {

      // 获得可用区域列表
      List<SysRegionConfig> regionConfigs = getRegionConfigs(origin);
      if (CollectionUtils.isEmpty(regionConfigs)) {
        log.warn("[execute] {}-发送代理活动奖励:{} 没有区域", group, nowDate);
        return;
      }

      // 获得活动道具
      PropsActivityTypeEnum typeEnum = Objects.equals(group, "WEEK") ?
          PropsActivityTypeEnum.AGENT_ACTIVE_WEEK_REWARD
          : PropsActivityTypeEnum.AGENT_ACTIVE_MONTH_REWARD;
      List<PropsActivityRuleConfig> resources = listRuleConfigs(origin, typeEnum);
      if (CollectionUtils.isEmpty(resources) || resources.size() < 4) {
        log.warn("[execute] {}-发送代理活动奖励:{}, 资源数据不对", group, nowDate);
        return;
      }

      List<String> notRegions = Optional.ofNullable(
              cacheEnumConfigManagerService.getVal(EnumConfigKey.NOT_CP_AGENT_REWARD, origin.name()))
          .map(v -> List.of(v.split(",")))
          .orElse(Lists.newArrayList());

      // 将上周或上月的数据修改为历史
      agentActivityCountService.updateHistory(group);

      regionConfigs.forEach(region -> {

        String code = region.getRegionCode();
        if (StringUtils.isBlank(code) || notRegions.contains(code)) {
          return;
        }

        if (StringUtils.isNotBlank(regionCode)) {
          if (Boolean.FALSE.equals(Objects.equals(regionCode, code))) {
            return;
          }
        }

        List<AgentActivityCount> top10Team = listTop10Team(group, region);
        if (CollectionUtils.isEmpty(top10Team)) {
          log.warn("[execute] {}-发送代理活动奖励:{}, 区域:{}, 没有榜单", group, nowDate,
              region.getRegionCode());
          return;
        }

        Map<Long, Long> mapOwnUserIds = mapOwnUserIds(top10Team);
        if (CollectionUtils.isEmpty(top10Team)) {
          log.warn("[execute] {}-发送代理活动奖励:{}, 区域:{}, 没有有效榜单", group, nowDate,
              region.getRegionCode());
          return;
        }
        log.warn("[execute] {}-发送代理活动奖励:{}, 区域:{}, 用户数量:{}", group, nowDate,
            region.getRegionCode(),
            mapOwnUserIds.size());

        // 标记本次top1-3
        Set<String> top3Ids = Sets.newHashSet();

        for (int index = 0; index < top10Team.size(); index++) {

          AgentActivityCount count = top10Team.get(index);
          // 发送奖励
          PropsActivityRuleConfig resource = index <= 2 ? resources.get(index) : resources.get(3);

          CommonContent content = CommonContent.serializable(resource.getJsonData());

          sendPropsManager.sendActivityGroupBySpecial(SendRewardGroup.builder()
              .trackId(resource.getId())
              .sysOrigin(origin)
              .acceptUserId(mapOwnUserIds.get(count.getTeamId()))
              .resourceGroupId(resource.getResourceGroupId())
              .origin(SendPropsOrigin.valueOf(typeEnum.name()))
              .build(), Optional.ofNullable(count.getTarget()).orElse(0L) >= Optional
              .ofNullable(content.getQuantity()).orElse(0L));

          // 获得top3
          if (index < 3) {
            top3Ids.add(count.getId());
          }

        }

        // 修改历史标记
        agentActivityCountService.updateHistoryTopUserByIds(top3Ids);

      });


    } catch (Exception ex) {
      log.warn("{}-发送代理活动奖励BUG:{}.系统:{}", group, ex.getMessage(), origin.name());
    }

    log.warn("[execute] {}-发送代理活动奖励结束:{}", group,
        LocalDateTimeUtils.nowFormat("yyyy-MM-dd"));
  }

  private Map<Long, Long> mapOwnUserIds(List<AgentActivityCount> top10Team) {

    return teamProfileService
        .mapOwnUserIdsByIds(top10Team.stream().map(AgentActivityCount::getTeamId).collect(
            Collectors.toSet()));
  }

  private List<AgentActivityCount> listTop10Team(String group, SysRegionConfig region) {
    return agentActivityCountService.top10Team(group, region.getId());
  }

  private List<SysRegionConfig> getRegionConfigs(SysOriginPlatformEnum origin) {
    return readUserRegionManager.listAvailable(origin.name());
  }

  /**
   * 发送用户友谊关系卡奖励.
   */
  private void processSendRewardFriendshipCard(SysOriginPlatformEnum origin) {

    log.warn("[execute] 发送用户关系卡奖励开始:{}.系统:{}",
        LocalDateTimeUtils.nowFormat("yyyy-MM-dd"),
        origin.name());

    try {

      List<WeekFriendshipCardCount> lastWeekTop3 = weekFriendshipCardCountService
          .listLastWeekTop(origin.name(), 5);

      if (CollectionUtils.isEmpty(lastWeekTop3)) {
        log.warn("[execute] 上周top3不存在 - 发送用户关系卡奖励结束:{}.系统:{}",
            LocalDateTimeUtils.nowFormat("yyyy-MM-dd"), origin.name());
        return;
      }

      List<PropsActivityRuleConfig> resources = listRuleConfigs(origin,
          PropsActivityTypeEnum.ACTIVITY_FRIENDSHIP_CARD_REWARDS);
      if (CollectionUtils.isEmpty(resources)) {
        return;
      }

      int sourceSize = resources.size();
      int countSize = lastWeekTop3.size();
      for (int index = 0; index < countSize; index++) {

        if (index >= sourceSize) {
          log.error("用户关系卡奖品发放，奖品数量不足.系统:{}", origin.name());
          return;
        }

        PropsActivityRuleConfig ruleConfig = resources.get(index);
        WeekFriendshipCardCount topUser = lastWeekTop3.get(index);
        if (Objects.isNull(ruleConfig) || Objects.isNull(topUser)) {
          continue;
        }

        //给非第一名发奖励
        if (index > 2) {
          //后面两个奖励组为白银，黄金冠军奖励。已在index=0时处理了，后续无需处理
          return;
        }
        if (index > 0) {
          //top2, 3奖励
          sendFriendshipCardProps(origin, ruleConfig, topUser);
          continue;
        }

        //给第一名发奖励并校验是否发送升级奖励
        ImmutableTriple<Long, Long, String> userIdPair = weekFriendshipCardCountService
            .parseUserIdPair(topUser.getId());

        //获得第一名得冠军次数
        long userOneChampionCount = (long) getChampionFrequencyCount(userIdPair.getLeft()) + 1;
        long userTwoChampionCount = (long) getChampionFrequencyCount(userIdPair.getMiddle()) + 1;

        //累加获得冠军次数,保存周冠军记录.
        saveWeekFriendshipCardChampion(userIdPair, topUser.getQuantity(), origin.name());

        //是否已发送
        boolean userOneCompleted = false;
        boolean userTwoCompleted = false;

        if (sourceSize > 3) {

          // for 里面发送白银与黄金奖励
          for (int i = 1; i <= sourceSize - 3; i++) {

            //两位冠军奖励是否都已发送完成
            if (userOneCompleted && userTwoCompleted) {
              return;
            }

            //最强奖励
            PropsActivityRuleConfig superReward = resources.get(sourceSize - i);
            boolean isUserOneSend = isSendSuperReward(superReward.getJsonData(),
                userOneChampionCount);
            boolean isUserTwoSend = isSendSuperReward(superReward.getJsonData(),
                userTwoChampionCount);

            if (isUserOneSend && !userOneCompleted) {
              sendReward(origin, superReward, userIdPair.getLeft(),
                  SendPropsOrigin.ACTIVITY_FRIENDSHIP_CARD_REWARDS);
              userOneCompleted = true;
            }

            if (isUserTwoSend && !userTwoCompleted) {
              sendReward(origin, superReward, userIdPair.getMiddle(),
                  SendPropsOrigin.ACTIVITY_FRIENDSHIP_CARD_REWARDS);
              userTwoCompleted = true;
            }
          }
          //如没有满足发放白银与黄金奖励要求则发送普通冠军奖励
          if (!userOneCompleted) {
            sendReward(origin, resources.get(0), userIdPair.getLeft(),
                SendPropsOrigin.ACTIVITY_FRIENDSHIP_CARD_REWARDS);
          }

          if (!userTwoCompleted) {
            sendReward(origin, resources.get(0), userIdPair.getMiddle(),
                SendPropsOrigin.ACTIVITY_FRIENDSHIP_CARD_REWARDS);
          }
        }
      }
    } catch (Exception ex) {
      log.warn("发送用户关系卡奖励BUG:{}.系统:{}", ex.getMessage(), origin.name());
    }

    log.warn("[execute] 发送用户关系卡奖励结束:{}.系统:{}",
        LocalDateTimeUtils.nowFormat("yyyy-MM-dd"),
        origin.name());

  }

  /**
   * 发送上周周星奖励.
   */
  public void sendLastWeekStar(SysOriginPlatformEnum sysOrigin) {

    List<GiftConfigCO> gifts = lastWeekStarGift(sysOrigin);
    if (CollectionUtils.isEmpty(gifts) || gifts.size() < 3) {
      log.warn("周星奖励主动发送:活动礼物不是三个，不发送任何奖励.系统:{}", sysOrigin.name());
      return;
    }
    log.warn("上周三个周星礼物: {}.系统:{}", JacksonUtils.toJson(gifts), sysOrigin.name());

    List<ActivityResourceCO> activityResources = activityMapper
        .toListActivityResourceCO(activitySourceGroupGateway
            .listActivityResource(sysOrigin, PropsActivityTypeEnum.STAR));
    if (CollectionUtils.isEmpty(activityResources) || activityResources.size() < 12) {
      log.warn("周星奖励主动发送:规则配置没有12组，不发送任何奖励.系统:{}", sysOrigin.name());
      return;
    }
    List<List<ActivityResourceCO>> listResources = Lists
        .partition(activityResources.stream().filter(activityResourceCO ->
            activityResourceCO.getRule().getSort() > 3).collect(Collectors.toList()), 3);

    // 将三组奖励循环
    for (int resourcesIndex = 0; resourcesIndex < listResources.size(); resourcesIndex++) {

      // 礼物i - Top成员
      List<WeekStarGiftCount> giftUsers = weekStarGiftCountService
          .listLastWeekTop(sysOrigin.name(), gifts.get(resourcesIndex).getId(), 3);

      if (CollectionUtils.isEmpty(giftUsers)) {
        log.warn("周星奖励主动发送:活动礼物序号{}， 活动礼物ID: {},没有获奖用户.系统:{}",
            resourcesIndex + 1,
            gifts.get(resourcesIndex).getId(), sysOrigin.name());
        continue;
      }
      // 奖励
      List<ActivityResourceCO> resourcesList = listResources.get(resourcesIndex);

      // 给具体用户发具体奖励组
      for (int userIndex = 0; userIndex < giftUsers.size(); userIndex++) {

        // 获得具体奖励与具体用户
        ActivityResourceCO detailedAward = resourcesList.get(userIndex);
        if (Objects.isNull(detailedAward)) {
          log.warn("周星奖励主动发送:活动礼物{},top{} 配置不存在.系统:{}", resourcesIndex + 1,
              userIndex + 1,
              sysOrigin.name());
          continue;
        }
        WeekStarGiftCount user = giftUsers.get(userIndex);
        if (Objects.isNull(user)) {
          log.warn("周星奖励主动发送:活动礼物{},top{} 用户不存在.系统:{}", resourcesIndex + 1,
              userIndex + 1,
              sysOrigin.name());
          continue;
        }

        // 发送奖励
        sendPropsManager.sendActivityGroup(SendRewardGroup.builder()
            .trackId(detailedAward.getRule().getId())
            .sysOrigin(sysOrigin)
            .acceptUserId(user.getUserId())
            .resourceGroupId(detailedAward.getRule().getResourceGroupId())
            .origin(SendPropsOrigin.WEEK_STAR)
            .build());
        log.warn("周星奖励主动发送:活动礼物{},top{} 用户:{} 奖励已发送.系统:{}", resourcesIndex + 1,
            userIndex + 1,
            user.getUserId(), sysOrigin.name());
      }
    }
  }

  /**
   * 获得上周周星礼物.
   */
  private List<GiftConfigCO> lastWeekStarGift(SysOriginPlatformEnum sysOrigin) {
    return weekStarGroupService.getByTypeOne(sysOrigin, WeekStarGiftTypeEnum.LAST_WEEK)
        .map(weekStarGroup -> appGiftConfigMapper.toGiftConfigCO(giftConfigService
            .listByIds(weekStarGiftService.listGiftIdsByGroupId(weekStarGroup.getId()))))
        .orElse(Lists.newArrayList());
  }

  /**
   * 是否发送奖励.
   */
  private Boolean isSendSuperReward(String jsonData, Long userCount) {

    return userCount >= FriendshipCardContent
        .toFriendshipCardContent(jsonData)
        .getQuantity();
  }

  /**
   * 将冠军次数+1，同时保存冠军用户.
   */
  private void saveWeekFriendshipCardChampion(ImmutableTriple<Long, Long, String> userIdPair,
      Long quantity, String sysOrigin) {

    //冠军次数
    activityFriendshipCardChampionService.save(userIdPair.getLeft());
    activityFriendshipCardChampionService.save(userIdPair.getMiddle());
    //上周一
    ZonedDateTime lastWeekMonday = ZonedDateTimeUtils.getAsiaRiyadhLastWeekMonday();
    //上周天
    ZonedDateTime lastWeekSunday = ZonedDateTimeUtils.getAsiaRiyadhLastWeekSunday();

    //保存冠军记录
    activityFriendshipCardHistoryService
        .save(ActivityFriendshipCardHistory.builder()
            .sysOrigin(sysOrigin)
            .cardType(userIdPair.getRight())
            .userId(userIdPair.getMiddle())
            .twoUserId(userIdPair.getLeft())
            .quantity(BigDecimal.valueOf(quantity))
            .period(ZonedDateTimeUtils.format(lastWeekMonday, DateFormatEnum.MM_point_dd)
                .concat(" - ")
                .concat(ZonedDateTimeUtils.format(lastWeekSunday, DateFormatEnum.MM_point_dd)))
            .build());

  }


  private int getChampionFrequencyCount(Long userId) {
    return activityFriendshipCardChampionService.getChampionFrequencyByUserId(userId);
  }

  /**
   * 发送上周CP.
   */
  public void processSendRewardCp(SysOriginPlatformEnum sysOrigin, String regionCode) {

    log.warn("[execute] 发送CP奖励开始:{}.系统:{}", LocalDateTimeUtils.nowFormat("yyyy-MM-dd"),
        sysOrigin.name());

    try {

      List<SysRegionConfig> sysRegionConfigs = getRegionConfigs(sysOrigin);
      if (CollectionUtils.isEmpty(sysRegionConfigs)) {
        return;
      }

      List<String> notRegions = Optional.ofNullable(
              cacheEnumConfigManagerService.getVal(EnumConfigKey.NOT_CP_AGENT_REWARD,
                  sysOrigin.name()))
          .map(v -> List.of(v.split(",")))
          .orElse(Lists.newArrayList());

      for (int i = 0; i < sysRegionConfigs.size(); i++) {

        String code = sysRegionConfigs.get(i).getRegionCode();
        // 因为TR与AR发一份奖励, 所以AR发了TR就不用再发了(TR与AR现在是一个榜单)
        if (StringUtils.isBlank(code) || notRegions.contains(code) || Objects.equals(code, "TR")) {
          continue;
        }

        if (StringUtils.isNotBlank(regionCode)) {
          if (Boolean.FALSE.equals(Objects.equals(regionCode, code))) {
            continue;
          }
        }

        List<WeekCpValueCount> weekCpValueCounts = weekCpValueCountService.listLastWeekTop(
            sysOrigin.name(), code, 3);

        if (CollectionUtils.isEmpty(weekCpValueCounts)) {
          continue;
        }

        List<PropsActivityRuleConfig> resources = listRuleConfigs(sysOrigin,
            PropsActivityTypeEnum.WEEK_CP_GIFT);
        if (CollectionUtils.isEmpty(resources)) {
          return;
        }

        int sourceSize = resources.size();
        int countSize = weekCpValueCounts.size();
        for (int index = 0; index < countSize; index++) {

          if (index >= sourceSize) {
            log.error("CP奖品发放，奖品数量不足.系统:{}", sysOrigin.name());
            return;
          }

          PropsActivityRuleConfig ruleConfig = resources.get(index);
          WeekCpValueCount topUser = weekCpValueCounts.get(index);
          if (Objects.isNull(ruleConfig) || Objects.isNull(topUser)) {
            continue;
          }

          sendCpProps(sysOrigin, ruleConfig, topUser);
        }

      }

    } catch (Exception ex) {
      log.warn("发送CP奖励BUG:{}.系统:{}", ex.getMessage(), sysOrigin.name());
    }

    log.warn("[execute] 发送CP奖励结束:{}.系统:{}", LocalDateTimeUtils.nowFormat("yyyy-MM-dd"),
        sysOrigin.name());

  }

  private void sendReward(SysOriginPlatformEnum origin, PropsActivityRuleConfig resources,
      Long userId, SendPropsOrigin groupOrigin) {

    if (Objects.isNull(resources) || Objects.isNull(userId)) {
      return;
    }

    sendPropsManager.sendActivityGroup(SendRewardGroup.builder()
        .trackId(resources.getId())
        .sysOrigin(origin)
        .acceptUserId(userId)
        .resourceGroupId(resources.getResourceGroupId())
        .origin(groupOrigin)
        .build());
  }

  private List<PropsActivityRuleConfig> listRuleConfigs(SysOriginPlatformEnum sysOrigin,
      PropsActivityTypeEnum typeEnum) {

    return propsActivityRuleConfigService.listPlatformByActivityType(sysOrigin.getSysOrigin(), typeEnum);
  }

  private void sendCpProps(SysOriginPlatformEnum origin, PropsActivityRuleConfig ruleConfig,
      WeekCpValueCount topUser) {

    ImmutablePair<Long, Long> userIdPair = weekCpValueCountService.parseUserIdPair(topUser.getId());

    sendPropsManager.sendActivityGroup(SendRewardGroup.builder()
        .trackId(ruleConfig.getId())
        .sysOrigin(origin)
        .acceptUserId(userIdPair.getLeft())
        .resourceGroupId(ruleConfig.getResourceGroupId())
        .origin(SendPropsOrigin.WEEK_CP_GIFT)
        .build());

    sendPropsManager.sendActivityGroup(SendRewardGroup.builder()
        .trackId(ruleConfig.getId())
        .sysOrigin(origin)
        .acceptUserId(userIdPair.getRight())
        .resourceGroupId(ruleConfig.getResourceGroupId())
        .origin(SendPropsOrigin.WEEK_CP_GIFT)
        .build());
  }

  private void sendFriendshipCardProps(SysOriginPlatformEnum origin,
      PropsActivityRuleConfig ruleConfig, WeekFriendshipCardCount topUser) {

    ImmutableTriple<Long, Long, String> userIdPair = weekFriendshipCardCountService
        .parseUserIdPair(topUser.getId());

    sendPropsManager.sendActivityGroup(SendRewardGroup.builder()
        .trackId(ruleConfig.getId())
        .sysOrigin(origin)
        .acceptUserId(userIdPair.getLeft())
        .resourceGroupId(ruleConfig.getResourceGroupId())
        .origin(SendPropsOrigin.ACTIVITY_FRIENDSHIP_CARD_REWARDS)
        .build());

    sendPropsManager.sendActivityGroup(SendRewardGroup.builder()
        .trackId(ruleConfig.getId())
        .sysOrigin(origin)
        .acceptUserId(userIdPair.getMiddle())
        .resourceGroupId(ruleConfig.getResourceGroupId())
        .origin(SendPropsOrigin.ACTIVITY_FRIENDSHIP_CARD_REWARDS)
        .build());
  }


}
