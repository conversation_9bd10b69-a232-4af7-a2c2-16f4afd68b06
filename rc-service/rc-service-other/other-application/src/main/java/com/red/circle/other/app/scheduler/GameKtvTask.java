package com.red.circle.other.app.scheduler;

import com.red.circle.common.business.core.enums.SysOriginPlatformEnum;
import com.red.circle.common.business.enums.SendPropsOrigin;
import com.red.circle.component.redis.annotation.TaskCacheLock;
import com.red.circle.other.domain.gateway.props.ActivitySourceGroupGateway;
import com.red.circle.other.domain.gateway.user.ability.UserRegionGateway;
import com.red.circle.other.domain.model.user.ability.RegionConfig;
import com.red.circle.other.infra.common.activity.PropsActivitySendCommon;
import com.red.circle.other.infra.common.activity.send.SendRewardGroup;
import com.red.circle.other.infra.database.cache.service.user.CacheEnumConfigManagerService;
import com.red.circle.other.infra.database.mongo.entity.game.ktv.GameKtvUserWeekRankCount;
import com.red.circle.other.infra.database.mongo.service.game.GameKtvUserWeekRankCountService;
import com.red.circle.other.infra.database.rds.enums.OtherConfigEnum;
import com.red.circle.other.inner.enums.activity.PropsActivityTypeEnum;
import com.red.circle.tool.core.collection.CollectionUtils;
import com.red.circle.tool.core.sequence.IdWorkerUtils;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

/**
 * KTV调器度 - 每周K歌人气榜.
 *
 * <AUTHOR> on 2023/5/8.
 */
@Slf4j
@Component
@AllArgsConstructor
public class GameKtvTask {

  private final PropsActivitySendCommon sendPropsManager;
  private final UserRegionGateway readUserRegionManager;
  private final CacheEnumConfigManagerService cacheEnumConfigManagerService;
  private final ActivitySourceGroupGateway activitySourceGroupRepository;
  private final GameKtvUserWeekRankCountService gameKtvUserWeekRankCountService;

  /**
   * 支持系统：tarab. 每周一0点执行.
   */
//  @Scheduled(cron = "0 0 0 ? * MON", zone = "Asia/Riyadh")
  @TaskCacheLock(key = "GAME_KTV_WEEK_RANK_TASK", expireSecond = 7200)
  public void gameKtvWeekRankTask(){
    gameKtvTarabWeekRankTask();
  }

  public void gameKtvTarabWeekRankTask(){
    long startTime = System.currentTimeMillis();
    log.info("exec game_ktv_week_rank_task start_2fun");
    List<RegionConfig> regions = readUserRegionManager
            .listRegionConfigBySysOrigin(SysOriginPlatformEnum.TARAB.name());
    if (CollectionUtils.isEmpty(regions)) {
      log.warn("每周K歌人气榜定时器:tarab系统不存在区域");
      return;
    }

    regions.forEach(region -> {

      log.warn("每周K歌人气榜定时器:tarab区域{}", region.getRegionCode());

      List<GameKtvUserWeekRankCount> top10List = gameKtvUserWeekRankCountService
              .lastWeekTop10(SysOriginPlatformEnum.TARAB.name(), region.getId());
      if (CollectionUtils.isEmpty(top10List)) {

        log.warn("每周K歌人气榜定时器:tarab区域{}不存在TOP10榜单用户", region.getRegionCode());
        return;
      }

      List<Long> groupIds = activitySourceGroupRepository
              .listActivityResourceGroupIds(SysOriginPlatformEnum.TARAB,
                      PropsActivityTypeEnum.GAME_KTV_WEEK_RANK_REWARD);
      if (CollectionUtils.isEmpty(groupIds) || groupIds.size() != 4) {
        log.warn("tarab每周K歌人气榜定时器:没有奖品信息或资源配置错误");
        return;
      }

      if (cacheEnumConfigManagerService.getValBool(OtherConfigEnum.SUSPEND_CONSUMPTION,
              SysOriginPlatformEnum.TARAB.name())) {
        return;
      }

      for (int index = 0; index < top10List.size(); index++) {

        sendPropsManager.sendActivityGroup(SendRewardGroup.builder()
                .resourceGroupId(index >= 3 ? groupIds.get(3) : groupIds.get(index))
                .trackId(IdWorkerUtils.getId())
                .acceptUserId(top10List.get(index).getUserId())
                .origin(SendPropsOrigin.GAME_KTV_WEEK_RANK_REWARD)
                .sysOrigin(SysOriginPlatformEnum.TARAB)
                .build());

      }

    });
    log.info("exec game_ktv_week_rank_task end with_2fun {}",System.currentTimeMillis()-startTime);
  }


}

