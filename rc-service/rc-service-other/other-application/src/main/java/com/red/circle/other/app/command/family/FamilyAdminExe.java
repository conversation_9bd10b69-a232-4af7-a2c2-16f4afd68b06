package com.red.circle.other.app.command.family;

import com.red.circle.framework.core.asserts.ResponseAssert;
import com.red.circle.other.app.convertor.user.UserProfileAppConvertor;
import com.red.circle.other.app.dto.clientobject.family.FamilyMemberCO;
import com.red.circle.other.app.dto.cmd.family.FamilyAdminQueryCmd;
import com.red.circle.other.domain.gateway.user.UserProfileGateway;
import com.red.circle.other.infra.common.family.FamilyCommon;
import com.red.circle.other.infra.database.rds.entity.family.FamilyMemberInfo;
import com.red.circle.other.infra.database.rds.entity.family.FamilyMemberWeekExp;
import com.red.circle.other.infra.database.rds.service.family.FamilyMemberInfoService;
import com.red.circle.other.infra.database.rds.service.family.FamilyMemberWeekExpService;
import com.red.circle.other.infra.enums.family.FamilyRoleEnum;
import com.red.circle.other.inner.asserts.FamilyErrorCode;
import com.red.circle.other.inner.model.dto.user.UserProfileDTO;
import com.red.circle.tool.core.num.NumUtils;
import java.util.Objects;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

/**
 * <p>
 * 获得工会族长.
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-23
 */
@Component
@RequiredArgsConstructor
public class FamilyAdminExe {

  private final FamilyCommon familyCommon;
  private final UserProfileGateway userProfileGateway;
  private final UserProfileAppConvertor userProfileAppConvertor;
  private final FamilyMemberInfoService familyMemberInfoService;
  private final FamilyMemberWeekExpService familyMemberWeekExpService;

  public FamilyMemberCO execute(FamilyAdminQueryCmd cmd) {

    ResponseAssert.isTrue(FamilyErrorCode.NOT_EXIST_FAMILY_INFO_DATA, getExistFamilyById(cmd));

    FamilyMemberInfo member = getAdmin(cmd);

    FamilyMemberWeekExp memberLog = getFamilyMemberWeekExp(member);

    UserProfileDTO user = getUserBaseInfo(member);

    return getFamilyMemberCO(member, memberLog, user);
  }

  private Boolean getExistFamilyById(FamilyAdminQueryCmd cmd) {
    return familyCommon.isExistFamilyById(cmd.getFamilyId());
  }

  private FamilyMemberCO getFamilyMemberCO(FamilyMemberInfo member, FamilyMemberWeekExp memberLog,
      UserProfileDTO user) {

    return new FamilyMemberCO()
        .setFamilyId(member.getFamilyId())
        .setMemberId(member.getId())
        .setMemberUserId(member.getMemberUserId())
        .setMemberAvatar(user.getUserAvatar())
        .setMemberNickname(user.getUserNickname())
        .setMemberAge(user.getAge())
        .setMemberSex(Objects.equals(user.getUserSex(), 1))
        .setMemberExp(NumUtils.formatLong(Objects.nonNull(memberLog) ? memberLog.getExp() : 0))
        .setMemberRole(member.getMemberRole());
  }

  private UserProfileDTO getUserBaseInfo(FamilyMemberInfo familyMemberInfo) {
    return userProfileAppConvertor.toUserProfileDTO(
        userProfileGateway.getByUserId(familyMemberInfo.getMemberUserId()));
  }

  private FamilyMemberWeekExp getFamilyMemberWeekExp(FamilyMemberInfo familyMemberInfo) {
    return familyMemberWeekExpService
        .getFamilyMemberWeekExp(familyMemberInfo.getFamilyId(), familyMemberInfo.getMemberUserId());
  }

  private FamilyMemberInfo getAdmin(FamilyAdminQueryCmd cmd) {
    FamilyMemberInfo familyMemberInfo = familyMemberInfoService
        .getAdmin(cmd.getFamilyId(), FamilyRoleEnum.ADMIN.getKey());
    ResponseAssert.notNull(FamilyErrorCode.NOT_EXIST_FAMILY_INFO_DATA, familyMemberInfo);
    return familyMemberInfo;
  }


}
