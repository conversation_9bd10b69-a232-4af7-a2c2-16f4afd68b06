package com.red.circle.other.app.command.family;

import com.red.circle.common.business.core.ImageSizeConst;
import com.red.circle.common.business.core.SensitiveWordFilter;
import com.red.circle.common.business.core.enums.DataApprovalTypeEnum;
import com.red.circle.external.inner.endpoint.oss.OssServiceClient;
import com.red.circle.framework.core.asserts.ResponseAssert;
import com.red.circle.framework.core.response.CommonErrorCode;
import com.red.circle.other.app.dto.cmd.family.FamilyInfoEditCmd;
import com.red.circle.other.infra.common.family.FamilyCommon;
import com.red.circle.other.infra.database.rds.entity.family.FamilyBaseInfo;
import com.red.circle.other.infra.database.rds.entity.family.FamilyMemberInfo;
import com.red.circle.other.infra.database.rds.service.approval.ApprovalUserSettingDataService;
import com.red.circle.other.infra.database.rds.service.family.FamilyBaseInfoService;
import com.red.circle.other.infra.database.rds.service.family.FamilyMemberInfoService;
import com.red.circle.other.inner.asserts.DynamicErrorCode;
import com.red.circle.other.inner.asserts.FamilyErrorCode;
import com.red.circle.tool.core.text.StringUtils;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

/**
 * <p>
 * 编辑工会信息.
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-22
 */
@Component
@RequiredArgsConstructor
public class FamilyInfoEditExe {

  private final FamilyCommon familyCommon;
  private final OssServiceClient ossServiceClient;
  private final FamilyBaseInfoService familyBaseInfoService;
  private final FamilyMemberInfoService familyMemberInfoService;
  private final ApprovalUserSettingDataService approvalUserSettingDataService;

  public void execute(FamilyInfoEditCmd cmd) {

    if (StringUtils.isBlank(cmd.getFamilyAvatar()) &&
        StringUtils.isBlank(cmd.getFamilyName()) &&
        StringUtils.isBlank(cmd.getFamilyNotice())) {

      ResponseAssert.failure(CommonErrorCode.UPDATE_FAILURE);
    }
    //敏感词
    ResponseAssert.isFalse(DynamicErrorCode.SENSITIVE_WORD_ERROR, SensitiveWordFilter.checkSensitiveWord(cmd.getFamilyName()));
    ResponseAssert.isFalse(DynamicErrorCode.SENSITIVE_WORD_ERROR, SensitiveWordFilter.checkSensitiveWord(cmd.getFamilyNotice()));

    ResponseAssert.isTrue(FamilyErrorCode.NOT_EXIST_FAMILY_INFO_DATA, getExistFamilyByUserId(cmd));

    FamilyMemberInfo manageMember = getOperatorMember(cmd);

    ResponseAssert.notNull(FamilyErrorCode.NOT_EXIST_FAMILY_INFO_DATA, manageMember);
    ResponseAssert.isTrue(CommonErrorCode.INSUFFICIENT_PERMISSION, isAdmin(manageMember));

    FamilyBaseInfo baseInfo = familyBaseInfoService.getBaseInfoById(manageMember.getFamilyId());
    ResponseAssert.notNull(FamilyErrorCode.NOT_EXIST_FAMILY_INFO_DATA, baseInfo);

    familyBaseInfoService.updateSelectiveById(getFamilyBaseInfo(cmd, manageMember.getFamilyId()));

  }

  private FamilyBaseInfo getFamilyBaseInfo(FamilyInfoEditCmd cmd, Long familyId) {

    FamilyBaseInfo familyBaseInfo = new FamilyBaseInfo().setId(familyId);

    if (StringUtils.isNotBlank(cmd.getFamilyNotice())) {
      familyBaseInfo.setFamilyNotice(cmd.getFamilyNotice());
      saveApproval(cmd, DataApprovalTypeEnum.FAMILY_NOTICE);
    }
    if (StringUtils.isNotBlank(cmd.getFamilyAvatar())) {

      familyBaseInfo.setFamilyAvatar(ResponseAssert.requiredSuccess(
          ossServiceClient.processImgSaveAsCompressZoom(cmd.getFamilyAvatar(),
              ImageSizeConst.COVER_HEIGHT)));
      saveApproval(cmd, DataApprovalTypeEnum.FAMILY_AVATAR);
    }
    if (StringUtils.isNotBlank(cmd.getFamilyName())) {
      familyBaseInfo.setFamilyName(cmd.getFamilyName());
      saveApproval(cmd, DataApprovalTypeEnum.FAMILY_NICKNAME);
    }

    return familyBaseInfo;
  }

  private void saveApproval(FamilyInfoEditCmd cmd, DataApprovalTypeEnum typeEnum) {
    approvalUserSettingDataService
        .saveOrUpdateApproval(cmd.getReqUserId(), cmd.getReqSysOrigin().getOrigin(), typeEnum);
  }

  private Boolean getExistFamilyByUserId(FamilyInfoEditCmd cmd) {
    return familyCommon.isExistFamilyByUserId(cmd.getReqUserId());
  }

  private boolean isAdmin(FamilyMemberInfo manageMember) {
    return familyCommon.isAdmin(manageMember.getMemberRole());
  }

  private FamilyMemberInfo getOperatorMember(FamilyInfoEditCmd cmd) {
    return familyMemberInfoService.getFamilyMemberByUserId(cmd.getReqUserId());
  }

}
