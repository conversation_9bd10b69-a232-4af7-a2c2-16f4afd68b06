package com.red.circle.other.app.command.family;

import com.red.circle.other.app.dto.cmd.family.FamilyBaseInfoQueryCmd;
import com.red.circle.other.app.dto.cmd.family.FamilyGroupChatPayCheckCmd;
import com.red.circle.other.infra.database.rds.service.family.FamilyGroupChatPayRecordService;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;

/**
 * 工会群聊是否解锁
 *
 * <AUTHOR> on 2023/12/21
 */
@Component
@AllArgsConstructor
public class FamilyGroupChatIsPayExe {

  private final FamilyGroupChatPayRecordService familyGroupChatPayRecordService;

  public Boolean execute(FamilyGroupChatPayCheckCmd cmd) {
    return familyGroupChatPayRecordService.getByFamilyIdId(cmd.getFamilyId());
  }
}
