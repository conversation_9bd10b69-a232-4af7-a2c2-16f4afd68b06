package com.red.circle.other.app.listener.gift.strategy;

import com.red.circle.common.business.core.enums.ReceiptType;
import com.red.circle.common.business.core.enums.SysOriginPlatformEnum;
import com.red.circle.common.business.enums.DiamondOrigin;
import com.red.circle.other.inner.enums.config.EnumConfigKey;
import com.red.circle.other.infra.database.cache.service.other.EnumConfigCacheService;
import com.red.circle.wallet.inner.model.cmd.DiamondReceiptCmd;
import com.red.circle.tool.core.date.TimestampUtils;
import com.red.circle.tool.core.sequence.IdWorkerUtils;
import java.math.BigDecimal;
import java.math.RoundingMode;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import com.red.circle.framework.core.asserts.ResponseAssert;
import com.red.circle.mq.business.model.event.gift.OfflineProcessGiftEvent;
import com.red.circle.other.domain.gateway.user.ability.UserRegionGateway;
import com.red.circle.other.domain.model.user.ability.RegionConfig;
import com.red.circle.other.infra.database.mongo.entity.gift.GiftAcceptUser;
import com.red.circle.other.infra.database.mongo.entity.gift.GiftGiveRunningWater;
import com.red.circle.other.infra.database.mongo.entity.team.team.TeamMember;
import com.red.circle.other.infra.database.mongo.service.gift.GiftGiveRunningWaterService;
import com.red.circle.other.infra.database.mongo.service.team.team.TeamMemberService;
import com.red.circle.other.infra.database.rds.service.gift.GiftWallService;
import com.red.circle.other.inner.enums.material.GiftCurrencyType;
import com.red.circle.other.inner.enums.material.GiftTabEnum;
import com.red.circle.tool.core.date.TimestampUtils;
import com.red.circle.tool.core.num.ArithmeticUtils;
import com.red.circle.tool.core.sequence.IdWorkerUtils;
import com.red.circle.wallet.inner.endpoint.diamond.SalaryDiamondBalanceClient;
import com.red.circle.wallet.inner.endpoint.diamond.SalaryDiamondRunningWaterClient;
import com.red.circle.wallet.inner.endpoint.wallet.WalletDiamondClient;
import com.red.circle.wallet.inner.endpoint.wallet.WalletGoldClient;
import com.red.circle.wallet.inner.model.cmd.DiamondReceiptCmd;
import com.red.circle.wallet.inner.model.cmd.GoldReceiptCmd;
import com.red.circle.wallet.inner.model.cmd.UserSalaryDiamondCmd;
import com.red.circle.wallet.inner.model.dto.UserSalaryDiamondRunningWaterDTO;
import com.red.circle.wallet.inner.model.enums.GoldOrigin;

import java.math.BigDecimal;
import java.util.Objects;

import com.red.circle.wallet.inner.model.enums.UserBankWaterEvent;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 接收奖品策略.
 *
 * <AUTHOR> on 2022/7/26
 */
@Slf4j
@Service("GIFT_PRIZE_ACCEPT_EVENT_LISTENER")
@RequiredArgsConstructor
public class GiftPrizeAcceptStrategy implements GiftStrategy {

  private final GiftWallService giftWallService;
  private final WalletGoldClient walletGoldClient;
  private final WalletDiamondClient walletDiamondClient;
  private final GiftGiveRunningWaterService giftGiveRunningWaterService;


  private final SalaryDiamondBalanceClient salaryDiamondBalanceClient;
  private final SalaryDiamondRunningWaterClient salaryDiamondRunningWaterClient;
  private final UserRegionGateway userRegionGateway;
  private final TeamMemberService teamMemberService;
  private final EnumConfigCacheService enumConfigCacheService;

  @Override
  public void processor(OfflineProcessGiftEvent event) {

    GiftGiveRunningWater runningWater = giftGiveRunningWaterService.getByIdPrimary(
        event.getRunningWaterId());

    if (Objects.isNull(runningWater)) {
      log.warn("【接收礼物奖品】没有找到流水信息，忽律处理");
      return;
    }

    giftGiveRunningWaterService.addLog(runningWater.getId(), "开始：接收奖品策略");

    runningWater.getAcceptUsers().forEach(accept -> {

      // 记录礼物墙
      giftWallService.incrGiftWall(accept.getAcceptUserId(),
          runningWater.getGiftId(),
          (long) runningWater.getGiftValue().getQuantity());

      if (ArithmeticUtils.lteZero(accept.getAcceptAmount())) {
        return;
      }

      // 当前礼物是否为幸运礼物
      Boolean isLuckyGift = runningWater.getGiftValue().getGiftType()
          .contains(GiftTabEnum.LUCKY_GIFT.name());

      // YOLO任何礼物改为接受钻石
      /*if (Objects.equals(runningWater.getSysOrigin(), SysOriginPlatformEnum.YOLO.name())
          && GiftCurrencyType.GOLD.eq(runningWater.getGiftValue().getCurrencyType())) {
        yoloSalaryDiamondCount(runningWater, accept);
        *//*DiamondReceiptCmd cmd = new DiamondReceiptCmd()
                .setConsumeId(IdWorkerUtils.getIdStr())
                .setSysOrigin(SysOriginPlatformEnum.valueOf(runningWater.getSysOrigin()))
                .setTrackId(runningWater.getId())
                .setUserId(accept.getAcceptUserId())
                .setOriginUserId(runningWater.getUserId())
                .setOrigin(DiamondOrigin.ACCEPT_GIFT)
                .setCustomizeOriginDesc("[" + runningWater.getGiftValue().getGiftType() + "]"
                        + GoldOrigin.ACCEPT_GIFT.getDesc())
                .setType(ReceiptType.INCOME)
                .setAmount(accept.getAcceptAmount())
                .setCreateTime(TimestampUtils.now());
        log.warn("cmd" + cmd);
        walletDiamondClient.changeBalance(cmd);*//*
      } else{*/
        // 发送金币 - 已禁用：只保留充值和币商获取金币
        /*
        if (GiftCurrencyType.GOLD.eq(runningWater.getGiftValue().getCurrencyType())
            && Boolean.FALSE.equals(isLuckyGift)) {

          Long consumeId = walletGoldClient.changeBalance(GoldReceiptCmd.builder()
              .appIncome()
              .eventId(event.getTrackId())
              .userId(accept.getAcceptUserId())
              .sysOrigin(runningWater.getSysOrigin())
              .origin(
                  GoldOrigin.ACCEPT_GIFT + "_" + runningWater.getGiftValue().getGiftType())
              .originDescribe("[" + runningWater.getGiftValue().getGiftType() + "]"
                  + GoldOrigin.ACCEPT_GIFT.getDesc())
              .closeDelayAsset()
              .amount(accept.getAcceptAmount())
              .build()).getBody().getAssetRecordId();

          giftGiveRunningWaterService.updateAcceptUserReceiptId(runningWater.getId(),
              accept.getAcceptUserId(), consumeId);
        }
        */

        // 发放幸运礼物钻石 - 已修改为所有礼物都按比例发放钻石
        /*
        if (GiftCurrencyType.GOLD.eq(runningWater.getGiftValue().getCurrencyType())
            && Boolean.TRUE.equals(isLuckyGift)) {

          walletDiamondClient.changeBalance(
              new DiamondReceiptCmd()
                  .setConsumeId(IdWorkerUtils.getIdStr())
                  .setSysOrigin(SysOriginPlatformEnum.valueOf(runningWater.getSysOrigin()))
                  .setTrackId(runningWater.getId())
                  .setUserId(accept.getAcceptUserId())
                  .setOriginUserId(runningWater.getUserId())
                  .setOrigin(DiamondOrigin.ACCEPT_LUCKY_GIFTS)
                  .setCustomizeOriginDesc(DiamondOrigin.ACCEPT_LUCKY_GIFTS.getDesc())
                  .setType(ReceiptType.INCOME)
                  .setAmount(accept.getAcceptAmount())
                  .setCreateTime(TimestampUtils.now())
          );
        }
        */

        // 新逻辑：所有礼物都按比例发放钻石（如50%）
        if (GiftCurrencyType.GOLD.eq(runningWater.getGiftValue().getCurrencyType())) {
          // 获取钻石奖励比例配置（默认50%）
          BigDecimal diamondRatio = enumConfigCacheService.getValueBigDecimal(
              EnumConfigKey.GIFT_DIAMOND_REWARD_RATIO, runningWater.getSysOrigin());
          if (diamondRatio == null || diamondRatio.compareTo(BigDecimal.ZERO) <= 0) {
            diamondRatio = new BigDecimal("50"); // 默认50%
          }

          // 计算钻石数量：礼物价值 * 比例 / 100
          BigDecimal diamondAmount = accept.getAcceptAmount()
              .multiply(diamondRatio)
              .divide(new BigDecimal("100"), 0, RoundingMode.DOWN);

          if (diamondAmount.compareTo(BigDecimal.ZERO) > 0) {
            walletDiamondClient.changeBalance(
                new DiamondReceiptCmd()
                    .setConsumeId(IdWorkerUtils.getIdStr())
                    .setSysOrigin(SysOriginPlatformEnum.valueOf(runningWater.getSysOrigin()))
                    .setTrackId(runningWater.getId())
                    .setUserId(accept.getAcceptUserId())
                    .setOriginUserId(runningWater.getUserId())
                    .setOrigin(DiamondOrigin.ACCEPT_LUCKY_GIFTS)
                    .setCustomizeOriginDesc("收到礼物钻石奖励(" + diamondRatio + "%)")
                    .setType(ReceiptType.INCOME)
                    .setAmount(diamondAmount)
                    .setCreateTime(TimestampUtils.now())
            );
          }
        }
      //}

      // 发放积分
//      if (GiftCurrencyType.DIAMOND.eq(runningWater.getGiftValue().getCurrencyType())
//          && Boolean.FALSE.equals(isLuckyGift)) {
//
//        walletRepository.changeDiamondBalanceAsync(
//            DiamondReceipt.builder()
//                .consumeId(event.getTrackId() + "_" + accept.getAcceptUserId())
//                .sysOrigin(SysOriginPlatformEnum.valueOf(runningWater.getSysOrigin()))
//                .trackId(Long.parseLong(event.getTrackId()))
//                .userId(accept.getAcceptUserId())
//                .originUserId(runningWater.getUserId())
//                .type(ReceiptType.INCOME)
//                .origin(DiamondOrigin.ACCEPT_GIFT)
//                .amount(accept.getAcceptAmount())
//                .createTime(runningWater.getCreateTime())
//                .build());
//      }
    });

    giftGiveRunningWaterService.addLog(runningWater.getId(),
        "结束：接收奖品策略(礼物墙、积分/钻石发放)");
  }

  /**
   * yolo钻石工资统计
   *
   * @param runningWater
   * @param acceptUsers
   */
  private void yoloSalaryDiamondCount(GiftGiveRunningWater runningWater, GiftAcceptUser acceptUsers) {
    log.warn("yolo钻石入账1:{}\n{}", runningWater, acceptUsers);
    if (!Objects.equals(runningWater.getSysOrigin(), SysOriginPlatformEnum.YOLO.name())) {
      // 如果不是YOLO就停止入账
      return;
    }
    if (acceptUsers.getAcceptAmount().compareTo(BigDecimal.ZERO) <= 0) {
      log.warn("小于0不入账:{}", acceptUsers.getAcceptAmount());
      return;
    }
    /*if (Objects.equals(salaryDiamondRunningWaterClient.existsTrackId(acceptUsers.getAcceptUserId(), UserBankWaterEvent.ACCEPT_GIFTS.name(), runningWater.getId().toString()).getBody(),
            Boolean.TRUE)) {
      log.warn("重复处理:{}", runningWater.getId());
      return;
    }*/
//    RegionConfig sendRegion = getUserRegion(acceptAnchorUser.getAcceptUserId());
//    BigDecimal ratio = getAssistGiftToOtherGoldRatio(sendRegion.getId());
//
//    log.warn("yolo钻石入账2:{}\n{}", sendRegion, ratio);

    /*BigDecimal quantity = runningWater.getGiftValue().getUnitPrice()
            .multiply(new BigDecimal(runningWater.getGiftValue().getQuantity()))
            .multiply(acceptUsers.getPercentage()).setScale(0, RoundingMode.DOWN);*/

    salaryDiamondBalanceClient.changeSalaryBalanceAsync(new UserSalaryDiamondCmd()
            .setUserId(acceptUsers.getAcceptUserId())
            .setQuantity(acceptUsers.getAcceptAmount())
            .setSysOrigin(runningWater.getSysOrigin()));

    TeamMember sendMember = teamMemberService.getByMemberId(acceptUsers.getAcceptUserId());
    Long teamId = 0L;
    if (Objects.nonNull(sendMember)) {
      teamId = sendMember.getTeamId();
    }
    salaryDiamondRunningWaterClient.addAsync(new UserSalaryDiamondRunningWaterDTO()
            .setId(IdWorkerUtils.getId())
            .setUserId(acceptUsers.getAcceptUserId())
            .setTeamId(teamId)
            .setSysOrigin(runningWater.getSysOrigin())
            .setType(ReceiptType.INCOME.getType())
            .setTrackId(runningWater.getId()+"")
            .setAmount(acceptUsers.getAcceptAmount())
            .setSalaryEvent(UserBankWaterEvent.ACCEPT_GIFTS.name())
            .setEventDesc(UserBankWaterEvent.ACCEPT_GIFTS.getDescribe())
            .setRemark(UserBankWaterEvent.ACCEPT_GIFTS.getDescribe())
            .setBalance(
                    ResponseAssert.requiredSuccess(
                            salaryDiamondBalanceClient.getBalance(acceptUsers.getAcceptUserId())).getBalance())
            .setCreateTime(TimestampUtils.now())
            .setCreateUser(0L)
            .setCreateUserOrigin(0)
    );
  }

  private RegionConfig getUserRegion(Long userId) {
    return userRegionGateway.getRegionConfigByUserId(userId);
  }

  /**
   * 赠送礼物给别人返还金币比例.
   */
  private BigDecimal getAssistGiftToOtherGoldRatio(String regionId) {
    return userRegionGateway.getAssistGiftToOtherGoldRatio(regionId);
  }

  /**
   * 赠送礼物给别人返还金币比例.
   */
  private BigDecimal getAssistGiftToOwnGoldRatio(String regionId) {
    return userRegionGateway.getAssistGiftToOwnGoldRatio(regionId);
  }
}
