package com.red.circle.other.app.command.family;

import com.google.common.collect.Sets;
import com.red.circle.common.business.dto.cmd.AppExtCommand;
import com.red.circle.framework.core.dto.AppCommand;
import com.red.circle.other.app.dto.clientobject.family.FamilyExpCO;
import com.red.circle.other.app.dto.clientobject.family.FamilyLeaderboardCO;
import com.red.circle.other.domain.gateway.user.ability.UserRegionGateway;
import com.red.circle.other.infra.common.family.FamilyCommon;
import com.red.circle.other.infra.database.rds.entity.family.FamilyBaseInfo;
import com.red.circle.other.infra.database.rds.entity.family.FamilyMemberInfo;
import com.red.circle.other.infra.database.rds.entity.family.FamilyMonthExp;
import com.red.circle.other.infra.database.rds.entity.family.FamilyWeekExp;
import com.red.circle.other.infra.database.rds.service.family.FamilyBaseInfoService;
import com.red.circle.other.infra.database.rds.service.family.FamilyMemberInfoService;
import com.red.circle.other.infra.database.rds.service.family.FamilyMonthExpService;
import com.red.circle.other.infra.database.rds.service.family.FamilyWeekExpService;
import com.red.circle.other.infra.enums.family.FamilyStatusEnum;
import com.red.circle.other.inner.model.dto.famliy.FamilyDetailsDTO;
import com.red.circle.tool.core.collection.CollectionUtils;
import com.red.circle.tool.core.num.NumUtils;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

/**
 * <p>
 * 工会排行榜 周榜、月榜、自己工会信息.
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-24
 */
@Component
@RequiredArgsConstructor
public class FamilyLeaderboardExe {

  private final FamilyCommon familyCommon;
  private final UserRegionGateway userRegionGateway;
  private final FamilyWeekExpService familyWeekExpService;
  private final FamilyMonthExpService familyMonthExpService;
  private final FamilyBaseInfoService familyBaseInfoService;
  private final FamilyMemberInfoService familyMemberInfoService;

  public FamilyLeaderboardCO execute(AppExtCommand cmd) {

    FamilyLeaderboardCO resultCO = new FamilyLeaderboardCO();

    List<FamilyWeekExp> weeks = getFamilyWeekExps(cmd);
    List<FamilyMonthExp> months = getFamilyMonthExps(cmd);
    Map<Long, FamilyBaseInfo> baseInfoMap = getBaseInfoMap(weeks, months);

    resultCO.setWeekDataList(listWeekCO(cmd, weeks, baseInfoMap));
    resultCO.setMonthDataList(listMonthCO(cmd, months, baseInfoMap));

    FamilyMemberInfo member = getMemberByUserId(cmd);
    if (Objects.isNull(member)) {
      return resultCO;
    }
    FamilyBaseInfo baseInfo = getFamilyBaseInfo(member);
    if (isNormal(baseInfo)) {
      return resultCO;
    }
    resultCO.setWeekData(getExpCO(cmd, baseInfo, Optional.ofNullable(getFamilyWeekExp(baseInfo))
        .map(FamilyWeekExp::getExp).orElse(0L)));

    resultCO.setMonthData(getExpCO(cmd, baseInfo, Optional.ofNullable(getFamilyMonthExp(baseInfo))
        .map(FamilyMonthExp::getExp).orElse(0L)));
    return resultCO;
  }

  private String getRegionId(AppExtCommand cmd) {
    return userRegionGateway.getRegionId(cmd.getReqUserId());
  }

  private FamilyMonthExp getFamilyMonthExp(FamilyBaseInfo baseInfo) {
    return familyMonthExpService.getThisMonthByFamilyId(baseInfo.getId());
  }

  private FamilyWeekExp getFamilyWeekExp(FamilyBaseInfo baseInfo) {
    return familyWeekExpService.getThisWeekByFamilyId(baseInfo.getId());
  }

  private boolean isNormal(FamilyBaseInfo familyBaseInfo) {
    return Objects.isNull(familyBaseInfo) || !Objects.equals(familyBaseInfo.getFamilyStatus(),
        FamilyStatusEnum.NORMAL.name());
  }

  private FamilyBaseInfo getFamilyBaseInfo(FamilyMemberInfo member) {
    return familyBaseInfoService.getBaseInfoById(member.getFamilyId());
  }

  private FamilyMemberInfo getMemberByUserId(AppExtCommand cmd) {
    return familyMemberInfoService.getFamilyMemberByUserId(cmd.getReqUserId());
  }

  private List<FamilyExpCO> listWeekCO(AppExtCommand cmd, List<FamilyWeekExp> data,
      Map<Long, FamilyBaseInfo> baseInfoMap) {

    return data.stream()
        .map(obj -> getExpCO(cmd, baseInfoMap.get(obj.getFamilyId()), obj.getExp()))
        .collect(Collectors.toList());
  }

  private List<FamilyExpCO> listMonthCO(AppCommand cmd, List<FamilyMonthExp> data,
      Map<Long, FamilyBaseInfo> baseInfoMap) {

    return data.stream()
        .map(obj -> getExpCO(cmd, baseInfoMap.get(obj.getFamilyId()), obj.getExp()))
        .collect(Collectors.toList());
  }

  private FamilyDetailsDTO getFamilyLevel(AppCommand cmd, Long familyId) {
    return familyCommon.getFamilyDetails(cmd.getReqSysOrigin().getOrigin(), familyId);
  }

  private Map<Long, FamilyBaseInfo> getBaseInfoMap(List<FamilyWeekExp> weeks,
      List<FamilyMonthExp> months) {
    return familyBaseInfoService.mapBaseInfo(getFamilyIds(weeks, months));
  }

  private List<FamilyMonthExp> getFamilyMonthExps(AppExtCommand cmd) {
    return familyMonthExpService.listThisMonthBySysOrigin(cmd.requireReqSysOrigin(),
            getRegionId(cmd))
        .stream()
        .limit(20)
        .collect(Collectors.toList());
  }

  private List<FamilyWeekExp> getFamilyWeekExps(AppExtCommand cmd) {

    return familyWeekExpService.listThisWeekBySysOrigin(cmd.requireReqSysOrigin(), getRegionId(cmd))
        .stream()
        .limit(20)
        .collect(Collectors.toList());
  }

  private Set<Long> getFamilyIds(List<FamilyWeekExp> weeks, List<FamilyMonthExp> months) {
    Set<Long> familyIds = Sets.newHashSet();
    Set<Long> weekIds = weeks.stream().map(FamilyWeekExp::getFamilyId).collect(Collectors.toSet());
    if (CollectionUtils.isNotEmpty(weekIds)) {
      familyIds.addAll(weekIds);
    }
    Set<Long> monthIds = months.stream().map(FamilyMonthExp::getFamilyId)
        .collect(Collectors.toSet());
    if (CollectionUtils.isNotEmpty(monthIds)) {
      familyIds.addAll(monthIds);
    }
    return CollectionUtils.isEmpty(familyIds) ? Sets.newHashSet() : familyIds;
  }

  private FamilyExpCO getExpCO(AppCommand cmd, FamilyBaseInfo baseInfo, Long exp) {

    FamilyDetailsDTO levelCache = getFamilyLevel(cmd, baseInfo.getId());

    return new FamilyExpCO()
        .setExp(NumUtils.formatLong(exp))
        .setFamilyId(baseInfo.getId())
        .setLevelKey(levelCache.getLevelKey())
        .setFamilyName(baseInfo.getFamilyName())
        .setFamilyAvatar(baseInfo.getFamilyAvatar())
        .setFamilyNotice(baseInfo.getFamilyNotice())
        .setAvatarFrameSvg(levelCache.getAvatarFrameSvg())
        .setAvatarFrameCover(levelCache.getAvatarFrameCover());
  }

}
