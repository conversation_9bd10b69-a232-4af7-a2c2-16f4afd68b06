package com.red.circle.other.app.command.room;


import com.red.circle.common.business.dto.cmd.app.AppRoomIdCmd;
import com.red.circle.component.redis.service.RedisService;
import com.red.circle.framework.core.asserts.ResponseAssert;
import com.red.circle.framework.core.response.CommonErrorCode;
import com.red.circle.framework.core.response.ResponseErrorCode;
import com.red.circle.other.app.dto.clientobject.room.RoomRewardMappingCO;
import com.red.circle.other.infra.database.rds.entity.live.RoomContributionBalance;
import com.red.circle.other.infra.database.rds.service.live.RoomContributionBalanceService;
import com.red.circle.other.inner.asserts.RoomErrorCode;
import com.red.circle.tool.core.date.LocalDateTimeUtils;
import com.red.circle.tool.core.num.ArithmeticUtils;
import com.red.circle.wallet.inner.endpoint.wallet.WalletGoldClient;
import com.red.circle.wallet.inner.model.cmd.GoldReceiptCmd;
import com.red.circle.wallet.inner.model.enums.GoldOrigin;
import java.math.BigDecimal;
import java.math.RoundingMode;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

/**
 * 房间奖励提取.
 *
 * <AUTHOR> on 2021/3/2
 */
@Component
@RequiredArgsConstructor
public class RoomRewardExtractCmdExe {

  private final RedisService redisService;
  private final WalletGoldClient walletGoldClient;
  private final RoomContributionBalanceService roomContributionBalanceService;

  public BigDecimal execute(AppRoomIdCmd cmd) {
    ResponseAssert.failure(ResponseErrorCode.REQUEST_PARAMETER_ERROR);

    String lockKey = "RoomRewardExtract";
    ResponseAssert.isTrue(CommonErrorCode.REQUEST_LIMITING,
        redisService.lock(lockKey, 60));

    RoomContributionBalance balance = roomContributionBalanceService.getByRoomId(cmd.getRoomId());

    ResponseAssert.failure(RoomErrorCode.NOT_FOUND_CONTRIBUTION);

    //  提现最小额
    ResponseAssert.isTrue(RoomErrorCode.MINIMUM_WITHDRAWAL,
        ArithmeticUtils
            .gte(balance.calculationBalance(), BigDecimal.valueOf(1000)));

    // 操作失败
    ResponseAssert.isTrue(CommonErrorCode.OPERATING_FAILURE, roomContributionBalanceService
        .incrWithdrawQuantity(cmd.getRoomId(), balance.calculationBalance()));

    sendGolds(cmd, balance.calculationBalance()
        .multiply(getRatio(balance))
        .setScale(0, RoundingMode.DOWN));

    return BigDecimal.ZERO;

  }

  private String getDayKey() {
    return "C_DIAMOND_E_" + LocalDateTimeUtils.nowFormat("yyyyMMdd");
  }

  private BigDecimal getRatio(RoomContributionBalance balance) {
    return BigDecimal.valueOf(
        (double) RoomRewardMappingCO.calculateCurrentRatio(balance.getTotalQuantity()) / 100);
  }

  private void sendGolds(AppRoomIdCmd cmd, BigDecimal balance) {
    // 已禁用：房间奖励提取已关闭，只保留充值和币商获取金币
    /*
    walletGoldClient.changeBalance(GoldReceiptCmd.builder()
        .appIncome()
        .userId(cmd.requiredReqUserId())
        .eventId(cmd.getRoomId())
        .sysOrigin(cmd.requireReqSysOrigin())
        .origin(GoldOrigin.ROOM_REWARD_EXTRACT)
        .amount(balance)
        .build());
    */
    log.warn("房间奖励提取已禁用，用户ID: {}, 房间ID: {}, 金额: {}",
        cmd.requiredReqUserId(), cmd.getRoomId(), balance);
  }

}
