package com.red.circle.other.app.scheduler;

import com.red.circle.component.redis.annotation.TaskCacheLock;
import com.red.circle.mq.business.model.event.team.TeamBillSettleEvent;
import com.red.circle.mq.rocket.business.producer.TeamSalaryMqMessage;
import com.red.circle.other.infra.database.mongo.entity.team.team.TeamProfile;
import com.red.circle.other.infra.database.mongo.service.gift.GiftGiveRunningWaterService;
import com.red.circle.other.infra.database.mongo.service.team.team.TeamBillCycleService;
import com.red.circle.other.infra.database.mongo.service.team.team.TeamProfileService;
import com.red.circle.other.infra.utils.ZonedDateTimeUtils;
import com.red.circle.other.inner.model.cmd.team.TeamBillCmd;
import com.red.circle.tool.core.date.TimestampUtils;
import java.time.LocalDateTime;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

/**
 * 团队账单调度器.
 *
 * <AUTHOR> on 2022/12/15
 */
@Slf4j
@Component
@AllArgsConstructor
public class TeamBillTask {

  private final TeamSalaryMqMessage otherMqMessage;
  private final TeamProfileService teamProfileService;
  private final TeamBillCycleService teamBillCycleService;
  private final GiftGiveRunningWaterService giftGiveRunningWaterService;

  /**
   * 每月1号 0点执行.
   */
  // 每月最后一天 0点执行
  // @Scheduled(cron = "0 0 0 L * ?", zone = "Asia/Riyadh")
//  @Scheduled(cron = "0 0 0 1 * ?", zone = "Asia/Riyadh")
  @TaskCacheLock(key = "PROCESS_TEAM_BILL", expireSecond = 86400)
  public void processTeamMonthBill() {
    long startTime = System.currentTimeMillis();
    log.info("exec process_team_bill start");
    processTeamBill();
    log.info("exec process_team_bill end with {}",System.currentTimeMillis()-startTime);
  }

  public void processTeamMonthBillTest() {
    processTeamBill();
  }

//  /**
//   * 10分钟， 检查是否存在没有补目标用户.
//   */
//  @Scheduled(cron = "0 */10 * * * ?")
//  @TaskCacheLock(key = "CHECK_NOT_COUNT_ANCHOR_TARGET", expireSecond = 50)
//  public void checkNotCountAnchorGiftTarget() {
//
//    List<GiftGiveRunningWater> giftGiveRunningWaters = giftGiveRunningWaterService.listThisMonthAnchorNotCountTarget(
//        1000);
//    if (CollectionUtils.isEmpty(giftGiveRunningWaters)) {
//      return;
//    }
//
//    for (GiftGiveRunningWater runningWater : giftGiveRunningWaters) {
//      for (GiftAcceptUser acceptUser : runningWater.getAcceptUsers()) {
//
//        if (!Objects.equals(acceptUser.getAnchor(), Boolean.TRUE)) {
//          continue;
//        }
//
//        if (BigDecimalUtils.isNullOrLteZero(acceptUser.getTargetAmount())) {
//          giftGiveRunningWaterService.updateAcceptUserCountTargetAmountTrue(
//              runningWater.getId(),
//              0L,
//              acceptUser.getAcceptUserId());
//          continue;
//        }
//
//        // 累计目标
//        TeamTargetBillRes teamTargetBillRes = teamTargetManager
//            .incrementGiftValue(acceptUser.getAcceptUserId(),
//                acceptUser.getTargetAmount());
//        if (teamTargetBillRes.getSuccess()) {
//          // 标记已被统计
//          giftGiveRunningWaterService.updateAcceptUserCountTargetAmountTrue(
//              runningWater.getId(),
//              teamTargetBillRes.getBillId(),
//              acceptUser.getAcceptUserId());
//        }
//
//      }
//    }
//  }
//

  private void processTeamBill() {
    log.warn("开始执行账单处理:{},{}", ZonedDateTimeUtils.nowAsiaRiyadhToInt(), LocalDateTime.now());
    // 出单
    teamBillCycleService.billStatusPayOut();

    // 创建本月账单，发出结算信号
    consumeProcessPayOutBill();
    log.warn("结束账单处理");
  }

  /**
   * 创建本月账单，发出结算信号.
   */
  private void consumeProcessPayOutBill() {
    teamProfileService.scanStatusAvailable(teamProfiles -> {

      for (TeamProfile teamProfile : teamProfiles) {
        teamBillCycleService.createIfAbsent(new TeamBillCmd()
            .setSysOrigin(teamProfile.getSysOrigin())
            .setTeamId(teamProfile.getId())
            .setRegion(teamProfile.getRegion())
            .setOperationTime(TimestampUtils.now())
            .setOperationBackUser(teamProfile.getCreateUser()));

        otherMqMessage.teamBillSettle(new TeamBillSettleEvent().setTeamId(teamProfile.getId()));
      }
    }, 5000);

  }


}
