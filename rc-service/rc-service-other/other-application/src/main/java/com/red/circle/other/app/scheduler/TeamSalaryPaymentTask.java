package com.red.circle.other.app.scheduler;

import com.google.common.collect.Lists;
import com.red.circle.component.redis.annotation.TaskCacheLock;
import com.red.circle.mq.rocket.business.producer.TeamSalaryMqMessage;
import com.red.circle.other.infra.database.mongo.entity.team.team.TeamProfile;
import com.red.circle.other.infra.database.mongo.service.team.team.TeamProfileService;
import com.red.circle.other.infra.utils.ZonedDateTimeUtils;
import com.red.circle.tool.core.collection.CollectionUtils;
import com.red.circle.tool.core.date.TimestampUtils;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

/**
 * 每天零点零分发送团队成员与代理工资.
 *
 * <AUTHOR> on 2023/4/18.
 */
@Slf4j
@Component
@AllArgsConstructor
public class TeamSalaryPaymentTask {

  private final TeamProfileService teamProfileService;
  private final TeamSalaryMqMessage teamSalaryMqMessage;

  /**
   * 每天凌晨0点过30秒执行一次，处理团队工资结算.
   */
//  @Scheduled(cron = "30 1 0 * * ?", zone = "Asia/Riyadh")
  @TaskCacheLock(key = "TEAM_SALARY_PAYMENT_TASK", expireSecond = 10800)
  public void teamSalaryPaymentTask() {
    long startTime = System.currentTimeMillis();
    log.warn("exec team_salary_payment_task start");

    log.warn("支付团队工资 start，时间:{}", TimestampUtils.now());

    // 是否本月一号
    Boolean thisMonthFirstDay = Objects.equals(ZonedDateTimeUtils.nowAsiaRiyadhToInt(),
        ZonedDateTimeUtils.nowAsiaRiyadhFirstDayOfMonthToInt());

    List<TeamProfile> teamProfiles = teamProfileService.listStatusAvailable();

    if (CollectionUtils.isNotEmpty(teamProfiles)) {

      List<List<TeamProfile>> teams = Lists.partition(teamProfiles, 200);

      teams.forEach(teamList -> {

        if (CollectionUtils.isEmpty(teamList)) {
          return;
        }

        teamSalaryMqMessage.sendSalary(teamList.stream().map(TeamProfile::getId)
            .collect(Collectors.toSet()), thisMonthFirstDay);
      });

    }

    log.warn("支付团队工资 end，时间:{}", TimestampUtils.now());
    log.warn("exec team_salary_payment_task end with {}",System.currentTimeMillis()-startTime);
  }

  public void teamSalaryPaymentTaskTest() {

    log.warn("支付团队工资 start，时间:{}", TimestampUtils.now());

    // 是否本月一号
    Boolean thisMonthFirstDay = Objects.equals(ZonedDateTimeUtils.nowAsiaRiyadhToInt(),
            ZonedDateTimeUtils.nowAsiaRiyadhFirstDayOfMonthToInt());

    List<TeamProfile> teamProfiles = teamProfileService.listStatusAvailable();

    if (CollectionUtils.isNotEmpty(teamProfiles)) {

      List<List<TeamProfile>> teams = Lists.partition(teamProfiles, 200);

      teams.forEach(teamList -> {

        if (CollectionUtils.isEmpty(teamList)) {
          return;
        }

        teamSalaryMqMessage.sendSalary(teamList.stream().map(TeamProfile::getId)
            .collect(Collectors.toSet()), thisMonthFirstDay);
      });

    }

    log.warn("支付团队工资 end，时间:{}", TimestampUtils.now());
  }

  /**
   * 每月凌晨0点过30秒执行一次,处理超出政策后的剩余目标兑换美元操作.
   */
  @Scheduled(cron = "40 1 0 1 * ?", zone = "Asia/Riyadh")
  @TaskCacheLock(key = "TEAM_EXCHANGE_SURPLUS_TARGET_TASK", expireSecond = 10800)
  public void teamExchangeSurplusTargetTask() {
    long startTime = System.currentTimeMillis();
    log.info("exec team_exchange_surplus_target_task start");
    log.warn("处理超出政策后的剩余目标兑换美元操作 start，时间:{}", TimestampUtils.now());

    List<TeamProfile> teamProfiles = teamProfileService.listStatusAvailable();
    Integer lastMonth = ZonedDateTimeUtils.getAsiaRiyadhLastMonth();

    if (CollectionUtils.isNotEmpty(teamProfiles)) {

      List<List<TeamProfile>> teams = Lists.partition(teamProfiles, 200);

      teams.forEach(teamList -> {

        if (CollectionUtils.isEmpty(teamList)) {
          return;
        }

        teamSalaryMqMessage.sendExchangeSurplusTarget(teamList.stream().map(TeamProfile::getId)
            .collect(Collectors.toSet()), lastMonth, null);
      });

    }

    log.warn("处理超出政策后的剩余目标兑换美元操作 end，时间:{}", TimestampUtils.now());
    log.info("exec team_exchange_surplus_target_task end with {}",System.currentTimeMillis()-startTime);

  }

}
