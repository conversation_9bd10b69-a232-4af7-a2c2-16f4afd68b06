package com.red.circle.other.app.command.family;

import com.red.circle.framework.core.asserts.ResponseAssert;
import com.red.circle.framework.core.response.CommonErrorCode;
import com.red.circle.other.app.dto.cmd.family.FamilyGrantUserRoleCmd;
import com.red.circle.other.infra.common.family.FamilyCommon;
import com.red.circle.other.infra.database.rds.entity.family.FamilyMemberInfo;
import com.red.circle.other.infra.database.rds.service.family.FamilyMemberInfoService;
import com.red.circle.other.infra.enums.family.FamilyRoleEnum;
import com.red.circle.other.inner.asserts.FamilyErrorCode;
import java.util.Objects;
import java.util.Optional;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

/**
 * <p>
 * 授权用户工会权限.
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-22
 */
@Component
@RequiredArgsConstructor
public class FamilyGrantUserRoleExe {

  private final FamilyCommon familyCommon;
  private final FamilyMemberInfoService familyMemberInfoService;

  public void execute(FamilyGrantUserRoleCmd cmd) {
    ResponseAssert.isTrue(FamilyErrorCode.NOT_EXIST_FAMILY_INFO_DATA, getExistFamilyByUserId(cmd));
    ResponseAssert.isFalse(CommonErrorCode.INSUFFICIENT_PERMISSION, isGrantAdmin(cmd));

    FamilyMemberInfo manageMember = getOperatorMember(cmd);
    ResponseAssert.notNull(FamilyErrorCode.NOT_EXIST_FAMILY_INFO_DATA, manageMember);
    FamilyMemberInfo member = getAuthorizedMember(cmd);
    ResponseAssert.notNull(FamilyErrorCode.NOT_EXIST_FAMILY_INFO_DATA, member);
    ResponseAssert
        .isFalse(CommonErrorCode.INSUFFICIENT_PERMISSION, isPermissionDenied(manageMember, member));
    ResponseAssert.isFalse(CommonErrorCode.OPERATION_CONFLICT,
        Objects.equals(member.getMemberUserId(), cmd.getReqUserId()));

    checkGrantManage(cmd, manageMember);

    updateRoleById(cmd);
  }

  private boolean isPermissionDenied(FamilyMemberInfo manageMember, FamilyMemberInfo member) {
    return Objects.equals(manageMember.getMemberRole(), member.getMemberRole()) && Objects
        .equals(manageMember.getMemberRole(), FamilyRoleEnum.MANAGE.getKey());
  }


  private void checkGrantManage(FamilyGrantUserRoleCmd cmd, FamilyMemberInfo manageMember) {

    ResponseAssert.isFalse(FamilyErrorCode.FAMILY_MANAGE_MAX,
        Objects.equals(manageMember.getMemberRole(), FamilyRoleEnum.MEMBER.getKey()));

    if (!Objects.equals(cmd.getRoleKey(), FamilyRoleEnum.MANAGE.getKey())) {
      return;
    }
    ResponseAssert.isTrue(FamilyErrorCode.FAMILY_MANAGE_MAX, isAllowGrant(cmd, manageMember));
  }

  private boolean isAllowGrant(FamilyGrantUserRoleCmd cmd, FamilyMemberInfo manageMember) {
    return getFamilyCurrentManagerCount(manageMember) < getFamilyLevelMaxManagerCount(cmd,
        manageMember);
  }

  private Integer getFamilyCurrentManagerCount(FamilyMemberInfo manageMember) {
    return familyMemberInfoService.getManagerCount(manageMember.getFamilyId());
  }

  private Integer getFamilyLevelMaxManagerCount(FamilyGrantUserRoleCmd cmd,
      FamilyMemberInfo manageMember) {
    return Optional.ofNullable(
        familyCommon.getFamilyDetails(cmd.getReqSysOrigin().getOrigin(), manageMember.getFamilyId())
            .getMaxManager()).orElse(0);
  }

  private Boolean getExistFamilyByUserId(FamilyGrantUserRoleCmd cmd) {
    return familyCommon.isExistFamilyByUserId(cmd.getReqUserId());
  }

  private void updateRoleById(FamilyGrantUserRoleCmd cmd) {
    familyMemberInfoService.updateRoleById(cmd.getAuthorizedMemberId(), cmd.getRoleKey());
  }

  private boolean isGrantAdmin(FamilyGrantUserRoleCmd cmd) {
    return Objects.equals(FamilyRoleEnum.ADMIN.getKey(), cmd.getRoleKey());
  }

  private FamilyMemberInfo getAuthorizedMember(FamilyGrantUserRoleCmd cmd) {
    return familyMemberInfoService
        .geMemberByMemberId(cmd.getAuthorizedMemberId());
  }

  private FamilyMemberInfo getOperatorMember(FamilyGrantUserRoleCmd cmd) {
    return familyMemberInfoService.getFamilyMemberByUserId(cmd.getReqUserId());
  }

}
