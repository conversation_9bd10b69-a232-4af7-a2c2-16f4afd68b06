package com.red.circle.other.app.command.family;

import com.google.common.collect.Lists;
import com.red.circle.framework.core.dto.CommonCommand;
import com.red.circle.other.app.convertor.user.UserProfileAppConvertor;
import com.red.circle.other.app.dto.clientobject.family.FamilyMsgListCO;
import com.red.circle.other.app.dto.clientobject.family.FamilyUserCO;
import com.red.circle.other.app.dto.cmd.family.FamilyMessageListCmd;
import com.red.circle.other.domain.gateway.user.UserProfileGateway;
import com.red.circle.other.infra.common.family.FamilyCommon;
import com.red.circle.other.infra.database.rds.entity.family.FamilyMemberInfo;
import com.red.circle.other.infra.database.rds.entity.family.FamilyMessage;
import com.red.circle.other.infra.database.rds.service.family.FamilyMemberInfoService;
import com.red.circle.other.infra.database.rds.service.family.FamilyMessageService;
import com.red.circle.other.inner.model.dto.user.UserProfileDTO;
import com.red.circle.tool.core.collection.CollectionUtils;
import com.red.circle.tool.core.date.TimestampUtils;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

/**
 * <p>
 * 工会消息列表.
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-23
 */
@Component
@RequiredArgsConstructor
public class FamilyMessageListExe {

  private final FamilyCommon familyCommon;
  private final UserProfileGateway userProfileGateway;
  private final FamilyMessageService familyMessageService;
  private final UserProfileAppConvertor userProfileAppConvertor;
  private final FamilyMemberInfoService familyMemberInfoService;

  public List<FamilyMsgListCO> execute(FamilyMessageListCmd cmd) {

    isExistFamily(cmd);

    FamilyMemberInfo familyMemberInfo = getFamilyMemberByUserId(cmd);

    if (!checkRole(familyMemberInfo)) {
      return Lists.newArrayList();
    }

    List<FamilyMessage> msgList = getMsgList(cmd, familyMemberInfo);
    if (CollectionUtils.isEmpty(msgList)) {
      return Lists.newArrayList();
    }

    Map<Long, UserProfileDTO> baseInfoMap = userProfileAppConvertor.toMapUserProfileDTO(
        userProfileGateway.mapByUserIds(getUserIds(msgList)));

    return msgList.stream().map(msg -> {

      UserProfileDTO baseInfo = baseInfoMap.get(msg.getSenderUser());

      return new FamilyMsgListCO()
          .setMsgId(msg.getId())
          .setFamilyUser(
              new FamilyUserCO()
                  .setId(baseInfo.getId())
                  .setUserNickname(baseInfo.getUserNickname())
                  .setUserAvatar(baseInfo.getUserAvatar())
          )
          .setCreateTime(TimestampUtils.convert(msg.getCreateTime()));
    }).toList();

  }

  private Set<Long> getUserIds(List<FamilyMessage> msgList) {
    return msgList.stream().map(FamilyMessage::getSenderUser).collect(Collectors.toSet());
  }

  private List<FamilyMessage> getMsgList(FamilyMessageListCmd cmd,
      FamilyMemberInfo familyMemberInfo) {
    return familyMessageService.listUntreatedById(familyMemberInfo.getFamilyId(), cmd.getLastId());
  }

  private boolean checkRole(FamilyMemberInfo familyMemberInfo) {
    if (Objects.isNull(familyMemberInfo)) {
      return Boolean.FALSE;
    }
    return familyCommon.isAdmin(familyMemberInfo.getMemberRole()) || familyCommon
        .isManage(familyMemberInfo.getMemberRole());
  }

  private FamilyMemberInfo getFamilyMemberByUserId(CommonCommand cmd) {
    return familyMemberInfoService
        .getFamilyMemberByUserId(cmd.getReqUserId());
  }

  private void isExistFamily(CommonCommand cmd) {
    familyCommon.isExistFamilyByUserId(cmd.getReqUserId());
  }

}
