package com.red.circle.other.app.command.material.query;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.red.circle.common.business.dto.cmd.app.AppUserIdCmd;
import com.red.circle.other.app.dto.clientobject.material.BadgeConfigCO;
import com.red.circle.other.app.dto.clientobject.material.BadgeTableCO;
import com.red.circle.other.infra.database.rds.entity.badge.BadgeConfig;
import com.red.circle.other.infra.database.rds.entity.badge.BadgePictureConfig;
import com.red.circle.other.infra.database.rds.service.badge.BadgeConfigService;
import com.red.circle.other.infra.database.rds.service.badge.BadgePictureConfigService;
import java.util.Objects;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR> on 2024/3/20
 */
@Component
@RequiredArgsConstructor
public class FamilyBadgeQryExe {

  private final BadgeConfigService badgeConfigService;
  private final BadgePictureConfigService badgePictureConfigService;

  public BadgeTableCO execute(AppUserIdCmd cmd) {
    //获取用户等级最高的工会徽章配置
    BadgeConfig badgeConfig = badgeConfigService.getUserMaxLevelFamilyBadge(cmd.getUserId());
    if (Objects.isNull(badgeConfig)) {
      //获取黑铁工会徽章
      BadgeConfig blackIronConfig = badgeConfigService.getOne(new LambdaQueryWrapper<BadgeConfig>()
          .eq(BadgeConfig::getType, "FAMILY")
          .eq(BadgeConfig::getDel, 0)
          .eq(BadgeConfig::getBadgeKey, "BLACK_IRON")
          .last("LIMIT 1"));
      //获取黑铁徽章图片配置
      BadgePictureConfig blackIronPictureConfig = getBadgePictureConfig(cmd.requireReqSysOrigin(),
          blackIronConfig.getId());
      return badgeTableCO(blackIronConfig, blackIronPictureConfig, false);
    }
    //获取徽章图片配置
    BadgePictureConfig blackIronPictureConfig = getBadgePictureConfig(cmd.requireReqSysOrigin(),
        badgeConfig.getId());
    return badgeTableCO(badgeConfig, blackIronPictureConfig, true);
  }

  /**
   * 获取黑铁徽章图片配置
   *
   * @param sysOrigin
   * @param badgeId
   * @return
   */
  private BadgePictureConfig getBadgePictureConfig(String sysOrigin, Long badgeId) {
    return badgePictureConfigService.getOne(
        new LambdaQueryWrapper<BadgePictureConfig>()
            .eq(BadgePictureConfig::getBadgeConfigId, badgeId)
            .eq(BadgePictureConfig::getSysOrigin, sysOrigin)
            .last("LIMIT 1"));
  }

  /**
   * 获取徽章配置
   *
   * @param badgeConfig
   * @param badgePictureConfig
   * @return
   */
  private BadgeTableCO badgeTableCO(BadgeConfig badgeConfig, BadgePictureConfig badgePictureConfig,
      Boolean activation) {
    BadgeConfigCO badgeConfigCO = new BadgeConfigCO();
    badgeConfigCO.setId(badgeConfig.getId());
    badgeConfigCO.setBadgeLevel(badgeConfig.getBadgeLevel());
    badgeConfigCO.setMilestone(badgeConfig.getMilestone());
    badgeConfigCO.setBadgeName(badgeConfig.getBadgeName());
    badgeConfigCO.setType(badgeConfig.getType());
    badgeConfigCO.setBadgeKey(badgeConfig.getBadgeKey());
    badgeConfigCO.setAnimationUrl(badgePictureConfig.getAnimationUrl());
    badgeConfigCO.setSelectUrl(badgePictureConfig.getSelectUrl());
    badgeConfigCO.setNotSelectUrl(badgePictureConfig.getNotSelectUrl());
    return new BadgeTableCO()
        .setBadgeConfig(badgeConfigCO)
        .setActivation(activation);
  }
}
