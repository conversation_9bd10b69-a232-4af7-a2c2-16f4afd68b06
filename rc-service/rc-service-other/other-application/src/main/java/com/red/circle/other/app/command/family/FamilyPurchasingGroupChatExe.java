package com.red.circle.other.app.command.family;

import com.red.circle.external.inner.endpoint.message.ImGroupClient;
import com.red.circle.external.inner.model.cmd.message.CreateGroupAddMemberCmd;
import com.red.circle.external.inner.model.cmd.message.CreateGroupCmd;
import com.red.circle.framework.core.asserts.ResponseAssert;
import com.red.circle.framework.core.response.CommonErrorCode;
import com.red.circle.other.app.dto.cmd.family.FamilyGroupChatQueryCmd;
import com.red.circle.other.infra.database.cache.service.other.EnumConfigCacheService;
import com.red.circle.other.infra.database.rds.entity.family.FamilyBaseInfo;
import com.red.circle.other.infra.database.rds.entity.family.FamilyGroupChatPayRecord;
import com.red.circle.other.infra.database.rds.entity.family.FamilyMemberInfo;
import com.red.circle.other.infra.database.rds.service.family.FamilyBaseInfoService;
import com.red.circle.other.infra.database.rds.service.family.FamilyGroupChatPayRecordService;
import com.red.circle.other.infra.database.rds.service.family.FamilyMemberInfoService;
import com.red.circle.other.infra.enums.family.FamilyRoleEnum;
import com.red.circle.other.infra.enums.family.FamilyStatusEnum;
import com.red.circle.other.inner.asserts.FamilyErrorCode;
import com.red.circle.other.inner.asserts.PropsErrorCode;
import com.red.circle.other.inner.enums.config.EnumConfigKey;
import com.red.circle.tool.core.collection.CollectionUtils;
import com.red.circle.tool.core.num.ArithmeticUtils;
import com.red.circle.tool.core.thread.ThreadPoolManager;
import com.red.circle.wallet.inner.endpoint.wallet.WalletGoldClient;
import com.red.circle.wallet.inner.model.cmd.GoldReceiptCmd;
import com.red.circle.wallet.inner.model.dto.WalletReceiptResDTO;
import com.red.circle.wallet.inner.model.enums.GoldOrigin;
import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 解锁工会群聊
 *
 * <AUTHOR> on 2023/12/21
 */
@Slf4j
@Component
@AllArgsConstructor
public class FamilyPurchasingGroupChatExe {

  private final ImGroupClient imGroupClient;
  private final WalletGoldClient walletGoldClient;
  private final FamilyBaseInfoService familyBaseInfoService;
  private final EnumConfigCacheService enumConfigCacheService;
  private final FamilyMemberInfoService familyMemberInfoService;
  private final FamilyGroupChatPayRecordService familyGroupChatPayRecordService;

  public BigDecimal execute(FamilyGroupChatQueryCmd cmd) {

    if (Objects.equals(enumConfigCacheService.getValueBool(EnumConfigKey.SUSPEND_CONSUMPTION,
        cmd.requireReqSysOrigin()), Boolean.TRUE)) {
      ResponseAssert.failure(CommonErrorCode.NOT_OPEN_ERROR);
    }

    ResponseAssert.isFalse(CommonErrorCode.NON_REPEATABLE,
        familyGroupChatPayRecordService.getByFamilyIdId(cmd.getFamilyId()));

    FamilyBaseInfo familyBaseInfo = familyBaseInfoService.getBaseInfoById(cmd.getFamilyId());
    ResponseAssert.notNull(CommonErrorCode.NOT_FOUND_RECORD_INFO, familyBaseInfo);
    ResponseAssert.isTrue(CommonErrorCode.NOT_FOUND_RECORD_INFO, Objects
        .equals(FamilyStatusEnum.NORMAL.name(), familyBaseInfo.getFamilyStatus()));

    FamilyMemberInfo familyMember = familyMemberInfoService.getFamilyMemberByUserId(
        cmd.requiredReqUserId());
    ResponseAssert.notNull(FamilyErrorCode.NOT_EXIST_FAMILY_INFO_DATA, familyMember);
    ResponseAssert.isTrue(CommonErrorCode.INSUFFICIENT_PERMISSION,
        Objects.equals(familyMember.getFamilyId(), cmd.getFamilyId()));
    ResponseAssert.isFalse(CommonErrorCode.INSUFFICIENT_PERMISSION,
        Objects.equals(familyMember.getMemberRole(), "MEMBER"));

    BigDecimal amount = enumConfigCacheService
        .getValueBigDecimal(EnumConfigKey.FAMILY_GROUP_CHAT_AMOUNT,
            cmd.requireReqSysOrigin());

    // 2040 配置错误
    ResponseAssert.isTrue(CommonErrorCode.CONFIGURATION_ERROR,
        ArithmeticUtils.gtZero(amount));

    // 扣款
    WalletReceiptResDTO receiptRes = walletGoldClient.changeBalance(
        GoldReceiptCmd.builder().appExpenditure()
            .userId(cmd.requiredReqUserId())
            .eventId(0L)
            .sysOrigin(cmd.requireReqSysOrigin())
            .origin(GoldOrigin.FAMILY_GROUP_CHAT_AMOUNT)
            .amount(amount)
            .build()
    ).getBody();

    // 5006 商品购买失败
    ResponseAssert.isTrue(PropsErrorCode.BUY_FALL,
        familyGroupChatPayRecordService.save(new FamilyGroupChatPayRecord()
            .setFamilyId(cmd.getFamilyId())
            .setUserId(cmd.requiredReqUserId())
            .setGroupState(Boolean.TRUE)
        )
    );

    // 创建创建群createFamilyGroup
    createFamilyGroup(cmd, familyBaseInfo);
    return receiptRes.getBalance().getDollarAmount();
  }

  private void createFamilyGroup(FamilyGroupChatQueryCmd cmd, FamilyBaseInfo familyBaseInfo) {
    String groupId = "FAMILY_" + familyBaseInfo.getFamilyAccount();
    FamilyMemberInfo familyMemberInfo = familyMemberInfoService
        .getAdmin(cmd.getFamilyId(), FamilyRoleEnum.ADMIN.getKey());
    ResponseAssert.notNull(FamilyErrorCode.NOT_EXIST_FAMILY_INFO_DATA, familyMemberInfo);

    Boolean isSuccess = imGroupClient.createPublicGroup(new CreateGroupCmd()
        .setOwnerAccount(Objects.toString(familyMemberInfo.getMemberUserId()))
        .setGroupId(groupId)
        .setFaceUrl(familyBaseInfo.getFamilyAvatar())
        .setName(familyBaseInfo.getFamilyName())
    ).getBody();

    if (!Objects.equals(isSuccess, Boolean.TRUE)) {
      log.warn("创建失败: {}", groupId);
      return;
    }

    ThreadPoolManager.getInstance().execute(() -> {
      List<FamilyMemberInfo> familyMemberInfos = familyMemberInfoService.listByFamilyId(
          cmd.getFamilyId());

      if (CollectionUtils.isEmpty(familyMemberInfos)) {
        return;
      }

      List<Long> userIds = familyMemberInfos.stream().map(FamilyMemberInfo::getMemberUserId)
          .toList();

      CollectionUtils.partition(userIds, 100)
          .forEach(userIdArray -> imGroupClient.addGroupMembers(
                  new CreateGroupAddMemberCmd()
                      .setGroupId(groupId)
                      .setUserIds(userIdArray)
              )
          );

    });
  }

}
