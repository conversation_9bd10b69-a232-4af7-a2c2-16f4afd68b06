package com.red.circle.other.app.command.family;

import com.red.circle.common.business.dto.cmd.AppExtCommand;
import com.red.circle.framework.core.asserts.ResponseAssert;
import com.red.circle.framework.core.response.CommonErrorCode;
import com.red.circle.other.app.dto.clientobject.family.FamilyCreateRulesCO;
import com.red.circle.other.infra.database.rds.entity.family.FamilyCreateRules;
import com.red.circle.other.infra.database.rds.service.family.FamilyCreateRulesService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

/**
 * <p>
 * 工会创建要求.
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-28
 */
@Component
@RequiredArgsConstructor
public class FamilyCreateRuleExe {

  private final FamilyCreateRulesService familyCreateRulesService;

  public FamilyCreateRulesCO execute(AppExtCommand cmd) {

    FamilyCreateRules rules = getFamilyCreateRules(cmd);

    ResponseAssert.notNull(CommonErrorCode.DATA_ERROR, rules);

    return new FamilyCreateRulesCO()
        .setPayCandy(rules.getPayCandy())
        .setUserWealthLevel(rules.getUserWealthLevel());
  }

  private FamilyCreateRules getFamilyCreateRules(AppExtCommand cmd) {
    return familyCreateRulesService.getFamilyCreateRules(cmd.getReqSysOrigin().getOrigin());
  }


}
