package com.red.circle.other.app.command.family;

import com.red.circle.framework.core.asserts.ResponseAssert;
import com.red.circle.framework.core.response.CommonErrorCode;
import com.red.circle.other.app.dto.clientobject.family.FamilyBaseInfoCO;
import com.red.circle.other.app.dto.cmd.family.FamilyBaseInfoQueryCmd;
import com.red.circle.other.infra.common.family.FamilyCommon;
import com.red.circle.other.infra.database.cache.service.other.EnumConfigCacheService;
import com.red.circle.other.infra.database.rds.entity.family.FamilyBaseInfo;
import com.red.circle.other.infra.database.rds.enums.OtherConfigEnum;
import com.red.circle.other.infra.database.rds.service.family.FamilyBaseInfoService;
import com.red.circle.other.infra.database.rds.service.family.FamilyLevelExpService;
import com.red.circle.other.infra.database.rds.service.family.FamilyMemberInfoService;
import com.red.circle.other.infra.enums.family.FamilyStatusEnum;
import com.red.circle.other.inner.enums.config.EnumConfigKey;
import com.red.circle.other.inner.model.dto.famliy.FamilyDetailsDTO;

import java.math.BigDecimal;
import java.util.Objects;
import java.util.Optional;

import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

/**
 * <p>
 * 工会基本信息.
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-20
 */
@Component
@RequiredArgsConstructor
public class FamilyBaseInfoExe {

    private final FamilyCommon familyCommon;
    private final FamilyLevelExpService familyLevelExpService;
    private final FamilyBaseInfoService familyBaseInfoService;
    private final FamilyMemberInfoService familyMemberInfoService;
    private final EnumConfigCacheService enumConfigCacheService;

    public FamilyBaseInfoCO execute(FamilyBaseInfoQueryCmd cmd) {

        FamilyBaseInfo baseInfo = getFamilyBaseInfo(cmd);

        FamilyDetailsDTO familyLevel = getCacheFamilyLevel(cmd, baseInfo.getId());

        return getFamilyInfoCO(baseInfo, familyLevel, cmd);
    }

    private FamilyBaseInfoCO getFamilyInfoCO(FamilyBaseInfo baseInfo,
                                             FamilyDetailsDTO familyLevel, FamilyBaseInfoQueryCmd cmd) {

        BigDecimal amount = Optional.ofNullable(enumConfigCacheService
                .getValueBigDecimal(EnumConfigKey.valueOf(OtherConfigEnum.FAMILY_GROUP_CHAT_AMOUNT.getKey()),
                        cmd.requireReqSysOrigin())).orElse(BigDecimal.ZERO);
        return new FamilyBaseInfoCO()
                .setAmount(amount)
                .setFamilyId(baseInfo.getId())
                .setFamilyAccount(baseInfo.getFamilyAccount())
                .setFamilyAvatar(baseInfo.getFamilyAvatar())
                .setFamilyName(baseInfo.getFamilyName())
                .setFamilyNotice(baseInfo.getFamilyNotice())
                .setCurrentExp(
                        familyLevelExpService.getExp(baseInfo.getId(), baseInfo.getFamilyLevelId()).longValue())
                .setCurrentMember(getFamilyMemberCount(baseInfo.getId()))
                .setLevelKey(familyLevel.getLevelKey())
                .setLevelExp(familyLevel.getLevelExp())
                .setAvatarFrameCover(familyLevel.getAvatarFrameCover())
                .setAvatarFrameSvg(familyLevel.getAvatarFrameSvg())
                .setBadgeCover(familyLevel.getBadgeCover())
                .setBadgeSvg(familyLevel.getBadgeSvg())
                .setMaxMember(familyLevel.getMaxMember())
                .setFamilyWhatapp(baseInfo.getFamilyWhatapp())
                .setLeaderIdCardPhoto(baseInfo.getLeaderIdCardPhoto())
                .setLevelBackgroundPicture(familyLevel.getLevelBackgroundPicture());
    }

    private FamilyDetailsDTO getCacheFamilyLevel(FamilyBaseInfoQueryCmd cmd, Long familyId) {
        return familyCommon.getFamilyDetails(cmd.getReqSysOrigin().getOrigin(), familyId);
    }

    private Integer getFamilyMemberCount(Long familyId) {
        return familyMemberInfoService.getFamilyMemberCount(familyId);
    }

    private FamilyBaseInfo getFamilyBaseInfo(FamilyBaseInfoQueryCmd cmd) {
        FamilyBaseInfo familyBaseInfo = familyBaseInfoService.getBaseInfoById(cmd.getFamilyId());
        ResponseAssert.notNull(CommonErrorCode.NOT_FOUND_RECORD_INFO, familyBaseInfo);
        ResponseAssert.isTrue(CommonErrorCode.NOT_FOUND_RECORD_INFO, Objects
                .equals(FamilyStatusEnum.NORMAL.name(), familyBaseInfo.getFamilyStatus()));
        return familyBaseInfo;
    }


}
