package com.red.circle.other.app.listener.gift.strategy;

import com.google.common.collect.Lists;
import com.red.circle.common.business.core.enums.GameStateEnum;
import com.red.circle.common.business.core.enums.SysOriginPlatformEnum;
import com.red.circle.external.inner.endpoint.message.OfficialNoticeClient;
import com.red.circle.external.inner.model.cmd.message.notice.official.NoticeExtTemplateTypeCmd;
import com.red.circle.external.inner.model.enums.message.OfficialNoticeTypeEnum;
import com.red.circle.mq.business.model.event.gift.OfflineProcessGiftEvent;
import com.red.circle.mq.business.model.event.task.TaskApprovalEvent;
import com.red.circle.mq.rocket.business.producer.TaskMqMessage;
import com.red.circle.mq.rocket.business.producer.UserMqMessageService;
import com.red.circle.other.app.common.game.GameIndoorTeamPkCommon;
import com.red.circle.other.app.common.gift.GameLuckyGiftCommon;
import com.red.circle.other.app.dto.clientobject.game.GameRoomPkUserCO;
import com.red.circle.other.domain.gateway.user.UserProfileGateway;
import com.red.circle.other.domain.gateway.user.ability.UserRegionGateway;
import com.red.circle.other.domain.model.user.UserProfile;
import com.red.circle.other.infra.common.family.FamilyCommon;
import com.red.circle.other.infra.database.cache.service.other.EnumConfigCacheService;
import com.red.circle.other.infra.database.cache.service.other.GameRoomPkCacheService;
import com.red.circle.other.infra.database.cache.service.other.GiftCacheService;
import com.red.circle.other.infra.database.cache.service.user.UserRegionCacheService;
import com.red.circle.other.infra.database.mongo.entity.gift.GiftAcceptUser;
import com.red.circle.other.infra.database.mongo.entity.gift.GiftGiveRunningWater;
import com.red.circle.other.infra.database.mongo.entity.gift.GiftValue;
import com.red.circle.other.infra.database.mongo.entity.gift.PremiumGiftWallMongo;
import com.red.circle.other.infra.database.mongo.entity.live.RoomProfile;
import com.red.circle.other.infra.database.mongo.entity.live.RoomProfileManager;
import com.red.circle.other.infra.database.mongo.service.activity.ConsumeActivityCountService;
import com.red.circle.other.infra.database.mongo.service.activity.RoomContributionActivityCountService;
import com.red.circle.other.infra.database.mongo.service.activity.RoomFanVotesActivityCountService;
import com.red.circle.other.infra.database.mongo.service.gift.GiftGiveRunningWaterService;
import com.red.circle.other.infra.database.mongo.service.gift.PremiumGiftWallMongoService;
import com.red.circle.other.infra.database.mongo.service.live.RoomContributionCountService;
import com.red.circle.other.infra.database.mongo.service.live.RoomContributionRankCountService;
import com.red.circle.other.infra.database.mongo.service.live.RoomProfileManagerService;
import com.red.circle.other.infra.database.mongo.service.sys.ActivityConfigService;
import com.red.circle.other.infra.database.mongo.service.user.count.UserGuardCountService;
import com.red.circle.other.infra.database.mongo.service.user.count.WeekFriendshipCardCountService;
import com.red.circle.other.infra.database.rds.entity.game.GameRoomPkIntegralRecord;
import com.red.circle.other.infra.database.rds.entity.game.GameRoomPkRecord;
import com.red.circle.other.infra.database.rds.entity.user.user.ConfessionChance;
import com.red.circle.other.infra.database.rds.entity.user.user.CpBlessRecord;
import com.red.circle.other.infra.database.rds.entity.user.user.CpRelationship;
import com.red.circle.other.infra.database.rds.entity.user.user.CpValue;
import com.red.circle.other.infra.database.rds.entity.user.user.FriendshipCard;
import com.red.circle.other.infra.database.rds.service.game.GameRoomPkIntegralRecordService;
import com.red.circle.other.infra.database.rds.service.game.GameRoomPkRecordService;
import com.red.circle.other.infra.database.rds.service.live.RoomContributionBalanceService;
import com.red.circle.other.infra.database.rds.service.live.RoomMemberService;
import com.red.circle.other.infra.database.rds.service.user.user.ConfessionChanceService;
import com.red.circle.other.infra.database.rds.service.user.user.ConsumptionLevelService;
import com.red.circle.other.infra.database.rds.service.user.user.CpBlessRecordService;
import com.red.circle.other.infra.database.rds.service.user.user.CpRelationshipService;
import com.red.circle.other.infra.database.rds.service.user.user.CpValueService;
import com.red.circle.other.infra.database.rds.service.user.user.FriendshipCardService;
import com.red.circle.other.inner.enums.config.EnumConfigKey;
import com.red.circle.other.inner.enums.game.GamePkTypeEnum;
import com.red.circle.other.inner.enums.material.BadgeKeyEnum;
import com.red.circle.other.inner.enums.material.GiftCurrencyType;
import com.red.circle.other.inner.enums.material.GiftSpecialEnum;
import com.red.circle.other.inner.enums.material.GiftTabEnum;
import com.red.circle.other.inner.model.dto.material.GiftConfigDTO;
import com.red.circle.tool.core.collection.CollectionUtils;
import com.red.circle.tool.core.collection.MapBuilder;
import com.red.circle.tool.core.date.LocalDateTimeUtils;
import com.red.circle.tool.core.date.TimestampUtils;
import com.red.circle.tool.core.date.ZonedDateTimeAsiaRiyadhUtils;
import com.red.circle.tool.core.json.JacksonUtils;
import com.red.circle.tool.core.regex.RegexConstant;
import com.red.circle.tool.core.sequence.IdWorkerUtils;
import com.red.circle.tool.core.text.StringUtils;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.sql.Timestamp;
import java.time.ZonedDateTime;
import java.util.*;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 礼物统计相关.
 *
 * <AUTHOR> on 2022/7/26
 */
@Slf4j
@Service("GIFT_STATISTICS_LISTENER")
@RequiredArgsConstructor
public class GiftCountStrategy implements GiftStrategy {

  private final FamilyCommon familyCommon;
  private final UserRegionGateway userRegionGateway;
  private final RoomMemberService roomMemberService;
  private final GameLuckyGiftCommon gameLuckyGiftCommon;
  private final UserMqMessageService userMqMessageService;
  private final UserGuardCountService userGuardCountService;
  private final FriendshipCardService friendshipCardService;
  private final ActivityConfigService activityConfigService;
  private final GameRoomPkCacheService gameRoomPkCacheService;
  private final GameIndoorTeamPkCommon gameIndoorTeamPkCommon;
  private final EnumConfigCacheService enumConfigCacheService;
  private final ConsumptionLevelService consumptionLevelService;
  private final GameRoomPkRecordService gameRoomPkRecordService;
  private final RoomProfileManagerService roomProfileManagerService;
  private final GiftGiveRunningWaterService giftGiveRunningWaterService;
  private final PremiumGiftWallMongoService premiumGiftWallMongoService;
  private final ConsumeActivityCountService consumeActivityCountService;
  private final RoomContributionCountService roomContributionCountService;
  private final WeekFriendshipCardCountService weekFriendshipCardCountService;
  private final RoomContributionBalanceService roomContributionBalanceService;
  private final GameRoomPkIntegralRecordService gameRoomPkIntegralRecordService;
  private final RoomContributionRankCountService roomContributionRankCountService;
  private final RoomFanVotesActivityCountService roomFanVotesActivityCountService;
  private final RoomContributionActivityCountService roomContributionActivityCountService;
  private final ConfessionChanceService confessionChanceService;
  private final GiftCacheService cacheGiftConfigManagerService;
  private final CpRelationshipService cpRelationshipService;
  private final CpValueService cpValueService;
  private final CpBlessRecordService cpBlessRecordService;
  private final UserProfileGateway userProfileRepository;
  private final OfficialNoticeClient officialService;
  private final TaskMqMessage taskMqMessage;
  private static final List<String> inRegionCodes = List.of("IN", "PK", "BD");
  private final UserRegionCacheService userRegionCacheService;


  @Override
  public void processor(OfflineProcessGiftEvent event) {

    GiftGiveRunningWater runningWater = giftGiveRunningWaterService.getByIdPrimary(
        event.getRunningWaterId());

    if (Objects.isNull(runningWater)) {
      log.warn("【礼物统计】没有找到流水信息，忽律处理");
      return;
    }

    GiftValue giftValue = runningWater.getGiftValue();

    if (!SysOriginPlatformEnum.isVoiceSystem(runningWater.getSysOrigin())
        || !GiftCurrencyType.GOLD.eq(giftValue.getCurrencyType())) {
      return;
    }
    giftGiveRunningWaterService.addLog(runningWater.getId(), "开始：礼物统计相关");

//    try {
      // 如果发送的是告白礼物则发送人与接收人全部增加一次告白机会
      saveConfessionChance(runningWater);
      // 如果是祝福礼物，增加CP礼物记录
      saveBlessingsRecord(runningWater);
//    } catch (Exception e) {
//      log.error("cptesttest catch:{}", e.getMessage(), e);
//    }

    // 幸运礼物比例
    BigDecimal luckyGiftRatio = gameLuckyGiftCommon.getLuckyGiftShareRatio();

    // 房间内贡献
    if (runningWater.getOriginId().matches(RegexConstant.NUMBER)) {
      RoomProfile roomProfile = roomProfileManagerService
          .getProfileById(Long.parseLong(runningWater.getOriginId()));

      if (Objects.nonNull(roomProfile)) {

        // 房间粉丝人气票活动 - 保留,接下会来做这个活动
//        roomFanVotesActivity(roomProfile.getId(), roomProfile.getUserId(), runningWater.getUserId(),
//            runningWater.getGiftId(), giftValue.getActualAmount().longValue());

        // 房间贡献相关
        roomContribution(runningWater, roomProfile);

        userMqMessageService.sendBadgeEvent(roomProfile.getUserId(),
            BadgeKeyEnum.FORTUNE_ROOM.name());

        // 团队房间内PK
        processIndoorTeamPk(runningWater, roomProfile.getId(), luckyGiftRatio);

        // 房间内PK
        processRoomPk(runningWater, luckyGiftRatio);

      }
    }
    // 20230406 财富和魅力积分双倍
    List<Long> ids = new ArrayList<>();
    /*ids.add(1772929668714803202L);
    ids.add(1772929262757945346L);
    ids.add(1772928725046734850L);
    ids.add(1772928394474102785L);
    ids.add(1772927999046266881L);
    ids.add(1779713184067002370L);
    ids.add(1779713147019141122L);
    ids.add(1779713096439603201L);
    ids.add(1779712965271560194L);
    ids.add(1779712864764469250L);
    ids.add(1782947206856622082L);
    ids.add(1792817888939679745L);*/
    List<Long> activityDoubleGiftIds = activityConfigService.listProcessingGiftIds(ids);

    boolean isDoubleLevelExpActivity = activityDoubleGiftIds.contains(runningWater.getGiftId());
    //log.warn("幸运礼物id{}",isDoubleLevelExpActivity);
    log.info("礼物统计{}", runningWater);
    boolean isLuckyGift = isLuckyGift(giftValue);

    BigDecimal actualAmount = BigDecimal.valueOf(
        getActualAmount(isLuckyGift, giftValue.getActualAmount(),
            luckyGiftRatio));

    BigDecimal wealthActualAmount = isDoubleLevelExpActivity
        ? actualAmount.multiply(new BigDecimal(2)).setScale(0, RoundingMode.DOWN)
        : actualAmount;

    consumptionLevelService.incrConsumptionGolds(runningWater.getUserId(), wealthActualAmount);
    //累计财富等级mq
    taskMqMessage.sendTask(TaskApprovalEvent.builder()
            .taskId(13)
            .userId(runningWater.getUserId())
            .day(LocalDateTimeUtils.nowFormat("yyyy-MM-dd"))
            .build());

    // 财富等级
//    consumptionLevelService.incrConsumptionGolds(runningWater.getUserId(), wealthActualAmount);

    userMqMessageService.sendBadgeEvent(runningWater.getUserId(), BadgeKeyEnum.WEALTH.name());

    boolean isHighPriceGift = checkHighPriceGift(runningWater);

    BigDecimal giftValueCount = BigDecimal.valueOf(getActualAmount(isLuckyGift,
        giftValue.getGiftValue(), luckyGiftRatio));

    runningWater.getAcceptUsers().forEach(accept -> {

      BigDecimal charmGiftValue = isDoubleLevelExpActivity
          ? giftValueCount.multiply(new BigDecimal(2)).setScale(0, RoundingMode.DOWN)
          : giftValueCount;

      // 记录魅力等级
      consumptionLevelService.incrConsumptionDiamond(accept.getAcceptUserId(), charmGiftValue);
      taskMqMessage.sendTask(TaskApprovalEvent.builder()
              .taskId(14)
              .userId(accept.getAcceptUserId())
              .day(LocalDateTimeUtils.nowFormat("yyyy-MM-dd"))
              .build());

      userMqMessageService.sendBadgeEvent(accept.getAcceptUserId(), BadgeKeyEnum.CHARM.name());

      // 保存高价值礼物公共礼物墙
      if (isHighPriceGift) {
        saveHighPriceGiftWall(runningWater, accept.getAcceptUserId());
      }

    });

    // 守护贡献
    userGuardCountService.incrLasts15Days(runningWater.getUserId(),
        runningWater.getAcceptUsers().stream()
            .map(GiftAcceptUser::getAcceptUserId)
            .toList(),
        giftValueCount.longValue()
    );

    // 工会
    processFamily(runningWater, isLuckyGift, luckyGiftRatio);

    // 处理关系卡
    processUserFriendCard(runningWater, isLuckyGift, luckyGiftRatio);

    // 累计活动
    // consumeActivity(runningWater, isLuckyGift, luckyGiftRatio);

    giftGiveRunningWaterService.addLog(runningWater.getId(), "结束：礼物统计相关");
  }

  private void saveConfessionChance(GiftGiveRunningWater runningWater) {
    GiftConfigDTO cache = cacheGiftConfigManagerService.getById(runningWater.getGiftId());
    if (Objects.isNull(cache)) {
      return;
    }

    if (Boolean.FALSE.equals(cache.getExplanationGift())) {
      return;
    }

    Timestamp date = TimestampUtils.nowPlusDays(7);
    runningWater.getAcceptUsers().forEach(acceptUser -> {

      if (Objects.equals(runningWater.getUserId(), acceptUser.getAcceptUserId())) {
        return;
      }

      List<ConfessionChance> confessionChances = Lists.newArrayList();

      for (int index = 0; index < runningWater.getGiftValue().getQuantity(); index++) {
        ConfessionChance sendChance = ConfessionChance.builder()
            .sysOrigin(runningWater.getSysOrigin())
            .userId(runningWater.getUserId())
            .confessionUserId(acceptUser.getAcceptUserId())
            .giftId(runningWater.getGiftId())
            .confessed(Boolean.FALSE)
            .expiredTime(date)
            .build();
        sendChance.setCreateUser(runningWater.getUserId());
        confessionChances.add(sendChance);

        ConfessionChance acceptChance = ConfessionChance.builder()
            .sysOrigin(runningWater.getSysOrigin())
            .userId(acceptUser.getAcceptUserId())
            .confessionUserId(runningWater.getUserId())
            .giftId(runningWater.getGiftId())
            .confessed(Boolean.FALSE)
            .expiredTime(date)
            .build();
        sendChance.setCreateUser(runningWater.getUserId());
        confessionChances.add(acceptChance);
      }

      confessionChanceService.saveBatch(confessionChances);
    });

  }

  private void saveBlessingsRecord(GiftGiveRunningWater runningWater) {
    //log.warn("cptesttest saveBlessingsRecord:{},{}",Boolean.FALSE.equals(runningWater.getCheckBlessingsGift()),JacksonUtils.toJson(runningWater));
    if (Boolean.FALSE.equals(runningWater.getCheckBlessingsGift())) {
      return;
    }

    GiftConfigDTO cache = cacheGiftConfigManagerService.getById(runningWater.getGiftId());
    //log.warn("cache:{}", cache);
    if (Objects.isNull(cache)) {
      return;
    }

    CpRelationship cpRelationship = cpRelationshipService.getByUserId(
        runningWater.getAcceptUsers().get(0).getAcceptUserId());
    //log.warn("cpRelationship:{}", cpRelationship);
    if (Objects.isNull(cpRelationship)) {
      return;
    }

    CpValue cpValue = cpValueService.getById(cpRelationship.getCpValId());
    //log.warn("cpValue:{}", cpValue);
    if (Objects.isNull(cpValue)) {
      return;
    }

    cpBlessRecordService.saveBatch(Lists.newArrayList(CpBlessRecord.builder()
            .sysOrigin(runningWater.getSysOrigin())
            .userId(cpRelationship.getUserId())
            .sendUserId(runningWater.getUserId())
            .giftCover(cache.getGiftPhoto())
            .cpValId(cpRelationship.getCpValId())
            .giftCount(Long.valueOf(runningWater.getGiftValue().getQuantity()))
            .reading(Objects.equals(cpRelationship.getUserId(), runningWater.getUserId()))
            .build(),
        CpBlessRecord.builder()
            .sysOrigin(runningWater.getSysOrigin())
            .userId(cpRelationship.getCpUserId())
            .sendUserId(runningWater.getUserId())
            .giftCover(cache.getGiftPhoto())
            .cpValId(cpRelationship.getCpValId())
            .giftCount(Long.valueOf(runningWater.getGiftValue().getQuantity()))
            .reading(Boolean.FALSE)
            .build()));

    cpValueService.incrBlessValue(cpValue.getId(), runningWater.getGiftValue().getGiftValue());

    UserProfile userProfile = userProfileRepository.getByUserId(runningWater.getUserId());
    // 发送祝福通知
    officialService.send(NoticeExtTemplateTypeCmd.builder()
            .toAccount(runningWater.getAcceptUsers().get(0).getAcceptUserId())
            .noticeType(OfficialNoticeTypeEnum.CP_BLESS)
            .templateParam(MapBuilder.builder()
                .put("nickname", userProfile.getUserNickname())
                .build())
        .build());

  }


  /**
   * 房间粉丝人气票活动.
   */
  private void roomFanVotesActivity(Long roomId, Long roomUserId, Long sendUserId, Long giftId,
      Long points) {

    // 礼物必须是指定礼物
    if (!Objects.equals(giftId, 1668107708094943234L)) {
      return;
    }

    // 送礼人必须是房间成员
    if (!Objects.equals(roomUserId, sendUserId) &&
        Boolean.FALSE.equals(roomMemberService.exists(roomId, sendUserId))) {
      return;
    }

    // 获得房主区域
    String code = userRegionGateway.getRegionCode(roomUserId);
    if (StringUtils.isBlank(code)) {
      return;
    }

    // 基于当前沙特时间计算
    ZonedDateTime nowTime = ZonedDateTimeAsiaRiyadhUtils.now();
    int hour = nowTime.getHour();
    int minute = nowTime.getMinute();
    Boolean isValidData = Boolean.FALSE;

    // 印度，孟加拉，巴基斯坦 = 北京时间 23:30-0:30 => 沙特时间 18:30-19:30
    if (inRegionCodes.contains(code) && ((hour == 18 && minute >= 30) || (hour == 19
        && minute <= 30))) {
      isValidData = Boolean.TRUE;
    }

    // 印尼 = 北京时间 20:30-21:30 => 沙特时间 15:30-16:30
    if ("ID".equals(code) && ((hour == 15 && minute >= 30) || (hour == 16 && minute <= 30))) {
      isValidData = Boolean.TRUE;
    }

    // 阿拉伯 = 北京时间 2:30-3:30 => 沙特时间 21:30-22:30
    if ("AR".equals(code) && ((hour == 21 && minute >= 30) || (hour == 22 && minute <= 30))) {
      isValidData = Boolean.TRUE;
    }

    // 无效数据
//    if (Boolean.FALSE.equals(isValidData)) {
//      return;
//    }

    roomFanVotesActivityCountService.incr(roomId, roomUserId, code, points);

  }

  /**
   * 房间贡献.
   */
  private void roomContribution(GiftGiveRunningWater runningWater, RoomProfile roomProfile) {

    // 送礼人与房主不在同一区域则不计算房间贡献相关数据
    String roomCode = userRegionGateway.getRegionCode(roomProfile.getUserId());
    String sendUserCode = userRegionGateway.getRegionCode(runningWater.getUserId());
    if (Boolean.FALSE.equals(Objects.equals(roomCode, sendUserCode))) {
      return;
    }

    BigDecimal luckyGiftRatio = gameLuckyGiftCommon.getLuckyGiftShareRatio();
    log.info("礼物统计{}", runningWater);
    BigDecimal actualAmount = BigDecimal.valueOf(
        getActualAmount(isLuckyGift(runningWater.getGiftValue()),
            runningWater.getGiftValue().getActualAmount(), luckyGiftRatio)
    );

    roomContributionRankCountService.incr(roomProfile.getSysOrigin(),
        roomProfile.getId(),
        runningWater.getUserId(),
        actualAmount);

    roomContributionBalanceService.incrTotalQuantity(roomProfile.getId(),
        roomProfile.getUserId(),
        actualAmount);

    roomContributionCountService.incrQuantity(roomProfile.getSysOrigin(), roomProfile.getId(),
        roomProfile.getUserId(), actualAmount);

    // 幸运礼物目标榜单比例
    BigDecimal luckyGiftTargetRatio = gameLuckyGiftCommon.getLuckyGiftTargetRatio(
        runningWater.getSysOrigin());
    log.info("礼物统计{}", runningWater);
    BigDecimal actualTargetAmount = BigDecimal.valueOf(
        getActualAmount(isLuckyGift(runningWater.getGiftValue()),
            runningWater.getGiftValue().getActualAmount(), luckyGiftTargetRatio));

    // 活动 2022.11.1 - 2036.11.30
    roomContributionActivityCountService.incrQuantity(roomProfile.getId(), actualTargetAmount);
  }


  /**
   * 累计金币活动， 20230719-20230801.
   */
  private void consumeActivity(GiftGiveRunningWater runningWater, boolean isLuckyGift,
      BigDecimal luckyGiftRatio) {

    int nowInt = ZonedDateTimeAsiaRiyadhUtils.nowDateToInt();

    if (nowInt >= 20230719 && nowInt < 20230801) {

      consumeActivityCountService.incrTarget(runningWater.getUserId(),
          userRegionGateway.getRegionId(runningWater.getUserId()),
          getActualAmount(isLuckyGift, runningWater.getGiftValue().getActualAmount(),
              luckyGiftRatio));

    }

  }


  /**
   * 处理 房间pk礼物
   */
  private void processRoomPk(GiftGiveRunningWater runningWater, BigDecimal luckyGiftRatio) {

    if (checkWeekStar(runningWater.getGiftValue())) {
      return;
    }

    RoomProfileManager roomManager = roomProfileManagerService.getById(
        Long.valueOf(runningWater.getOriginId()));
    if (Objects.isNull(roomManager)) {
      return;
    }

    GameRoomPkUserCO roomPkCache = getCacheRoomPk(roomManager.getId());
    if (Objects.isNull(roomPkCache)) {
      return;
    }
    log.info("礼物统计{}", runningWater);
    boolean isLuckyGift = isLuckyGift(runningWater.getGiftValue());

    BigDecimal actualAmount = BigDecimal.valueOf(getActualAmount(isLuckyGift,
        runningWater.getGiftValue().getActualAmount(), luckyGiftRatio));

    //保存房间与房间之间的PK
    if (Objects.equals(roomPkCache.getPkType(), GamePkTypeEnum.ROOM_VS_ROOM.name())) {

      calculationPkRoomVsRoom(roomPkCache, roomManager.getId(), actualAmount);

      saveGameRoomPkIntegralRecord(runningWater.getUserId(), runningWater.getSysOrigin(),
          actualAmount.longValue(), roomPkCache, roomManager.getId());

      Map<Long, String> userMap = userRegionGateway.mapRegionCode(Set.of(roomPkCache.getSponsorUserId()));
      if (userMap != null) {
        roomPkCache.setSponsorUserRegion(userMap.get(roomPkCache.getSponsorUserId()));
      }
      //roomPkCache.setSponsorUserRegion(userRegionCacheService.getUserRegions(roomPkCache.getSponsorUserId()));
      gameRoomPkCacheService.cacheRoomPk(roomPkCache.getId(), JacksonUtils.toJson(roomPkCache));
      return;
    }

    Long giftValue = getActualAmount(isLuckyGift, runningWater.getGiftValue().getGiftValue(),
        luckyGiftRatio);

    //保存用户对用户之间的Pk
    for (GiftAcceptUser acceptUser : runningWater.getAcceptUsers()) {

      if (!Objects.equals(roomPkCache.getRecipientUserId(), acceptUser.getAcceptUserId()) &&
          !Objects.equals(roomPkCache.getSponsorUserId(), acceptUser.getAcceptUserId())) {
        continue;
      }
      calculationPkUserIntegral(giftValue, roomPkCache, acceptUser.getAcceptUserId());

      saveGameRoomPkIntegralRecord(runningWater.getUserId(), runningWater.getSysOrigin(),
          giftValue, roomPkCache, acceptUser.getAcceptUserId());
    }
    Map<Long, String> userMap = userRegionGateway.mapRegionCode(Set.of(roomPkCache.getSponsorUserId()));
    if (userMap != null) {
      roomPkCache.setSponsorUserRegion(userMap.get(roomPkCache.getSponsorUserId()));
    }
    //roomPkCache.setSponsorUserRegion(userRegionCacheService.getUserRegions(roomPkCache.getSponsorUserId()));
    gameRoomPkCacheService.cacheRoomPk(roomPkCache.getId(), JacksonUtils.toJson(roomPkCache));
  }

  /**
   * 处理团队房间内PK游戏.
   */
  private void processIndoorTeamPk(GiftGiveRunningWater runningWater, Long roomId,
      BigDecimal luckyGiftRatio) {
    log.info("礼物统计{}", runningWater);
    gameIndoorTeamPkCommon.calculatePoints(runningWater.getAcceptUsers(), roomId,
        runningWater.getUserId(), BigDecimal.valueOf(
            getActualAmount(isLuckyGift(runningWater.getGiftValue()),
                runningWater.getGiftValue().getGiftValue(), luckyGiftRatio)));
  }

  /**
   * 累计pk房间VS房间积分
   */
  private void calculationPkRoomVsRoom(GameRoomPkUserCO roomPkCache, Long roomId,
      BigDecimal totalAmount) {

    if (Objects.equals(roomPkCache.getSponsorRoomId(), roomId)) {
      roomPkCache.setSponsorIntegral(roomPkCache.getSponsorIntegral() + totalAmount.longValue());
    }

    if (Objects.equals(roomPkCache.getRecipientRoomId(), roomId)) {
      roomPkCache.setRecipientIntegral(
          roomPkCache.getRecipientIntegral() + totalAmount.longValue());
    }
  }

  /**
   * 累计房间pk用户积分
   */
  private void calculationPkUserIntegral(Long acceptAmount, GameRoomPkUserCO roomPkCache,
      Long userId) {

    if (Objects.equals(roomPkCache.getSponsorUserId(), userId)) {

      roomPkCache.setSponsorIntegral(
          roomPkCache.getSponsorIntegral() + acceptAmount);
    }

    if (Objects.equals(roomPkCache.getRecipientUserId(), userId)) {

      roomPkCache.setRecipientIntegral(
          roomPkCache.getRecipientIntegral() + acceptAmount);
    }
  }


  private void saveGameRoomPkIntegralRecord(Long sendUserId, String sysOrigin, Long amount,
      GameRoomPkUserCO roomPkCache, Long acceptUserId) {

    GameRoomPkIntegralRecord pkIntegralRecord = new GameRoomPkIntegralRecord()
        .setId(IdWorkerUtils.getId())
        .setReceiverUserId(acceptUserId)
        .setGameRoomPkId(roomPkCache.getId())
        .setSysOrigin(sysOrigin)
        .setIntegral(amount)
        .setPkType(roomPkCache.getPkType());

    pkIntegralRecord.setCreateUser(sendUserId);
    pkIntegralRecord.setCreateTime(TimestampUtils.now());
    gameRoomPkIntegralRecordService.save(pkIntegralRecord);

  }


  private GameRoomPkUserCO getCacheRoomPk(Long roomId) {

    GameRoomPkRecord gameRoomPkRecord = gameRoomPkRecordService
        .getByRoomIdByStatus(roomId, GameStateEnum.STARTED);

    if (Objects.isNull(gameRoomPkRecord)) {
      return null;
    }

    return gameRoomPkCacheService.getRoomPk(gameRoomPkRecord.getId(), GameRoomPkUserCO.class);
  }

  private void processUserFriendCard(GiftGiveRunningWater runningWater, boolean isLuckyGift,
      BigDecimal luckyGiftRatio) {

    Long giftValue = getActualAmount(isLuckyGift, runningWater.getGiftValue().getGiftValue(),
        luckyGiftRatio);

    List<Long> acceptUserIds = runningWater.getAcceptUsers().stream()
        .map(GiftAcceptUser::getAcceptUserId)
        .collect(Collectors.toList());

    List<FriendshipCard> cards = friendshipCardService.listByCondition(
        runningWater.getUserId(), acceptUserIds
    );

    if (CollectionUtils.isEmpty(cards)) {
      return;
    }

    // 在接收礼物人的关系卡中找到自己的卡片，然后累加友谊值
    friendshipCardService.updateBatchById(cards.stream().peek(card -> card.setFriendshipValue(
            card.getFriendshipValue().add(BigDecimal.valueOf(giftValue))))
        .toList());

    // 友谊关系卡活动
    cards.forEach(card -> {
      // Initiate 发送人永远为 userIdOne
      Long userIdOne =
          Boolean.TRUE.equals(card.getInitiate()) ? card.getAcceptUserId() : card.getSendUserId();
      Long userIdTwo =
          Boolean.TRUE.equals(card.getInitiate()) ? card.getSendUserId() : card.getAcceptUserId();

      weekFriendshipCardCountService.incrThisWeekQuantity(runningWater.getSysOrigin(),
          card.getCardType(), userIdOne, userIdTwo, giftValue);
    });
  }


  private void processFamily(GiftGiveRunningWater runningWater, boolean isLuckyGift,
      BigDecimal luckyGiftRatio) {

    familyCommon.familyBusinessProcessing(runningWater.getUserId()
        , runningWater.getSysOrigin()
        , getActualAmount(isLuckyGift
            , runningWater.getGiftValue().getActualAmount()
            , luckyGiftRatio));
  }

  private boolean checkWeekStar(GiftValue giftValue) {
    return giftValue.getGiftType().contains(GiftSpecialEnum.STAR.name());
  }


  private boolean checkHighPriceGift(GiftGiveRunningWater runningWater) {

    BigDecimal cost = enumConfigCacheService
        .getValueBigDecimal(EnumConfigKey.HIGH_VALUE_GIFT_STANDARD, runningWater.getSysOrigin());

    if (Objects.isNull(cost)) {
      throw new IllegalArgumentException("Configuration error");
    }

    return runningWater.getGiftValue().getUnitPrice().compareTo(cost) >= 0;
  }

  private void saveHighPriceGiftWall(GiftGiveRunningWater runningWater, Long acceptUserId) {

    premiumGiftWallMongoService.add(new PremiumGiftWallMongo()
        .setId(IdWorkerUtils.getIdStr())
        .setGiftId(runningWater.getGiftId())
        .setAcceptUserId(acceptUserId)
        .setSendUserId(runningWater.getUserId())
        .setQuantity(runningWater.getGiftValue().getQuantity())
        .setSysOrigin(runningWater.getSysOrigin())
        .setCreateTime(TimestampUtils.now())
    );
  }

  private boolean isLuckyGift(GiftValue giftValue) {
    return giftValue.getGiftType().contains(GiftTabEnum.LUCKY_GIFT.name());
  }

  /**
   * 获得礼物额度.
   */
  private Long getActualAmount(boolean checkLuckyGift, BigDecimal amount,
      BigDecimal luckyGiftRatio) {

    log.warn("==-=-=-=--=是否是幸运礼物 {},{},{}", checkLuckyGift,amount,luckyGiftRatio);
    if (checkLuckyGift) {
      return amount.multiply(luckyGiftRatio).setScale(0, RoundingMode.DOWN).longValue();
    }
    return amount.longValue();

  }

}
