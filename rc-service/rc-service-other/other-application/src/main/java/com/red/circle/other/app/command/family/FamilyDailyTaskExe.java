package com.red.circle.other.app.command.family;

import com.red.circle.other.app.dto.cmd.family.FamilyDailyTaskCmd;
import com.red.circle.other.infra.common.family.FamilyCommon;
import com.red.circle.other.infra.database.rds.entity.family.FamilyDailyTaskTriggerRecord;
import com.red.circle.other.infra.database.rds.entity.family.FamilyMemberInfo;
import com.red.circle.other.infra.database.rds.service.family.FamilyDailyTaskTriggerRecordService;
import com.red.circle.other.infra.database.rds.service.family.FamilyMemberInfoService;
import com.red.circle.other.infra.enums.family.FamilyDailyTaskEnum;
import com.red.circle.tool.core.text.StringUtils;
import java.util.List;
import java.util.Objects;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

/**
 * <p>
 * 触发工会每日任务.
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-26
 */
@Component
@RequiredArgsConstructor
public class FamilyDailyTaskExe {

  private final FamilyCommon familyCommon;
  private final FamilyMemberInfoService familyMemberInfoService;
  private final FamilyDailyTaskTriggerRecordService familyDailyTaskTriggerRecordService;

  public void execute(FamilyDailyTaskCmd cmd) {

    FamilyDailyTaskEnum taskEnum = FamilyDailyTaskEnum.valueOf(cmd.getTaskType());
    if (StringUtils.isBlank(taskEnum.getKey())) {
      return;
    }

    FamilyMemberInfo member = getFamilyMemberByUserId(cmd);
    if (Objects.isNull(member)) {
      return;
    }

    List<FamilyDailyTaskTriggerRecord> taskTriggers = getTaskTriggers(cmd, member);

    if (isAddLoginDaily(taskEnum, taskTriggers)) {
      addExpAndTaskRecord(taskEnum, member, cmd);
      return;
    }

    if (isAddMicrophoneTenMin(taskEnum, taskTriggers)) {
      addExpAndTaskRecord(taskEnum, member, cmd);
      return;
    }

    if (isWatchLiveTenMin(taskEnum, taskTriggers)) {
      addExpAndTaskRecord(taskEnum, member, cmd);
    }
  }

  private void addExpAndTaskRecord(FamilyDailyTaskEnum taskEnum, FamilyMemberInfo member,
      FamilyDailyTaskCmd cmd) {

    familyCommon.familyBusinessProcessing(member.getMemberUserId(), member.getSysOrigin(),
        taskEnum.getExp());

    addFamilyDailyTaskRecord(cmd, member);
  }

  private boolean isWatchLiveTenMin(FamilyDailyTaskEnum taskEnum,
      List<FamilyDailyTaskTriggerRecord> taskTriggers) {
    return Objects.equals(taskEnum.getKey(), FamilyDailyTaskEnum.WATCH_LIVE_TEN_MIN.getKey())
        && taskTriggers.size() < taskEnum.getOpportunity();
  }

  private boolean isAddMicrophoneTenMin(FamilyDailyTaskEnum taskEnum,
      List<FamilyDailyTaskTriggerRecord> taskTriggers) {
    return Objects.equals(taskEnum.getKey(), FamilyDailyTaskEnum.MICROPHONE_TEN_MIN.getKey())
        && taskTriggers.size() < taskEnum.getOpportunity();
  }

  private boolean isAddLoginDaily(FamilyDailyTaskEnum taskEnum,
      List<FamilyDailyTaskTriggerRecord> taskTriggers) {
    return Objects.equals(taskEnum.getKey(), FamilyDailyTaskEnum.DAILY_LOGIN.getKey())
        && taskTriggers.size() < taskEnum.getOpportunity();
  }


  private void addFamilyDailyTaskRecord(FamilyDailyTaskCmd cmd, FamilyMemberInfo member) {
    familyDailyTaskTriggerRecordService.save(new FamilyDailyTaskTriggerRecord()
        .setSysOrigin(cmd.getReqSysOrigin().getOrigin())
        .setTaskType(cmd.getTaskType())
        .setFamilyMemberId(member.getId())
    );
  }

  private List<FamilyDailyTaskTriggerRecord> getTaskTriggers(FamilyDailyTaskCmd cmd,
      FamilyMemberInfo member) {
    return familyDailyTaskTriggerRecordService
        .listByCondition(new FamilyDailyTaskTriggerRecord()
            .setFamilyMemberId(member.getId())
            .setTaskType(cmd.getTaskType())
            .setSysOrigin(cmd.getReqSysOrigin().getOrigin())
        );
  }

  private FamilyMemberInfo getFamilyMemberByUserId(FamilyDailyTaskCmd cmd) {
    return familyMemberInfoService.getFamilyMemberByUserId(cmd.getReqUserId());
  }


}
