package com.red.circle.other.app.command.family;

import com.red.circle.mq.business.model.event.task.TaskApprovalEvent;
import com.red.circle.mq.rocket.business.producer.TaskMqMessage;
import com.red.circle.other.app.dto.clientobject.family.FamilyUserInfoCO;
import com.red.circle.other.app.dto.cmd.family.FamilyUserInfoQueryCmd;
import com.red.circle.other.infra.common.family.FamilyCommon;
import com.red.circle.other.infra.database.rds.entity.family.FamilyBaseInfo;
import com.red.circle.other.infra.database.rds.entity.family.FamilyMemberInfo;
import com.red.circle.other.infra.database.rds.service.family.FamilyBaseInfoService;
import com.red.circle.other.infra.database.rds.service.family.FamilyMemberInfoService;
import com.red.circle.other.inner.model.dto.famliy.FamilyDetailsDTO;
import java.util.Objects;

import com.red.circle.tool.core.date.LocalDateTimeUtils;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

/**
 * <p>
 * 成员在工会中的基本信息.
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-20
 */
@Component
@RequiredArgsConstructor
public class FamilyUserInfoExe {

  private final FamilyCommon familyCommon;
  private final FamilyBaseInfoService familyBaseInfoService;
  private final FamilyMemberInfoService familyMemberInfoService;
  private final TaskMqMessage taskMqMessage;

  public FamilyUserInfoCO execute(FamilyUserInfoQueryCmd cmd) {

    if (Boolean.FALSE.equals(getExistFamilyByUserId(cmd))) {
      return new FamilyUserInfoCO();
    }

    FamilyMemberInfo familyMemberInfo = getFamilyMemberByUserId(cmd);
    if (Objects.isNull(familyMemberInfo)) {
      return new FamilyUserInfoCO();
    }

    FamilyBaseInfo baseInfo = getBaseInfoById(familyMemberInfo);
    if (Objects.isNull(baseInfo)) {
      return new FamilyUserInfoCO();
    }
    //成长任务 创建或加入工会
    taskMqMessage.sendTask(TaskApprovalEvent.builder()
            .taskId(6)
            .userId(cmd.getUserId())
            .day(LocalDateTimeUtils.nowFormat("yyyy-MM-dd"))
            .build());
    return getBaseInfoCO(cmd.getReqSysOrigin().getOrigin(), familyMemberInfo, baseInfo);
  }

  private Boolean getExistFamilyByUserId(FamilyUserInfoQueryCmd cmd) {
    return familyCommon.isExistFamilyByUserId(cmd.getUserId());
  }

  private FamilyBaseInfo getBaseInfoById(FamilyMemberInfo familyMemberInfo) {
    return familyBaseInfoService.getBaseInfoById(familyMemberInfo.getFamilyId());
  }

  private FamilyMemberInfo getFamilyMemberByUserId(FamilyUserInfoQueryCmd cmd) {
    return familyMemberInfoService.getFamilyMemberByUserId(cmd.getUserId());
  }

  private FamilyUserInfoCO getBaseInfoCO(String sysOrigin, FamilyMemberInfo familyMemberInfo,
      FamilyBaseInfo baseInfo) {

    FamilyDetailsDTO familyLevel = getFamilyLevel(sysOrigin, baseInfo);

    return new FamilyUserInfoCO()
        .setFamilyId(baseInfo.getId())
        .setFamilyAccount(baseInfo.getFamilyAccount())
        .setFamilyAvatar(baseInfo.getFamilyAvatar())
        .setFamilyName(baseInfo.getFamilyName())
        .setFamilyRole(familyMemberInfo.getMemberRole())
        .setLevelKey(familyLevel.getLevelKey())
        .setCurrentMember(getFamilyMemberCount(baseInfo))
        .setMaxMember(familyLevel.getMaxMember())
        .setLevelBackgroundPicture(familyLevel.getLevelBackgroundPicture())
        .setAvatarFrameCover(familyLevel.getAvatarFrameCover())
        .setAvatarFrameSvg(familyLevel.getAvatarFrameSvg())
        .setBadgeCover(familyLevel.getBadgeCover())
        .setBadgeSvg(familyLevel.getBadgeSvg());
  }

  private FamilyDetailsDTO getFamilyLevel(String sysOrigin, FamilyBaseInfo baseInfo) {
    return familyCommon.getFamilyDetails(sysOrigin, baseInfo.getId());
  }

  private Integer getFamilyMemberCount(FamilyBaseInfo baseInfo) {
    return familyMemberInfoService.getFamilyMemberCount(baseInfo.getId());
  }

}
