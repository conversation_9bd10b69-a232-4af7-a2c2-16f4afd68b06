package com.red.circle.other.app.scheduler;

import com.red.circle.common.business.core.enums.SysOriginPlatformEnum;
import com.red.circle.common.business.enums.SendPropsOrigin;
import com.red.circle.component.redis.annotation.TaskCacheLock;
import com.red.circle.component.redis.service.RedisService;
import com.red.circle.other.infra.common.activity.PropsActivitySendCommon;
import com.red.circle.other.infra.common.activity.send.SendRewardGroup;
import com.red.circle.other.infra.database.mongo.entity.activity.RoomFanVotesActivityCount;
import com.red.circle.other.infra.database.mongo.service.activity.RoomFanVotesActivityCountService;
import com.red.circle.other.infra.database.rds.entity.activity.PropsActivityRuleConfig;
import com.red.circle.other.infra.database.rds.enums.CacheKeysEnum;
import com.red.circle.other.infra.database.rds.service.activity.PropsActivityRuleConfigService;
import com.red.circle.other.infra.database.rds.service.family.FamilyDailyTaskTriggerRecordService;
import com.red.circle.other.infra.utils.ZonedDateTimeUtils;
import com.red.circle.other.inner.enums.activity.PropsActivityTypeEnum;
import com.red.circle.tool.core.collection.CollectionUtils;
import java.time.Duration;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

/**
 * 每日任务数据.
 *
 * <AUTHOR> on 2022/8/19
 */
@Slf4j
@Component
@AllArgsConstructor
public class DailyTask {

  private final PropsActivitySendCommon sendPropsManager;
  private final RedisService redisService;
  private final PropsActivityRuleConfigService propsActivityRuleConfigService;
  private final RoomFanVotesActivityCountService roomFanVotesActivityCountService;
  private final FamilyDailyTaskTriggerRecordService familyDailyTaskTriggerRecordService;

  /**
   * 每日处理数据.
   */
  @Scheduled(cron = "0 0 0 1/1 * ?", zone = "Asia/Riyadh")
  @TaskCacheLock(key = "DAILY_PROCESS_TASK", expireSecond = 10000)
  public void dailyTask() {
    long startTime = System.currentTimeMillis();
    log.info("exec daily_process_task start");
    // 删除工会每日任务数据
    familyDailyTaskTriggerRecordService.delete().execute();
    log.info("exec daily_process_task end with {}",System.currentTimeMillis()-startTime);
  }

  /**
   * 每日19:31处理 [印度，孟加拉，巴基斯坦] 区域粉丝人气票活动发奖.
   */
//  @Scheduled(cron = "0 31 19 1/1 * ?", zone = "Asia/Riyadh")
  @TaskCacheLock(key = "ROOM_FAN_VOTES_ACTIVITY_IN_TASK", expireSecond = 10000)
  public void roomFanVotesActivityInTask() {
    long startTime = System.currentTimeMillis();
    log.info("exec room_fan_votes_activity_in_task start");
    log.warn("Start - 每日19:31处理 [印度，孟加拉，巴基斯坦] 区域粉丝人气票活动发奖");
    // 重置倒计时
    cacheCountDown(CacheKeysEnum.ROOM_FAN_VOTES_ACTIVITY_IN_COUNT_DOWN);

    SysOriginPlatformEnum.getVoiceSystems().forEach(sysOrigin -> {
      sendFanVotesReward(sysOrigin.name(), "IN");
      sendFanVotesReward(sysOrigin.name(), "PK");
      sendFanVotesReward(sysOrigin.name(), "BD");
    });

    log.warn("End - 每日19:31处理 [印度，孟加拉，巴基斯坦] 区域粉丝人气票活动发奖");
    log.info("exec room_fan_votes_activity_in_task end with {}",System.currentTimeMillis()-startTime);
  }

  /**
   * 每日18:30[印度，孟加拉，巴基斯坦] 重置倒计时，重置榜单.
   */
//  @Scheduled(cron = "0 30 18 1/1 * ?", zone = "Asia/Riyadh")
  @TaskCacheLock(key = "ROOM_FAN_VOTES_ACTIVITY_IN_START_TASK", expireSecond = 10000)
  public void roomFanVotesActivityInStartTask() {
    long startTime = System.currentTimeMillis();
    log.info("exec room_fan_votes_activity_in_start_task start");
    // 重置倒计时 60分钟
    long countDown = Duration.between(ZonedDateTimeUtils.nowAsiaRiyadh(),
        ZonedDateTimeUtils.nowAsiaRiyadh().plusHours(1)).toMillis() - 1000;
    redisService.setString(CacheKeysEnum.ROOM_FAN_VOTES_ACTIVITY_IN_COUNT_DOWN.name(),
        countDown, countDown, TimeUnit.MILLISECONDS);

    List<String> codes = List.of("IN", "PK", "BD");

    // 将昨天数据修改历史
    roomFanVotesActivityCountService.upHistory(codes);

    // 删除昨天之前的数据
    roomFanVotesActivityCountService.del(codes);
    log.info("exec room_fan_votes_activity_in_start_task end with {}",System.currentTimeMillis()-startTime);
  }

  /**
   * 每日16:31处理 [印尼] 区域粉丝人气票活动发奖.
   */
//  @Scheduled(cron = "0 31 16 1/1 * ?", zone = "Asia/Riyadh")
  @TaskCacheLock(key = "ROOM_FAN_VOTES_ACTIVITY_ID_TASK", expireSecond = 10000)
  public void roomFanVotesActivityIdTask() {
    long startTime = System.currentTimeMillis();
    log.info("exec room_fan_votes_activity_id_task start");
    log.warn("Start - 每日16:31处理 [印尼] 区域粉丝人气票活动发奖");

    // 重置倒计时
    cacheCountDown(CacheKeysEnum.ROOM_FAN_VOTES_ACTIVITY_ID_COUNT_DOWN);
    SysOriginPlatformEnum.getVoiceSystems()
        .forEach(sysOrigin -> sendFanVotesReward(sysOrigin.name(), "ID"));

    log.warn("End - 每日16:31处理 [印尼] 区域粉丝人气票活动发奖");
    log.info("exec room_fan_votes_activity_id_task end with {}",System.currentTimeMillis()-startTime);
  }

  /**
   * 每日15:30[印尼] 重置倒计时，重置榜单.
   */
//  @Scheduled(cron = "0 30 15 1/1 * ?", zone = "Asia/Riyadh")
  @TaskCacheLock(key = "ROOM_FAN_VOTES_ACTIVITY_ID_START_TASK", expireSecond = 10000)
  public void roomFanVotesActivityIdStartTask() {
    long startTime = System.currentTimeMillis();
    log.info("exec room_fan_votes_activity_id_start_task start");
    // 重置倒计时 60分钟
    long countDown = Duration.between(ZonedDateTimeUtils.nowAsiaRiyadh(),
        ZonedDateTimeUtils.nowAsiaRiyadh().plusHours(1)).toMillis() - 1000;
    redisService.setString(CacheKeysEnum.ROOM_FAN_VOTES_ACTIVITY_ID_COUNT_DOWN.name(),
        countDown, countDown, TimeUnit.MILLISECONDS);

    List<String> codes = List.of("ID");

    // 将昨天数据修改历史
    roomFanVotesActivityCountService.upHistory(codes);

    // 删除昨天之前的数据
    roomFanVotesActivityCountService.del(codes);
    log.info("exec room_fan_votes_activity_id_start_task end with {}",System.currentTimeMillis()-startTime);
  }

  /**
   * 每日22:31处理 [阿拉伯] 区域粉丝人气票活动发奖.
   */
//  @Scheduled(cron = "0 31 22 1/1 * ?", zone = "Asia/Riyadh")
  @TaskCacheLock(key = "ROOM_FAN_VOTES_ACTIVITY_AR_TASK", expireSecond = 10000)
  public void roomFanVotesActivityArTask() {
    long startTime = System.currentTimeMillis();
    log.info("exec room_fan_votes_activity_ar_task start");
    log.warn("Start - 每日22:31处理 [阿拉伯] 区域粉丝人气票活动发奖");

    // 重置倒计时
    cacheCountDown(CacheKeysEnum.ROOM_FAN_VOTES_ACTIVITY_AR_COUNT_DOWN);

    SysOriginPlatformEnum.getVoiceSystems()
        .forEach(sysOrigin -> sendFanVotesReward(sysOrigin.name(), "AR"));

    log.warn("End - 每日22:31处理 [阿拉伯] 区域粉丝人气票活动发奖");
    log.info("exec room_fan_votes_activity_ar_task end with {}",System.currentTimeMillis()-startTime);
  }

  /**
   * 每日21:30[阿拉伯] 重置倒计时，重置榜单.
   */
//  @Scheduled(cron = "0 30 21 1/1 * ?", zone = "Asia/Riyadh")
  @TaskCacheLock(key = "ROOM_FAN_VOTES_ACTIVITY_AR_START_TASK", expireSecond = 10000)
  public void roomFanVotesActivityArStartTask() {
    long startTime = System.currentTimeMillis();
    log.info("exec room_fan_votes_activity_ar_start_task start");
    // 重置倒计时 60分钟
    long countDown = Duration.between(ZonedDateTimeUtils.nowAsiaRiyadh(),
        ZonedDateTimeUtils.nowAsiaRiyadh().plusHours(1)).toMillis() - 1000;
    redisService.setString(CacheKeysEnum.ROOM_FAN_VOTES_ACTIVITY_AR_COUNT_DOWN.name(),
        countDown, countDown, TimeUnit.MILLISECONDS);

    List<String> codes = List.of("AR");

    // 将昨天数据修改历史
    roomFanVotesActivityCountService.upHistory(codes);

    // 删除昨天之前的数据
    roomFanVotesActivityCountService.del(codes);
    log.info("exec room_fan_votes_activity_ar_start_task end with {}",System.currentTimeMillis()-startTime);
  }

  private void sendFanVotesReward(String code, String sysOrigin) {

    List<RoomFanVotesActivityCount> votesCounts = roomFanVotesActivityCountService
        .listCurrent(code, sysOrigin, 10);
    if (CollectionUtils.isEmpty(votesCounts)) {
      log.warn("[execute] {} 区域粉丝人气票活动发奖,没有房间获奖", code);
      return;
    }

    List<PropsActivityRuleConfig> resources = listRuleConfigs(sysOrigin);
    if (CollectionUtils.isEmpty(resources) || resources.size() < 3) {
      log.warn("[execute] {} 区域粉丝人气票活动发奖,资源数据不对", code);
      return;
    }

    log.warn("[execute] {} 区域粉丝人气票活动发奖,获奖数据:{}", code, votesCounts);

    try {
      for (int index = 0; index < votesCounts.size(); index++) {

        if (index > 2) {
          break;
        }

        RoomFanVotesActivityCount count = votesCounts.get(index);

        if (Objects.equals(count.getAlreadySent(), Boolean.TRUE)) {
          log.warn("[execute] {} 区域粉丝人气票活动发奖,已发过奖,无需重复再发:{}", code, count);
          continue;
        }

        PropsActivityRuleConfig resource = resources.get(index);
        // 发送奖励
        sendPropsManager.sendActivityGroup(SendRewardGroup.builder()
            .trackId(resource.getId())
            .sysOrigin(SysOriginPlatformEnum.valueOf(sysOrigin))
            .acceptUserId(count.getUserId())
            .resourceGroupId(resource.getResourceGroupId())
            .origin(SendPropsOrigin.ROOM_FAN_VOTES_ACTIVITY)
            .build());
      }
    } catch (Exception ex) {
      log.warn("[execute] {} 区域粉丝人气票活动发奖: {}", code, ex.getMessage());
    }

    // 修改区域为已经领取
    roomFanVotesActivityCountService.upAlreadySent(code);

  }

  private List<PropsActivityRuleConfig> listRuleConfigs(String sysOrigin) {

    return propsActivityRuleConfigService.listPlatformByActivityType(
        sysOrigin,
        PropsActivityTypeEnum.ROOM_FAN_VOTES_ACTIVITY);
  }

  /**
   * 缓存区域粉丝人气票活动倒计时.
   */
  private void cacheCountDown(CacheKeysEnum keysEnum) {

    long countDown = Duration.between(ZonedDateTimeUtils.nowAsiaRiyadh(),
        ZonedDateTimeUtils.nowAsiaRiyadh().plusDays(1)).toMillis() - 3660000;
    redisService.setString(keysEnum.name(), countDown, countDown, TimeUnit.MILLISECONDS);

  }
}
