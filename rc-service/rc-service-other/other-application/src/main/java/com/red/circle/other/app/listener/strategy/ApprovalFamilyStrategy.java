package com.red.circle.other.app.listener.strategy;

import com.red.circle.common.business.core.enums.ApprovalStatusEnum;
import com.red.circle.common.business.enums.AccountStatusStrEnum;
import com.red.circle.external.inner.endpoint.message.NewsletterClient;
import com.red.circle.external.inner.model.cmd.message.NewsletterResultCmd;
import com.red.circle.external.inner.model.enums.message.NewsletterEvent;
import com.red.circle.mq.business.model.event.approval.ApprovalContent;
import com.red.circle.mq.business.model.event.approval.ApprovalEvent;
import com.red.circle.other.infra.database.rds.service.approval.ApprovalUserViolationHistoryService;
import com.red.circle.other.infra.database.rds.service.dynamic.DynamicMessageService;
import com.red.circle.other.infra.database.rds.service.family.FamilyBaseInfoService;
import com.red.circle.other.infra.enums.family.FamilyStatusEnum;
import com.red.circle.tool.core.collection.CollectionUtils;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 工会审核
 */
@Component("FAMILY_AVATAR_APPROVAL")
@RequiredArgsConstructor
public class ApprovalFamilyStrategy implements ApprovalStrategy {

    private final FamilyBaseInfoService familyBaseInfoService;

    @Override
    public void doOperation(ApprovalEvent param) {

//    AccountStatusStrEnum.FREEZE
        if (Objects.isNull(param) || CollectionUtils.isEmpty(param.getWaitApprovalUser())) {
            return;
        }
        ApprovalContent dto = param.getWaitApprovalUser().get(0);
        if (Objects.isNull(dto)) {
            return;
        }
        String status = FamilyStatusEnum.FREEZE.name();
        if (param.getApprovalStatus().eq(ApprovalStatusEnum.PASS.name())) {
            status = FamilyStatusEnum.NORMAL.name();
        }
        Set<Long> userIds = getUserIds(param);

        familyBaseInfoService.updateStatusByUserIds(userIds, status);
//    //保存数据
//    dynamicMessageService.saveBatch(dto.getContent(), dto.getContentId(), userIds, dto.getTags());
//
//    //发送消息标记
//    dynamicMessageService.mapByUserIdsByContentId(userIds)
//        .forEach((key, list) -> newsletterClient
//            .send(new NewsletterResultCmd()
//                .setUserIds(List.of(key))
//                .setEvent(NewsletterEvent.DYNAMIC_MESSAGE_QUANTITY)
//                .setData(list.size())
//            )
//        );

    }

    private Set<Long> getUserIds(ApprovalEvent param) {
        return param.getWaitApprovalUser().parallelStream().map(ApprovalContent::getUserId).collect(
                Collectors.toSet());
    }

}
