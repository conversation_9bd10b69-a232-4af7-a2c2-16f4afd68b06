package com.red.circle.other.app.command.user;

import com.red.circle.common.business.core.enums.ApprovalStatusEnum;
import com.red.circle.common.business.core.enums.DataApprovalTypeEnum;
import com.red.circle.common.business.core.enums.OpUserType;
import com.red.circle.common.business.core.enums.SysOriginPlatformEnum;
import com.red.circle.common.business.enums.AccountStatusEnum;
import com.red.circle.framework.core.asserts.ResponseAssert;
import com.red.circle.framework.core.response.CommonErrorCode;
import com.red.circle.framework.core.response.ResponseErrorCode;
import com.red.circle.mq.business.model.event.approval.ApprovalContent;
import com.red.circle.mq.business.model.event.approval.ApprovalEvent;
import com.red.circle.mq.rocket.business.producer.CensorMqMessage;
import com.red.circle.other.app.convertor.user.UserProfileAppConvertor;
import com.red.circle.other.app.dto.cmd.user.ManagerApprovalCmd;
import com.red.circle.other.domain.gateway.user.UserProfileGateway;
import com.red.circle.other.domain.model.user.UserConsumptionLevel;
import com.red.circle.other.infra.common.user.UserAccountCommon;
import com.red.circle.other.infra.database.rds.service.approval.ApprovalUserSettingDataService;
import com.red.circle.other.infra.database.rds.service.sys.AdministratorAuthService;
import com.red.circle.other.inner.asserts.PropsErrorCode;
import com.red.circle.other.inner.asserts.user.UserErrorCode;
import com.red.circle.other.inner.model.cmd.user.account.PunishmentAccountCmd;
import com.red.circle.other.inner.model.cmd.user.account.UnblockAccountCmd;
import com.red.circle.other.inner.model.dto.user.UserProfileDTO;
import com.red.circle.tool.core.sequence.IdWorkerUtils;
import com.red.circle.tool.core.text.StringUtils;
import com.red.circle.wallet.inner.error.WalletErrorCode;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

/**
 * 管理员审批服务.
 *
 * <AUTHOR> on 2021/4/6
 */
@Component
@RequiredArgsConstructor
public class ManagerApprovalNotPassCmdExe {

  private final CensorMqMessage censorMqMessage;
  private final UserAccountCommon userAccountCommon;
  private final UserProfileGateway userProfileGateway;
  private final UserProfileAppConvertor userProfileAppConvertor;
  private final AdministratorAuthService administratorAuthService;
  private final ApprovalUserSettingDataService approvalUserSettingDataService;
  private final List<String> trRegions = List.of("TR", "AZ");

  public void execute(ManagerApprovalCmd cmd) {

    ResponseAssert.isTrue(WalletErrorCode.INSUFFICIENT_BALANCE,
        administratorAuthService.existsAuth(cmd.requiredReqUserId(), cmd.getType().name()));

    List<String> authResources = administratorAuthService.listAuthResources(
        cmd.requiredReqUserId());

    if (authResources.contains("LIMIT_IN") && isLimit(cmd, "IN")) {
      return;
    }

    if (authResources.contains("LIMIT_IR") && isLimit(cmd, "IR")) {
      return;
    }

    if (authResources.contains("LIMIT_TR")
        || authResources.contains("LIMIT_AZ")) {

      UserProfileDTO userProfile = userProfileAppConvertor.toUserProfileDTO(
          userProfileGateway.getByUserId(cmd.getBeApprovalUserId())
      );
      ResponseAssert.notNull(UserErrorCode.USER_INFO_NOT_FOUND, userProfile);

      if (StringUtils.isBlank(userProfile.getCountryCode().toUpperCase())
          || !trRegions.contains(userProfile.getCountryCode().toUpperCase())) {
        return;
      }

      String lange = userProfileGateway.getLanguage(userProfile.getId());
      if (StringUtils.isBlank(lange) || !trRegions.contains(
          userProfile.getCountryCode().toUpperCase())) {
        return;
      }

//      if ( roomAgentBaseInfoService.isAgent(userProfile.getId())) {
//        return;
//      }

    }

    if (cmd.checkUnblockAccount()) {
      userAccountCommon.unblock(new UnblockAccountCmd()
          .setBeOptUserId(cmd.getBeApprovalUserId())
          .setOptUserId(cmd.requiredReqUserId())
          .setDescription("【管理员审批】解锁账号状态-" + cmd.requiredReqUserId())
      );
      return;
    }

    if (cmd.checkApprovalUserAccountFreeze() || cmd.checkApprovalUserAccountArchive()) {
      approvalUserAccount(cmd);
      return;
    }

    if (Objects.isNull(cmd.getContentId())) {
      ResponseAssert.failure(ResponseErrorCode.REQUEST_PARAMETER_ERROR, "contentId is required.");
    }

    if (StringUtils.isBlank(cmd.getContent())) {
      ResponseAssert.failure(ResponseErrorCode.REQUEST_PARAMETER_ERROR, "content is required.");
    }

    censorMqMessage.approval(new ApprovalEvent()
        //App审核人员
        .setOpsUserId(cmd.requiredReqUserId())
        .setOpUserType(OpUserType.APP)
        .setApprovalId(IdWorkerUtils.getId())
        .setApprovalType(changeApprovalStatusReturnType(cmd))
        .setApprovalStatus(ApprovalStatusEnum.NOT_PASS)
        .setWaitApprovalUser(Collections.singletonList(new ApprovalContent()
                .setUserId(cmd.getBeApprovalUserId())
                .setContentId(cmd.getContentId())
                .setContent(cmd.getContent())
                .setTags("-")
            )
        )
    );
  }

  private boolean isLimit(ManagerApprovalCmd cmd, String code) {
    UserProfileDTO userProfile = userProfileAppConvertor.toUserProfileDTO(
        userProfileGateway.getByUserId(cmd.getBeApprovalUserId())
    );
    ResponseAssert.notNull(UserErrorCode.USER_INFO_NOT_FOUND, userProfile);

    if (StringUtils.isBlank(userProfile.getCountryCode())
        || !StringUtils.equalsIgnoreCase(code, userProfile.getCountryCode())) {
      return true;
    }
    return false;
  }

  private void approvalUserAccount(ManagerApprovalCmd cmd) {
    if (cmd.checkApprovalUserAccountFreeze()) {
      UserConsumptionLevel userLevel = userProfileGateway.getUserConsumptionLevel(
              SysOriginPlatformEnum.valueOf("MARCIE"),
              cmd.getBeApprovalUserId());

      if (userLevel.getWealthLevel() >= 3) {
        ResponseAssert.failure(PropsErrorCode.USER_NOT_OPERATION_ENOUGH);
      }



    if (cmd.checkApprovalUserAccountArchive()) {
      userAccountCommon.punishmentPro(new PunishmentAccountCmd()
          .setBeOptUserId(cmd.getBeApprovalUserId())
          .setOptUserId(cmd.requiredReqUserId())
          .setStatus(AccountStatusEnum.ARCHIVE_DEVICE)
          .setDescription("【管理员审批】封号+设备-" + cmd.requiredReqUserId())
      );
      return;
    }
      userAccountCommon.punishmentPro(new PunishmentAccountCmd()
          .setBeOptUserId(cmd.getBeApprovalUserId())
          .setOptUserId(cmd.requiredReqUserId())
          .setStatus(AccountStatusEnum.FREEZE)
          .setDescription("【管理员审批】冻结-" + cmd.requiredReqUserId())
      );
    }
  }

  private DataApprovalTypeEnum changeApprovalStatusReturnType(ManagerApprovalCmd cmd) {
    if (cmd.checkApprovalUserAvatar()) {
      approvalUserSettingDataService.updateApprovalStatus(cmd.getBeApprovalUserId(),
          DataApprovalTypeEnum.AVATAR, ApprovalStatusEnum.NOT_PASS, cmd.requiredReqUserId());
      return DataApprovalTypeEnum.AVATAR;
    }
    if (cmd.checkApprovalUserNickname()) {
      approvalUserSettingDataService.updateApprovalStatus(cmd.getBeApprovalUserId(),
          DataApprovalTypeEnum.NICKNAME, ApprovalStatusEnum.NOT_PASS, cmd.requiredReqUserId());
      return DataApprovalTypeEnum.NICKNAME;
    }

    if (cmd.checkApprovalRoomCover()) {
      approvalUserSettingDataService.updateApprovalStatus(cmd.getBeApprovalUserId(),
          DataApprovalTypeEnum.ROOM_AVATAR, ApprovalStatusEnum.NOT_PASS, cmd.requiredReqUserId());
      return DataApprovalTypeEnum.ROOM_AVATAR;
    }

    if (cmd.checkApprovalRoomNickname()) {
      approvalUserSettingDataService.updateApprovalStatus(cmd.getBeApprovalUserId(),
          DataApprovalTypeEnum.ROOM_NICKNAME, ApprovalStatusEnum.NOT_PASS,
          cmd.requiredReqUserId());
      return DataApprovalTypeEnum.ROOM_NICKNAME;
    }

    if (cmd.checkApprovalRoomNotice()) {
      approvalUserSettingDataService.updateApprovalStatus(cmd.getBeApprovalUserId(),
          DataApprovalTypeEnum.ROOM_NOTICE, ApprovalStatusEnum.NOT_PASS, cmd.requiredReqUserId());
      return DataApprovalTypeEnum.ROOM_NOTICE;
    }

    // 1006 类型不在范围
    ResponseAssert.failure(CommonErrorCode.TYPE_IS_NOT_IN_SCOPE);
    return null;
  }

}
