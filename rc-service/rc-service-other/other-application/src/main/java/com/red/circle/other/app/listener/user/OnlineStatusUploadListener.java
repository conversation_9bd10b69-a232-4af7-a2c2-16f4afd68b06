package com.red.circle.other.app.listener.user;

import com.red.circle.component.mq.MessageEventProcess;
import com.red.circle.component.mq.MessageEventProcessDescribe;
import com.red.circle.component.mq.config.RocketMqMessageListener;
import com.red.circle.component.mq.service.Action;
import com.red.circle.component.mq.service.ConsumerMessage;
import com.red.circle.component.mq.service.MessageListener;
import com.red.circle.mq.business.model.event.user.UserOnlineStatusEvent;
import com.red.circle.mq.rocket.business.streams.OnlineStatusUploadSink;
import com.red.circle.other.domain.gateway.user.UserProfileGateway;
import com.red.circle.other.domain.gateway.user.ability.UserRegionGateway;
import com.red.circle.other.domain.gateway.user.ability.UserSVipGateway;
import com.red.circle.other.domain.model.user.UserProfile;
import com.red.circle.other.infra.common.user.OnlineStatusCommon;
import com.red.circle.other.infra.database.cache.service.user.CountUserActiveIndexService;
import com.red.circle.other.infra.database.mongo.entity.user.status.OnlineUser;
import com.red.circle.other.infra.database.mongo.service.team.team.TeamMemberService;
import com.red.circle.other.infra.database.mongo.service.user.status.OnlineUserService;
import com.red.circle.other.infra.database.rds.service.user.user.ExpandService;
import com.red.circle.other.infra.database.rds.service.user.user.OnlineTimeService;
import com.red.circle.other.inner.enums.user.UserOnlineStatusEnum;
import com.red.circle.other.inner.model.dto.user.reigon.UserRegionDTO;
import com.red.circle.tool.core.date.DateUtils;
import com.red.circle.tool.core.date.TimestampUtils;
import com.red.circle.tool.core.parse.DataTypeUtils;
import com.red.circle.tool.core.sequence.IdWorkerUtils;
import com.red.circle.wallet.inner.endpoint.wallet.WalletGoldClient;
import java.sql.Timestamp;
import java.util.Objects;
import java.util.Optional;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR> on 2020/9/22
 */
@Slf4j
@RocketMqMessageListener(groupId = OnlineStatusUploadSink.INPUT, tag = OnlineStatusUploadSink.TAG)
@RequiredArgsConstructor
public class OnlineStatusUploadListener implements MessageListener {

  private final ExpandService expandService;
  private final UserSVipGateway userSVipGateway;
  private final WalletGoldClient walletGoldClient;
  private final UserRegionGateway userRegionGateway;
  private final TeamMemberService teamMemberService;
  private final OnlineUserService onlineUserService;
  private final OnlineTimeService onlineTimeService;
  private final UserProfileGateway userProfileGateway;
  private final OnlineStatusCommon onlineStatusCommon;
  private final MessageEventProcess messageEventProcess;
  private final CountUserActiveIndexService countUserActiveIndexService;

  @Override
  public Action consume(ConsumerMessage message) {
    return messageEventProcess.consume(
        MessageEventProcessDescribe.builder()
            .logTag("在线状态")
            .consumeMsgTimeoutMinute(10)
            .repeatConsumeMinute(1)
            .repeatConsumeTag("OnlineStatusUpload")
            .checkConsumeTag(OnlineStatusUploadSink.TAG)
            .message(message)
            .build(),
        UserOnlineStatusEvent.class,
        eventBody -> {

          UserProfile userProfile = userProfileGateway.getByUserId(eventBody.getUserId());

          if (Objects.isNull(userProfile)) {
            log.warn("[在线状态]参数错误 user Not Found：{}", eventBody.getUserId());
            return;
          }

          // 添加在线用户
          addOnlineUser(eventBody, userProfile);
          // 团队成员活跃时间刷新
          teamMemberService.updateActiveTimeNow(eventBody.getUserId());
          // 用户活跃时间刷新
          expandService.updateLastActiveTime(eventBody.getUserId());

          // 累计在线时长
          if (Objects.equals(eventBody.getTask(), Boolean.TRUE)) {
            onlineTimeService
                .inrOnlineOneMinute(eventBody.getUserId()
                    , eventBody.getSysOrigin().name()
                );
          }

          // 检查db缓存余额同步
          walletGoldClient.syncCacheDbCheck(eventBody.getUserId(), 1000L);

          // 统计活跃主播
//          countActiveUser(userProfile);
        }
    );

  }

  private void countActiveUser(UserProfile userProfile) {
    long account = Objects.hash(userProfile.getAccount());
    if (userProfileGateway.checkTeamMember(userProfile.getId())) {
      countUserActiveIndexService.countAnchorActiveUser(account);
      countUserActiveIndexService.countAnchorActiveUser(userProfile.getOriginSys(), account);
      return;
    }

    countUserActiveIndexService.countOrdinaryActiveUser(account);
    countUserActiveIndexService.countOrdinaryActiveUser(userProfile.getOriginSys(), account);
  }

  private void addOnlineUser(UserOnlineStatusEvent event, UserProfile userProfile) {
    if (StringUtils.isBlank(event.getSessionId())) {
      return;
    }
    onlineUserService.saveOrUpdate(new OnlineUser()
        .setId(userProfile.getId())
        .setUserId(userProfile.getId())
        .setTimingId(IdWorkerUtils.getId())
        .setSysOrigin(Objects.toString(event.getSysOrigin()))
        .setCountryCode(userProfile.getCountryCode())
        .setGender(userProfile.getUserSex())
        .setRegionCode(Optional.ofNullable(
            userRegionGateway.getUserRegion(userProfile.getId())
        ).map(UserRegionDTO::getRegionCode).orElse("OTHER"))
        .setStatus(StringUtils.isNotBlank(event.getStatus())
            ? UserOnlineStatusEnum.valueOf(event.getStatus())
            : UserOnlineStatusEnum.IDLE)
        .setWeights(onlineStatusCommon.getWeights(userProfile.getId()))
        .setExpiredTime(TimestampUtils.nowPlusSeconds(90))
        .setCreateTime(TimestampUtils.now())
        .setSessionId(DataTypeUtils.toLong(event.getSessionId()))
        .setSvipLevel(userSVipGateway.checkSVipIdentity(userProfile.getId())));
  }

}
