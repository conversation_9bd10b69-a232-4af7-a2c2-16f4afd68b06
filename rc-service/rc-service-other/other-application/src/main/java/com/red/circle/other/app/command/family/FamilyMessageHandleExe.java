package com.red.circle.other.app.command.family;

import com.red.circle.external.inner.endpoint.message.ImMessageClient;
import com.red.circle.framework.core.asserts.ResponseAssert;
import com.red.circle.framework.core.response.CommonErrorCode;
import com.red.circle.other.app.dto.cmd.family.FamilyMessageHandleCmd;
import com.red.circle.other.infra.common.family.FamilyCommon;
import com.red.circle.other.infra.database.rds.entity.family.FamilyMemberInfo;
import com.red.circle.other.infra.database.rds.entity.family.FamilyMessage;
import com.red.circle.other.infra.database.rds.service.family.FamilyMemberInfoService;
import com.red.circle.other.infra.database.rds.service.family.FamilyMessageService;
import com.red.circle.other.infra.enums.family.FamilyMsgBusinessEnum;
import com.red.circle.other.infra.enums.family.FamilyRoleEnum;
import com.red.circle.other.inner.asserts.FamilyErrorCode;
import com.red.circle.other.inner.model.dto.famliy.FamilyDetailsDTO;
import com.red.circle.tool.core.sequence.IdWorkerUtils;
import com.red.circle.tool.core.text.StringUtils;
import java.util.Objects;
import java.util.Optional;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

/**
 * <p>
 * 工会消息处理.
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-23
 */
@Component
@RequiredArgsConstructor
public class FamilyMessageHandleExe {

  private final FamilyCommon familyCommon;
  private final ImMessageClient imMessageClient;
  private final FamilyMessageService familyMessageService;
  private final FamilyMemberInfoService familyMemberInfoService;

  public void execute(FamilyMessageHandleCmd cmd) {

    isExistFamily(cmd);

    FamilyMemberInfo approved = getApprovedByUserId(cmd);

    FamilyMessage msg = getMessage(cmd, approved);

    updateMsg(cmd, msg);

    FamilyDetailsDTO levelConfig = getFamilyLevel(approved);

    if (isNotJoinFamily(cmd, approved, msg, levelConfig)) {
      return;
    }
    userJoinFamily(cmd, msg, levelConfig);

    sendMsg(approved.getMemberUserId(), msg.getSenderUser(), "Welcome to join the big family");

  }

  private boolean isNotJoinFamily(FamilyMessageHandleCmd cmd, FamilyMemberInfo approved,
      FamilyMessage msg, FamilyDetailsDTO levelConfig) {

    if (Objects.nonNull(getFamilyMemberByUserId(msg.getSenderUser()))) {
      sendMsg(approved.getMemberUserId(), msg.getSenderUser(),
          FamilyErrorCode.THERE_ARE_FAMILIES_ERROR.getMessage());
      return true;
    }

    if (Objects.equals(FamilyMsgBusinessEnum.valueOf(cmd.getEvent()).name(),
        FamilyMsgBusinessEnum.REFUSE.name())) {
      sendMsg(approved.getMemberUserId(), msg.getSenderUser(),
          FamilyErrorCode.FAMILY_REFUSE_JOIN.getMessage());
      return true;
    }

    Integer familyCurrentMemberCount = getMemberCount(levelConfig);

    if (familyCurrentMemberCount >= levelConfig.getMaxMember()) {
      ResponseAssert.failure(FamilyErrorCode.FAMILY_MEMBER_MAX);
      return true;
    }

    return false;
  }

  private void sendMsg(Long fromAccount, Long toAccount, String text) {
    imMessageClient.sendMessageText(fromAccount, toAccount, text);
  }

  private void userJoinFamily(FamilyMessageHandleCmd cmd, FamilyMessage msg,
      FamilyDetailsDTO levelConfig) {

    Long memberId = IdWorkerUtils.getId();

    addFamilyMember(memberId, levelConfig.getFamilyId(), msg.getSenderUser(),
        cmd.getReqSysOrigin().getOrigin());

    sendHonorReward(msg.getSenderUser(), levelConfig);
  }

  private Integer getMemberCount(FamilyDetailsDTO levelConfig) {
    return Optional
        .ofNullable(familyMemberInfoService.getFamilyMemberCount(levelConfig.getFamilyId()))
        .orElse(0);
  }

  private FamilyMemberInfo getFamilyMemberByUserId(Long senderUser) {
    return familyMemberInfoService.getFamilyMemberByUserId(senderUser);
  }

  private void sendHonorReward(Long senderUser, FamilyDetailsDTO levelConfig) {
    familyCommon.sendFamilyHonorToMember(senderUser,
        levelConfig.getAvatarFrameId(), levelConfig.getBadgeId());
  }

  private void addFamilyMember(Long memberId, Long familyId, Long userId, String sysOrigin) {
    familyMemberInfoService.save(new FamilyMemberInfo()
        .setId(memberId)
        .setFamilyId(familyId)
        .setSysOrigin(sysOrigin)
        .setMemberUserId(userId)
        .setMemberRole(FamilyRoleEnum.MEMBER.name())
    );
    familyCommon.addGroup(familyId, userId);
  }

  private FamilyDetailsDTO getFamilyLevel(FamilyMemberInfo familyApproved) {
    return familyCommon
        .getFamilyDetails(familyApproved.getSysOrigin(), familyApproved.getFamilyId());
  }

  private void updateMsg(FamilyMessageHandleCmd cmd, FamilyMessage msg) {
    msg.setApproveUser(cmd.getReqUserId());
    msg.setStatus(Boolean.TRUE);
    msg.setBusinessEvent(cmd.getEvent());
    msg.setUpdateUser(cmd.getReqUserId());
    familyMessageService.updateSelectiveById(msg);
  }

  private FamilyMessage getMessage(FamilyMessageHandleCmd cmd, FamilyMemberInfo familyMemberInfo) {

    FamilyMessage msg = familyMessageService.getById(cmd.getFamilyMessageId());

    ResponseAssert.notNull(CommonErrorCode.NOT_FOUND_RECORD_INFO, msg);

    ResponseAssert.isFalse(CommonErrorCode.MSG_REPEAT_PROCESSING, msg.getStatus());

    ResponseAssert.isTrue(FamilyErrorCode.NOT_EXIST_FAMILY_INFO_DATA,
        Objects.equals(msg.getFamilyId(), familyMemberInfo.getFamilyId()));

    return msg;
  }

  private void isExistFamily(FamilyMessageHandleCmd cmd) {
    ResponseAssert.isTrue(FamilyErrorCode.NOT_EXIST_FAMILY_INFO_DATA,
        familyCommon.isExistFamilyByUserId(cmd.getReqUserId()));

    ResponseAssert.isFalse(CommonErrorCode.REQUIRED_FIELDS_CANNOT_BE_IGNORED,
        StringUtils.isBlank(FamilyMsgBusinessEnum.valueOf(cmd.getEvent()).name()));
  }

  private FamilyMemberInfo getApprovedByUserId(FamilyMessageHandleCmd cmd) {

    FamilyMemberInfo familyMemberInfo = familyMemberInfoService
        .getFamilyMemberByUserId(cmd.getReqUserId());

    ResponseAssert.notNull(FamilyErrorCode.NOT_EXIST_FAMILY_INFO_DATA, familyMemberInfo);

    ResponseAssert.isTrue(CommonErrorCode.INSUFFICIENT_PERMISSION,
        Objects.equals(familyMemberInfo.getMemberRole(), FamilyRoleEnum.ADMIN.name()) || Objects
            .equals(familyMemberInfo.getMemberRole(), FamilyRoleEnum.MANAGE.name()));

    return familyMemberInfo;
  }
}
