package com.red.circle.other.app.common.game;

import com.google.common.base.Throwables;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.red.circle.auth.inner.model.dto.activity.rule.GameBurstCrystalContent;
import com.red.circle.common.business.core.enums.SysOriginPlatformEnum;
import com.red.circle.common.business.enums.SendPropsOrigin;
import com.red.circle.external.inner.endpoint.message.ImGroupClient;
import com.red.circle.external.inner.model.cmd.message.BroadcastGroupMsgBodyCmd;
import com.red.circle.external.inner.model.cmd.message.CustomGroupMsgBodyCmd;
import com.red.circle.external.inner.model.cmd.message.GroupMsgBodyCmd;
import com.red.circle.external.inner.model.enums.message.GroupMessageTypeEnum;
import com.red.circle.framework.core.asserts.ResponseAssert;
import com.red.circle.live.inner.endpoint.LiveMicClient;
import com.red.circle.mq.business.model.event.game.GameBurstCrystalEvent;
import com.red.circle.other.app.convertor.game.GameBurstCrystalConvertor;
import com.red.circle.other.app.convertor.live.RoomProfileAppConvertor;
import com.red.circle.other.app.convertor.sys.ActivityAppConvertor;
import com.red.circle.other.app.convertor.user.UserProfileAppConvertor;
import com.red.circle.other.app.dto.clientobject.game.********************************;
import com.red.circle.other.app.dto.clientobject.game.GameBurstCrystalLuckyBoxUserRewardCO;
import com.red.circle.other.app.dto.clientobject.game.GameBurstCrystalNoticeCO;
import com.red.circle.other.app.dto.clientobject.game.GameBurstCrystalNoticeProgressCO;
import com.red.circle.other.app.dto.clientobject.game.GameBurstCrystalPreheatNoticeCO;
import com.red.circle.other.app.dto.clientobject.game.GameBurstCrystalScheduleCO;
import com.red.circle.other.app.dto.clientobject.game.GameBurstCrystalWinnerUserProfileCO;
import com.red.circle.other.domain.gateway.props.ActivitySourceGroupGateway;
import com.red.circle.other.domain.gateway.user.UserProfileGateway;
import com.red.circle.other.domain.gateway.user.ability.UserRegionGateway;
import com.red.circle.other.infra.common.activity.PropsActivitySendCommon;
import com.red.circle.other.infra.common.activity.send.SendRewardSingle;
import com.red.circle.other.infra.database.cache.entity.game.burstcrystal.GameBurstCrystalProgressCache;
import com.red.circle.other.infra.database.cache.service.other.GameBurstCrystalThatDayCacheService;
import com.red.circle.other.infra.database.mongo.entity.live.RoomProfileManager;
import com.red.circle.other.infra.database.mongo.service.gift.GiftGiveRunningWaterService;
import com.red.circle.other.infra.database.mongo.service.live.RoomProfileManagerService;
import com.red.circle.other.infra.database.rds.entity.activity.PropsActivityRewardConfig;
import com.red.circle.other.infra.database.rds.entity.activity.PropsActivityRuleConfig;
import com.red.circle.other.infra.database.rds.entity.game.GameBurstCrystalProgress;
import com.red.circle.other.infra.database.rds.entity.game.GameBurstCrystalWinnerUsers;
import com.red.circle.other.infra.database.rds.service.activity.PropsActivityRewardConfigService;
import com.red.circle.other.infra.database.rds.service.activity.PropsActivityRuleConfigService;
import com.red.circle.other.infra.database.rds.service.game.GameBurstCrystalProgressService;
import com.red.circle.other.infra.database.rds.service.game.GameBurstCrystalWinnerUsersService;
import com.red.circle.other.inner.enums.activity.PropsActivityTypeEnum;
import com.red.circle.other.inner.enums.game.GameBurstCrystalProgressStatus;
import com.red.circle.other.inner.model.dto.activity.props.ActivityResource;
import com.red.circle.other.inner.model.dto.activity.props.ActivityRewardProps;
import com.red.circle.other.inner.model.dto.live.RoomProfileDTO;
import com.red.circle.other.inner.model.dto.user.UserProfileDTO;
import com.red.circle.tool.core.collection.CollectionUtils;
import com.red.circle.tool.core.date.TimestampUtils;
import com.red.circle.tool.core.date.ZonedDateTimeAsiaRiyadhUtils;
import com.red.circle.tool.core.json.JacksonUtils;
import com.red.circle.tool.core.text.StringUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Collection;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Random;
import java.util.Set;
import java.util.stream.Collectors;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 爆水晶游戏.
 *
 * <AUTHOR> on 2021/7/1
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class GameBurstCrystalCommon {

    private final ImGroupClient imGroupClient;
    private final LiveMicClient liveMicClient;
    private final UserRegionGateway userRegionGateway;
    private final UserProfileGateway userProfileGateway;
    private final ActivityAppConvertor activityAppConvertor;
    private final UserProfileAppConvertor userProfileAppConvertor;
    private final PropsActivitySendCommon propsActivitySendCommon;
    private final RoomProfileAppConvertor roomProfileAppConvertor;
    private final GameBurstCrystalConvertor gameBurstCrystalConvertor;
    private final RoomProfileManagerService roomProfileManagerService;
    private final ActivitySourceGroupGateway activitySourceGroupGateway;
    private final GiftGiveRunningWaterService giftGiveRunningWaterService;
    private final PropsActivityRuleConfigService propsActivityRuleConfigService;
    private final GameBurstCrystalProgressService gameBurstCrystalProgressService;
    private final PropsActivityRewardConfigService propsActivityRewardConfigService;
    private final GameBurstCrystalWinnerUsersService gameBurstCrystalWinnerUsersService;
    private final GameBurstCrystalThatDayCacheService gameBurstCrystalThatDayCacheService;

    /**
     * 游戏阶段检测.
     */
    public void gameStageDetection(GameBurstCrystalEvent cmd) {
        GameBurstCrystalProgressCache processing = initGame(cmd);

        if (Objects.isNull(processing)) {
            return;
        }

        if (isGameEnd(processing)) {
            return;
        }

        checkGameProgress(cmd, processing);
    }

    private void checkGameProgress(GameBurstCrystalEvent cmd,
                                   GameBurstCrystalProgressCache processing) {
        PropsActivityRuleConfig ruleConfig = propsActivityRuleConfigService
                .getById(processing.getRuleId());

        GameBurstCrystalContent ruleContent = GameBurstCrystalContent
                .serializable(ruleConfig.getJsonData());

        incrUserContribute(processing.getId(), cmd);
        Long currentProgress = incrThatDayProgressRate(processing.getId(), cmd.getQuantity());

        RoomProfileDTO roomProfileDTO = getRoomProfile(cmd);
        if (Objects.isNull(roomProfileDTO)) {
            log.info(" [爆炸水晶] 参数错误,没有找到房间信息：{}", JacksonUtils.toJson(cmd));
            return;
        }
        String regionCode = userRegionGateway.getRegionCode(roomProfileDTO.getUserId());

        // 发送预热
        if (checkPreheatProgress(currentProgress, ruleContent.getMilestone())
                && gameBurstCrystalThatDayCacheService
                .isPreheat(processing.getId())) {
            sendNotice(cmd.getSysOrigin(),
                    GroupMsgBodyCmd.createMsg(GroupMessageTypeEnum.GAME_BURST_CRYSTAL_WARM_UP,
                            new GameBurstCrystalNoticeCO()
                                    .setRegionCode(regionCode)
                                    .setGameResult(new GameBurstCrystalPreheatNoticeCO()
                                            .setRoomId(cmd.getRoomId())
                                            .setLevel(ruleContent.getLevel())
                                            .setMilestone(ruleContent.getMilestone())
                                            .setCurrentContribution(BigDecimal.valueOf(currentProgress))
                                    ).setRoomProfile(roomProfileDTO)
                    )
            );
        }
        // 开奖
        if (GameBurstCrystalProgressStatus.PROCESSING.eq(processing.getStatus())
                && checkDraw(currentProgress, ruleContent.getMilestone())
                && gameBurstCrystalThatDayCacheService.isDraw(processing.getId())) {
            try {

                // 发送奖励
                if (Objects.equals(cmd.getSysOrigin().name(), SysOriginPlatformEnum.MARCIE.name()) || Objects.equals(cmd.getSysOrigin().name(), SysOriginPlatformEnum.TARAB.name())) {
                    sendRewardTwoFun(cmd, processing, ruleConfig, ruleContent.getLevel());
                } else {
                    sendReward(cmd, processing, ruleConfig);
                }
                // 进入下一轮
                goToTheNextRound(cmd, ruleConfig);
                // 发送开奖消息
                sendNotice(cmd.getSysOrigin(),
                        GroupMsgBodyCmd.createMsg(GroupMessageTypeEnum.GAME_BURST_CRYSTAL_CLOSE,
                                new GameBurstCrystalNoticeCO()
                                        .setRegionCode(regionCode)
                                        .setGameResult(toGameBurstCrystalNoticeProgressCO(
                                                gameBurstCrystalProgressService.getById(processing.getId()), ruleContent))
                                        .setRoomProfile(roomProfileDTO)
                        ));
                gameBurstCrystalThatDayCacheService.removeCurrentProcessing(processing.getId());
            } catch (Exception ex) {
                log.info("水晶开奖异常:{}", Throwables.getStackTraceAsString(ex));
                gameBurstCrystalThatDayCacheService.removeDraw(processing.getId());
                throw ex;
            }
        }
    }

    private void sendRewardTwoFun(GameBurstCrystalEvent cmd,
                                  GameBurstCrystalProgressCache progress,
                                  PropsActivityRuleConfig propsActivityRuleConfig, Integer level) {
        // 发送前3名奖励
//    sendRewardTop3(cmd, progress, propsActivityRuleConfig);
        // 发送前5名奖励
        sendReward(cmd, progress, propsActivityRuleConfig);
        // 发送突破奖励
        sendSprintReward(cmd, progress, level);
        // 发送宝箱奖励
        sendLuckyBoxReward(cmd, progress, level);
    }

    private void sendLuckyBoxReward(GameBurstCrystalEvent cmd, GameBurstCrystalProgressCache progress,
                                    Integer level) {
        Random random = new Random();
        RoomProfileManager roomProfileManager = roomProfileManagerService.getById(cmd.getRoomId());
        PropsActivityRuleConfig ruleConfig = propsActivityRuleConfigService
                .getGtLevelCrystalLuckyBoxRule(cmd.getSysOrigin(), level);

        List<PropsActivityRewardConfig> activityRewardProps = propsActivityRewardConfigService
                .listByGroupId(ruleConfig.getResourceGroupId());

        ActivityResource activityResource = activitySourceGroupGateway.getActivityResource(
                ruleConfig.getId());

        List<ActivityRewardProps> propsGroupActivityRewardProps = activityResource.getPropsGroupActivityRewardProps();

        if (CollectionUtils.isEmpty(activityRewardProps)) {
            log.info("爆水晶游戏突破奖励数据错误，没有找到奖品：{},{}",
                    JacksonUtils.toJson(progress),
                    JacksonUtils.toJson(activityRewardProps));
            return;
        }

        // 获取房间在线用户
        List<Long> roomOnlineUserIds = ResponseAssert.requiredSuccess(
                liveMicClient.getLiveRoomUserIds(cmd.getRoomId(), 200));

        if (CollectionUtils.isEmpty(roomOnlineUserIds)) {
            return;
        }
        // 记录领取宝箱用户
        List<GameBurstCrystalLuckyBoxUserRewardCO> userRewards = Lists.newArrayList();
        Map<String, Object> map = Maps.newHashMap();
        // 在线人数小于5
        if (roomOnlineUserIds.size() <= 5) {
            for (int i = 1; i <= roomOnlineUserIds.size(); i++) {
                if (i % 2 == 0) {

                    PropsActivityRewardConfig rewardConfig = activityRewardProps.get(random.nextInt(5));

                    propsActivitySendCommon.sendActivitySingle(SendRewardSingle.builder()
                            .trackId(progress.getId())
                            .sysOrigin(cmd.getSysOrigin())
                            .acceptUserId(roomOnlineUserIds.get(i - 1))
                            .rewardConfig(rewardConfig)
                            .origin(SendPropsOrigin.GAME_BURST_CRYSTAL_LUCKY_BOX)
                            .build());

                    ActivityRewardProps rewardProps = propsGroupActivityRewardProps.get(
                            rewardConfig.getSort() - 1);
                    GameBurstCrystalLuckyBoxUserRewardCO rewardCO = new GameBurstCrystalLuckyBoxUserRewardCO();
                    rewardCO.setUserId(roomOnlineUserIds.get(i - 1));
                    rewardCO.setCover(rewardProps.getCover());
                    rewardCO.setType(rewardProps.getType());
                    rewardCO.setQuantity(rewardProps.getQuantity());
                    userRewards.add(rewardCO);
                }
            }
            map.put("userRewards", userRewards);
            String groupId = roomProfileManager.getRoomAccount();
            if (StringUtils.isBlank(groupId)) {
                return;
            }

            // 发送开奖消息
            imGroupClient.sendCustomMessage(
                    groupId,
                    CustomGroupMsgBodyCmd.builder()
                            .type(GroupMessageTypeEnum.GAME_BURST_CRYSTAL_BOX)
                            .data(map)
                            .build());
            return;
        }
        // 在线人数小于10
        if (roomOnlineUserIds.size() <= 10) {
            for (int i = 1; i <= roomOnlineUserIds.size(); i++) {
                if (i % 3 == 0) {
                    PropsActivityRewardConfig rewardConfig = activityRewardProps.get(random.nextInt(5));

                    propsActivitySendCommon.sendActivitySingle(SendRewardSingle.builder()
                            .trackId(progress.getId())
                            .sysOrigin(cmd.getSysOrigin())
                            .acceptUserId(roomOnlineUserIds.get(i - 1))
                            .rewardConfig(rewardConfig)
                            .origin(SendPropsOrigin.GAME_BURST_CRYSTAL_LUCKY_BOX)
                            .build());

                    ActivityRewardProps rewardProps = propsGroupActivityRewardProps.get(
                            rewardConfig.getSort() - 1);
                    GameBurstCrystalLuckyBoxUserRewardCO rewardCO = new GameBurstCrystalLuckyBoxUserRewardCO();
                    rewardCO.setUserId(roomOnlineUserIds.get(i - 1));
                    rewardCO.setCover(rewardProps.getCover());
                    rewardCO.setType(rewardProps.getType());
                    rewardCO.setQuantity(rewardProps.getQuantity());
                    userRewards.add(rewardCO);
                }
            }
            map.put("userRewards", userRewards);
            String groupId = roomProfileManager.getRoomAccount();
            if (StringUtils.isBlank(groupId)) {
                return;
            }

            // 发送开奖消息
            imGroupClient.sendCustomMessage(
                    groupId,
                    CustomGroupMsgBodyCmd.builder()
                            .type(GroupMessageTypeEnum.GAME_BURST_CRYSTAL_BOX)
                            .data(map)
                            .build());
            return;
        }

        // 在线人数大于10
        List<********************************> luckyBoxRewardUsers = Lists.newArrayList();
        for (int i = 0; i < roomOnlineUserIds.size(); i++) {

            ******************************** boxRewardCO = new ********************************();
            boxRewardCO.setUserId(roomOnlineUserIds.get(i));
            boxRewardCO.setSort(random.nextInt(roomOnlineUserIds.size()));
            luckyBoxRewardUsers.add(boxRewardCO);
        }
        // 根据max属性的大小筛选出最大的5条数据
        luckyBoxRewardUsers = luckyBoxRewardUsers.stream()
                .sorted(Comparator.comparingInt(********************************::getSort).reversed())
                .limit(5)
                .collect(Collectors.toList());
        for (int i = 0; i < activityRewardProps.size(); i++) {

            ******************************** boxRewardCO = luckyBoxRewardUsers.get(i);

            propsActivitySendCommon.sendActivitySingle(SendRewardSingle.builder()
                    .trackId(progress.getId())
                    .sysOrigin(cmd.getSysOrigin())
                    .acceptUserId(boxRewardCO.getUserId())
                    .rewardConfig(activityRewardProps.get(i))
                    .origin(SendPropsOrigin.GAME_BURST_CRYSTAL_LUCKY_BOX)
                    .build());

            ActivityRewardProps rewardProps = propsGroupActivityRewardProps.get(
                    activityRewardProps.get(i).getSort() - 1);
            GameBurstCrystalLuckyBoxUserRewardCO rewardCO = new GameBurstCrystalLuckyBoxUserRewardCO();
            rewardCO.setUserId(boxRewardCO.getUserId());
            rewardCO.setCover(rewardProps.getCover());
            rewardCO.setType(rewardProps.getType());
            rewardCO.setQuantity(rewardProps.getQuantity());
            userRewards.add(rewardCO);

        }
        map.put("userRewards", userRewards);
        String groupId = roomProfileManager.getRoomAccount();
        if (StringUtils.isBlank(groupId)) {
            return;
        }

        // 发送开奖消息
        imGroupClient.sendCustomMessage(
                groupId,
                CustomGroupMsgBodyCmd.builder()
                        .type(GroupMessageTypeEnum.GAME_BURST_CRYSTAL_BOX)
                        .data(map)
                        .build());

    }

    private void sendSprintReward(GameBurstCrystalEvent cmd,
                                  GameBurstCrystalProgressCache progress, Integer level) {
        Map<String, Object> map = Maps.newHashMap();
        RoomProfileManager roomProfileManager = roomProfileManagerService.getById(cmd.getRoomId());
        PropsActivityRuleConfig ruleConfig = propsActivityRuleConfigService
                .getGtLevelCrystalSprintRule(cmd.getSysOrigin(), level);

        List<PropsActivityRewardConfig> activityRewardProps = propsActivityRewardConfigService
                .listByGroupId(ruleConfig.getResourceGroupId());

        ActivityResource activityResource = activitySourceGroupGateway.getActivityResource(
                ruleConfig.getId());

        List<ActivityRewardProps> propsGroupActivityRewardProps = activityResource.getPropsGroupActivityRewardProps();

        if (CollectionUtils.isEmpty(activityRewardProps)) {
            log.info("爆水晶游戏突破奖励数据错误，没有找到奖品：{},{}",
                    JacksonUtils.toJson(progress),
                    JacksonUtils.toJson(activityRewardProps));
            return;
        }

        PropsActivityRewardConfig rewardConfig = activityRewardProps.get(0);

        propsActivitySendCommon.sendActivitySingle(SendRewardSingle.builder()
                .trackId(progress.getId())
                .sysOrigin(cmd.getSysOrigin())
                .acceptUserId(cmd.getUserId())
                .rewardConfig(rewardConfig)
                .origin(SendPropsOrigin.GAME_BURST_CRYSTAL_SPRINT)
                .build());

        GameBurstCrystalWinnerUsers winnerUsers = new GameBurstCrystalWinnerUsers();
        winnerUsers.setGameId(progress.getId());
        winnerUsers.setUserId(cmd.getUserId());
        winnerUsers.setPrizeId(rewardConfig.getId());
        winnerUsers.setRank(4);
        gameBurstCrystalWinnerUsersService.save(winnerUsers);

        ActivityRewardProps rewardProps = propsGroupActivityRewardProps.get(0);

        List<GameBurstCrystalLuckyBoxUserRewardCO> userRewards = Lists.newArrayList();
        GameBurstCrystalLuckyBoxUserRewardCO userRewardCO = new GameBurstCrystalLuckyBoxUserRewardCO();
        userRewardCO.setCover(rewardProps.getCover());
        userRewardCO.setUserId(cmd.getUserId());
        userRewardCO.setType(rewardProps.getType());
        userRewardCO.setQuantity(rewardProps.getQuantity());
        userRewards.add(userRewardCO);
        map.put("userRewards", userRewards);
        String groupId = roomProfileManager.getRoomAccount();
        if (StringUtils.isBlank(groupId)) {
            return;
        }

        imGroupClient.sendCustomMessage(
                groupId,
                CustomGroupMsgBodyCmd.builder()
                        .type(GroupMessageTypeEnum.GAME_BURST_CRYSTAL_SPRINT)
                        .data(map)
                        .build());

    }

    private void sendRewardTop3(GameBurstCrystalEvent cmd,
                                GameBurstCrystalProgressCache progress,
                                PropsActivityRuleConfig propsActivityRuleConfig) {

        List<Long> rewardUsers = getRewardUsersTop3(progress);
        if (CollectionUtils.isEmpty(rewardUsers)) {
            log.info("[2]爆水晶游戏数据错误，没有贡献者：{},{}",
                    JacksonUtils.toJson(progress),
                    JacksonUtils.toJson(propsActivityRuleConfig));
        }

        List<PropsActivityRewardConfig> activityRewardProps = propsActivityRewardConfigService
                .listByGroupId(propsActivityRuleConfig.getResourceGroupId());

        if (CollectionUtils.isEmpty(activityRewardProps)) {
            log.info("[3]爆水晶游戏数据错误，没有找到开奖奖品：{},{}",
                    JacksonUtils.toJson(progress),
                    JacksonUtils.toJson(propsActivityRuleConfig));
            return;
        }

        int activityRewardPropsSize = activityRewardProps.size();
        int rewardUsersSize = rewardUsers.size();

        for (int index = 0; index < rewardUsersSize; index++) {
            Long userId = rewardUsers.get(index);

            if (index >= activityRewardPropsSize) {
                log.info("[4]爆水晶游配置奖品不足:{},{}", progress.getRoomId(), userId);
                break;
            }

            PropsActivityRewardConfig rewardConfig = activityRewardProps.get(index);

            propsActivitySendCommon.sendActivitySingle(SendRewardSingle.builder()
                    .trackId(progress.getId())
                    .sysOrigin(cmd.getSysOrigin())
                    .acceptUserId(userId)
                    .rewardConfig(rewardConfig)
                    .origin(SendPropsOrigin.GAME_BURST_CRYSTAL)
                    .build());

            GameBurstCrystalWinnerUsers winnerUsers = new GameBurstCrystalWinnerUsers();
            winnerUsers.setGameId(progress.getId());
            winnerUsers.setUserId(userId);
            winnerUsers.setPrizeId(rewardConfig.getId());
            winnerUsers.setRank(index + 1);
            gameBurstCrystalWinnerUsersService.save(winnerUsers);
        }

        int remaining = activityRewardPropsSize - rewardUsersSize;
        if (remaining <= 0) {
            log.info("[5]爆水晶游配置奖品奖品没有剩余:{}", remaining);
            return;
        }

        List<Long> remainingUserIds = getRemainingUserIds(progress, rewardUsers, remaining);
        if (CollectionUtils.isEmpty(remainingUserIds)) {
            log.info("[6]爆水晶游，没有剩余用户领取后续奖项");
            return;
        }
        for (int index = 0; index < remaining; index++) {

            if (index >= remainingUserIds.size()) {
                break;
            }

            Long userId = remainingUserIds.get(index);
            PropsActivityRewardConfig rewardConfig = activityRewardProps.get(index);

            propsActivitySendCommon.sendActivitySingle(SendRewardSingle.builder()
                    .trackId(progress.getId())
                    .sysOrigin(cmd.getSysOrigin())
                    .acceptUserId(userId)
                    .rewardConfig(rewardConfig)
                    .origin(SendPropsOrigin.GAME_BURST_CRYSTAL)
                    .build());

            GameBurstCrystalWinnerUsers winnerUsers = new GameBurstCrystalWinnerUsers();
            winnerUsers.setGameId(progress.getId());
            winnerUsers.setUserId(userId);
            winnerUsers.setPrizeId(rewardConfig.getId());
            winnerUsers.setRank(rewardUsersSize + index + 1);
            gameBurstCrystalWinnerUsersService.save(winnerUsers);
        }
    }

    private void incrUserContribute(Long processingId, GameBurstCrystalEvent cmd) {
        if (Objects.nonNull(cmd.getUserId())) {
            gameBurstCrystalThatDayCacheService
                    .incrementContribute(processingId, cmd.getUserId(), cmd.getQuantity().doubleValue());
        }
    }

    private GameBurstCrystalNoticeProgressCO toGameBurstCrystalNoticeProgressCO(
            GameBurstCrystalProgress progress, GameBurstCrystalContent rule) {
        return new GameBurstCrystalNoticeProgressCO()
                .setCurrentContribution(progress.getProgressRate())
                .setStatus(GameBurstCrystalProgressStatus.valueOf(progress.getStatus()))
                .setLevel(rule.getLevel())
                .setMilestone(rule.getMilestone())
                .setSourceUrl(rule.getSourceUrl());
    }

    private GameBurstCrystalProgressCache initGame(GameBurstCrystalEvent cmd) {
        GameBurstCrystalProgressCache processing = getCurrentProcessing(cmd.getRoomId());

        if (Objects.nonNull(processing)) {
            return processing;
        }

        if (!gameBurstCrystalThatDayCacheService.isInit(cmd.getRoomId())) {
            return null;
        }

        PropsActivityRuleConfig ruleConfig = propsActivityRuleConfigService
                .getRuleCrystalFirst(cmd.getSysOrigin());

        if (Objects.isNull(ruleConfig)) {
            log.info("[0]爆水晶游戏数据错误,没有规则数据配置:{}", JacksonUtils.toJson(cmd));
            return null;
        }

        return createGameBurstCrystalProgress(cmd, ruleConfig, Boolean.TRUE);
    }

    private void sendNotice(SysOriginPlatformEnum sysOrigin, GroupMsgBodyCmd messageBody) {
        imGroupClient.sendMessageBroadcast(BroadcastGroupMsgBodyCmd.builder()
                .allPlatform()
                .toPlatform(sysOrigin)
                .type(GroupMessageTypeEnum.GAME_BURST_CRYSTAL)
                .data(messageBody)
                .build());
    }

    private boolean isGameEnd(GameBurstCrystalProgressCache processing) {
        return GameBurstCrystalProgressStatus.COMPLETED.eq(processing.getStatus());
    }

    private RoomProfileDTO getRoomProfile(GameBurstCrystalEvent cmd) {
        return roomProfileAppConvertor.toRoomProfileDTO(
                roomProfileManagerService.getProfileById(cmd.getRoomId()));
    }


    /**
     * 获取游戏进度.
     */
    public List<GameBurstCrystalScheduleCO> listProgressSchedule(SysOriginPlatformEnum sysOrigin,
                                                                 Long roomId) {
        List<ActivityResource> activityResources = activitySourceGroupGateway
                .listActivityResource(sysOrigin, PropsActivityTypeEnum.CRYSTAL);

        if (CollectionUtils.isEmpty(activityResources)) {
            return Lists.newArrayList();
        }

        Map<Integer, GameBurstCrystalProgress> gameBurstCrystalProgressMap = gameBurstCrystalProgressService
                .mapThatDayBoxLevel(roomId);

        Map<Long, List<GameBurstCrystalWinnerUsers>> winnerUsersMap = gameBurstCrystalWinnerUsersService
                .mapGroupByGameId(getCompletedGameIds(gameBurstCrystalProgressMap));

        Map<Long, UserProfileDTO> baseInfoMap = userProfileAppConvertor.toMapUserProfileDTO(
                userProfileGateway.mapByUserIds(getGameWinnerUserIds(winnerUsersMap))
        );

        return activityResources.stream()
                .map(activityResource -> {
                    GameBurstCrystalContent gameBurstCrystalContent = GameBurstCrystalContent
                            .serializable(activityResource.getRule().getJsonData());

                    GameBurstCrystalProgress progress = gameBurstCrystalProgressMap
                            .get(gameBurstCrystalContent.getLevel());

                    if (Objects.isNull(progress)) {
                        return new GameBurstCrystalScheduleCO()
                                .setRoomId(roomId)
                                .setStatus(GameBurstCrystalProgressStatus.NOT_STARTED)
                                .setCurrentContribution(BigDecimal.ZERO)
                                .setRule(
                                        gameBurstCrystalConvertor.toGameBurstCrystalRuleCO(gameBurstCrystalContent))
                                .setActivityRewardProps(activityAppConvertor
                                        .toActivityRewardPropsDTO(
                                                activityResource.getPropsGroupActivityRewardProps()))
                                .setWinnerUserProfiles(Lists.newArrayList());
                    }

                    return new GameBurstCrystalScheduleCO()
                            .setRoomId(roomId)
                            .setRule(gameBurstCrystalConvertor.toGameBurstCrystalRuleCO(gameBurstCrystalContent))
                            .setActivityRewardProps(activityAppConvertor
                                    .toActivityRewardPropsDTO(
                                            activityResource.getPropsGroupActivityRewardProps()))
                            .setStatus(GameBurstCrystalProgressStatus.valueOf(progress.getStatus()))
                            .setCurrentContribution(progress.getProgressRate())
                            .setActivityRewardProps(activityAppConvertor
                                    .toActivityRewardPropsDTO(activityResource.getPropsGroupActivityRewardProps()))
                            .setWinnerUserProfiles(getWinnerUserProfiles(winnerUsersMap, baseInfoMap, progress));
                }).collect(Collectors.toList());
    }

    private void goToTheNextRound(GameBurstCrystalEvent cmd, PropsActivityRuleConfig ruleConfig) {

        GameBurstCrystalContent crystalContent = GameBurstCrystalContent
                .serializable(ruleConfig.getJsonData());
        PropsActivityRuleConfig nextRuleConfig = propsActivityRuleConfigService
                .getRuleGtLevelCrystal(cmd.getSysOrigin(), crystalContent.getLevel());

        if (Objects.isNull(nextRuleConfig)) {
            log.info("[8]爆水晶游戏,没有下一局游戏结束.");
            gameBurstCrystalThatDayCacheService.removeCurrentProcessing(cmd.getRoomId());
            gameBurstCrystalProgressService.completedGame(cmd.getRoomId());
            return;
        }

        createGameBurstCrystalProgress(cmd, nextRuleConfig, Boolean.FALSE);

        gameBurstCrystalThatDayCacheService.removeCurrentProcessing(cmd.getRoomId());
    }


    private GameBurstCrystalProgressCache createGameBurstCrystalProgress(GameBurstCrystalEvent cmd,
                                                                         PropsActivityRuleConfig ruleConfig, boolean isInit) {
        GameBurstCrystalContent ruleContent = GameBurstCrystalContent
                .serializable(ruleConfig.getJsonData());

        GameBurstCrystalProgress newProgress = gameBurstCrystalProgressService
                .saveThatDayCloseProcessing(new GameBurstCrystalProgress()
                        .setRuleId(ruleConfig.getId())
                        .setNumberDate(ZonedDateTimeAsiaRiyadhUtils.nowDateToInt())
                        .setRoomId(cmd.getRoomId())
                        .setBoxLevel(ruleContent.getLevel())
                        .setProgressRate(BigDecimal.ZERO)
                        .setStatus(GameBurstCrystalProgressStatus.PROCESSING.name())
                        .withCreateTime(isInit ? cmd.getCreateTime() : TimestampUtils.now()));

        return gameBurstCrystalConvertor.toGameBurstCrystalProgressCache(newProgress);
    }


    private GameBurstCrystalProgressCache getCurrentProcessing(Long roomId) {
        return gameBurstCrystalThatDayCacheService
                .getCurrentProcessing(roomId,
                        rId -> gameBurstCrystalConvertor.toGameBurstCrystalProgressCache(
                                gameBurstCrystalProgressService.getThatDayLatestProgress(roomId)));
    }

    private void sendReward(GameBurstCrystalEvent cmd,
                            GameBurstCrystalProgressCache progress,
                            PropsActivityRuleConfig propsActivityRuleConfig) {

        List<Long> rewardUsers = getRewardUsers(progress);
        //
        log.info("领取用户列表 {}", rewardUsers);
        if (CollectionUtils.isEmpty(rewardUsers)) {
            log.info("[2]爆水晶游戏数据错误，没有贡献者：{},{}",
                    JacksonUtils.toJson(progress),
                    JacksonUtils.toJson(propsActivityRuleConfig));
        }

        List<PropsActivityRewardConfig> activityRewardProps = propsActivityRewardConfigService
                .listByGroupId(propsActivityRuleConfig.getResourceGroupId());

        log.info("发送奖励列表 {}", activityRewardProps);
        if (CollectionUtils.isEmpty(activityRewardProps)) {
            log.info("[3]爆水晶游戏数据错误，没有找到开奖奖品：{},{}",
                    JacksonUtils.toJson(progress),
                    JacksonUtils.toJson(propsActivityRuleConfig));
            return;
        }

        int activityRewardPropsSize = activityRewardProps.size();
        int rewardUsersSize = rewardUsers.size();

        for (int index = 0; index < rewardUsersSize; index++) {
            Long userId = rewardUsers.get(index);

            if (index >= activityRewardPropsSize) {
                log.info("[4]爆水晶游配置奖品不足:{},{}", progress.getRoomId(), userId);
                break;
            }

            PropsActivityRewardConfig rewardConfig = activityRewardProps.get(index);
            propsActivitySendCommon.sendActivitySingle(SendRewardSingle.builder()
                    .trackId(progress.getId())
                    .sysOrigin(cmd.getSysOrigin())
                    .acceptUserId(userId)
                    .rewardConfig(rewardConfig)
                    .origin(SendPropsOrigin.GAME_BURST_CRYSTAL)
                    .build());

            gameBurstCrystalWinnerUsersService.save(new GameBurstCrystalWinnerUsers()
                    .setGameId(progress.getId())
                    .setUserId(userId)
                    .setPrizeId(rewardConfig.getId())
                    .setRank(index + 1)
            );
        }

        int remaining = activityRewardPropsSize - rewardUsersSize;
        if (remaining <= 0) {
            log.info("[5]爆水晶游配置奖品奖品没有剩余:{}", remaining);
            return;
        }

        List<Long> remainingUserIds = getRemainingUserIds(progress, rewardUsers, remaining);
        if (CollectionUtils.isEmpty(remainingUserIds)) {
            log.info("[6]爆水晶游，没有剩余用户领取后续奖项");
            return;
        }
        for (int index = 0; index < remaining; index++) {

            if (index >= remainingUserIds.size()) {
                break;
            }

            Long userId = remainingUserIds.get(index);
            if (index + remaining <= activityRewardPropsSize) {
                PropsActivityRewardConfig rewardConfig = activityRewardProps.get(index + remaining);
                propsActivitySendCommon.sendActivitySingle(SendRewardSingle.builder()
                        .trackId(progress.getId())
                        .sysOrigin(cmd.getSysOrigin())
                        .acceptUserId(userId)
                        .rewardConfig(rewardConfig)
                        .origin(SendPropsOrigin.GAME_BURST_CRYSTAL)
                        .build());
                gameBurstCrystalWinnerUsersService.save(new GameBurstCrystalWinnerUsers()
                        .setGameId(progress.getId())
                        .setUserId(userId)
                        .setPrizeId(rewardConfig.getId())
                        .setRank(rewardUsersSize + index + 1)
                );
            }

        }
    }

    private List<Long> getRewardUsers(GameBurstCrystalProgressCache progress) {
        return gameBurstCrystalThatDayCacheService.popContributeTop5(progress.getId());
    }

    private List<Long> getRewardUsersTop3(GameBurstCrystalProgressCache progress) {
        return gameBurstCrystalThatDayCacheService.popContributeTop3(progress.getId());
    }

    private List<Long> getRemainingUserIds(GameBurstCrystalProgressCache progress,
                                           List<Long> rewardUsers,
                                           int remaining) {
        List<Long> userIds = giftGiveRunningWaterService
                .listLatestRoomContributionUserIs(progress.getRoomId(), progress.getCreateTime(), 10);
        if (CollectionUtils.isEmpty(userIds)) {
            return Lists.newArrayList();
        }
        userIds.removeAll(rewardUsers);
        return userIds.stream().limit(remaining).distinct().collect(Collectors.toList());
    }

    private boolean checkDraw(Long currentProgress, BigDecimal milestone) {
        return currentProgress >= milestone.longValue();
    }

    private boolean checkPreheatProgress(Long currentProgress, BigDecimal milestone) {
        return currentProgress >= getPreheatValue(milestone);
    }

    private Long getPreheatValue(BigDecimal milestone) {
        return milestone.multiply(BigDecimal.valueOf(0.9))
                .setScale(0, RoundingMode.DOWN)
                .longValue();
    }

    private Long incrThatDayProgressRate(Long progressId, BigDecimal quantity) {
        gameBurstCrystalProgressService.incrThatDayProgressRate(progressId, quantity);
        return gameBurstCrystalThatDayCacheService
                .incrProgressRate(progressId, quantity.longValue());
    }


    private List<GameBurstCrystalWinnerUserProfileCO> getWinnerUserProfiles(
            Map<Long, List<GameBurstCrystalWinnerUsers>> winnerUsersMap,
            Map<Long, UserProfileDTO> baseInfoMap,
            GameBurstCrystalProgress progress) {
        return Optional.ofNullable(winnerUsersMap.get(progress.getId()))
                .map(winnerUsers -> winnerUsers.stream().map(winnerUser ->
                                        new GameBurstCrystalWinnerUserProfileCO()
                                                .setRank(winnerUser.getRank())
                                                .setPrizeId(winnerUser.getPrizeId())
                                                .setUserProfile(baseInfoMap.get(winnerUser.getUserId()))
                                )
                                .sorted(Comparator.comparing(GameBurstCrystalWinnerUserProfileCO::getRank))
                                .toList()
                ).orElseGet(Lists::newArrayList);
    }

    private Set<Long> getGameWinnerUserIds(
            Map<Long, List<GameBurstCrystalWinnerUsers>> winnerUsersMap) {
        return winnerUsersMap.values().stream().flatMap(Collection::stream)
                .map(GameBurstCrystalWinnerUsers::getUserId).collect(Collectors.toSet());
    }

    private Set<Long> getCompletedGameIds(
            Map<Integer, GameBurstCrystalProgress> gameBurstCrystalProgressMap) {
        return gameBurstCrystalProgressMap.values().stream().filter(gameBurstCrystalProgress -> Objects
                        .equals(GameBurstCrystalProgressStatus.COMPLETED.name(),
                                gameBurstCrystalProgress.getStatus()))
                .map(GameBurstCrystalProgress::getId)
                .collect(Collectors.toSet());
    }

    /**
     * 获取游戏进度.
     */
    public List<GameBurstCrystalScheduleCO> listProgressScheduleTwoFun(
            SysOriginPlatformEnum sysOrigin,
            Long roomId) {
        List<GameBurstCrystalScheduleCO> result = Lists.newArrayList();
        // 奖励列表
        List<ActivityResource> activityResources = activitySourceGroupGateway
                .listActivityResource(sysOrigin, PropsActivityTypeEnum.CRYSTAL);

        if (CollectionUtils.isEmpty(activityResources)) {
            return Lists.newArrayList();
        }
        // 宝箱奖励
        List<ActivityResource> luckyBoxRewards = activitySourceGroupGateway
                .listActivityResource(sysOrigin, PropsActivityTypeEnum.CRYSTAL_LUCKY_BOX);

        if (CollectionUtils.isEmpty(luckyBoxRewards)) {
            return Lists.newArrayList();
        }

        // 突破奖励
        List<ActivityResource> topRewardProps = activitySourceGroupGateway
                .listActivityResource(sysOrigin, PropsActivityTypeEnum.CRYSTAL_TOP);

        if (CollectionUtils.isEmpty(topRewardProps)) {
            return Lists.newArrayList();
        }

        Map<Integer, GameBurstCrystalProgress> gameBurstCrystalProgressMap = gameBurstCrystalProgressService
                .mapThatDayBoxLevel(roomId);

        Map<Long, List<GameBurstCrystalWinnerUsers>> winnerUsersMap = gameBurstCrystalWinnerUsersService
                .mapGroupByGameId(getCompletedGameIds(gameBurstCrystalProgressMap));

        Map<Long, UserProfileDTO> baseInfoMap = userProfileAppConvertor.toMapUserProfileDTO(
                userProfileGateway
                        .mapByUserIds(getGameWinnerUserIds(winnerUsersMap))
        );

        for (int i = 0; i < activityResources.size(); i++) {

            GameBurstCrystalContent gameBurstCrystalContent = GameBurstCrystalContent
                    .serializable(activityResources.get(i).getRule().getJsonData());

            GameBurstCrystalProgress progress = gameBurstCrystalProgressMap
                    .get(gameBurstCrystalContent.getLevel());
            if (Objects.isNull(progress)) {
                result.add(new GameBurstCrystalScheduleCO()
                        .setRoomId(roomId)
                        .setStatus(GameBurstCrystalProgressStatus.NOT_STARTED)
                        .setCurrentContribution(BigDecimal.ZERO)
                        .setRule(gameBurstCrystalConvertor.toGameBurstCrystalRuleCO(gameBurstCrystalContent))
                        .setActivityRewardProps(activityAppConvertor
                                .toActivityRewardPropsDTO(
                                        activityResources.get(i).getPropsGroupActivityRewardProps()))
                        .setTopRewardProps(activityAppConvertor
                                .toActivityRewardPropsDTO(
                                        topRewardProps.get(i).getPropsGroupActivityRewardProps()))
                        .setLuckyBoxRewards(activityAppConvertor
                                .toActivityRewardPropsDTO(
                                        luckyBoxRewards.get(i).getPropsGroupActivityRewardProps()))
                        .setWinnerUserProfiles(Lists.newArrayList())
                );
            } else {
                result.add(new GameBurstCrystalScheduleCO()
                        .setRoomId(roomId)
                        .setRule(gameBurstCrystalConvertor.toGameBurstCrystalRuleCO(gameBurstCrystalContent))
                        .setActivityRewardProps(activityAppConvertor
                                .toActivityRewardPropsDTO(
                                        activityResources.get(i).getPropsGroupActivityRewardProps()))
                        .setStatus(GameBurstCrystalProgressStatus.valueOf(progress.getStatus()))
                        .setCurrentContribution(progress.getProgressRate())
                        .setActivityRewardProps(activityAppConvertor
                                .toActivityRewardPropsDTO(
                                        activityResources.get(i).getPropsGroupActivityRewardProps()))
                        .setTopRewardProps(activityAppConvertor
                                .toActivityRewardPropsDTO(
                                        topRewardProps.get(i).getPropsGroupActivityRewardProps()))
                        .setLuckyBoxRewards(activityAppConvertor
                                .toActivityRewardPropsDTO(
                                        luckyBoxRewards.get(i).getPropsGroupActivityRewardProps()))
                        .setWinnerUserProfiles(getWinnerUserProfiles(winnerUsersMap, baseInfoMap, progress))
                );
            }
        }
        return result;
    }

}
