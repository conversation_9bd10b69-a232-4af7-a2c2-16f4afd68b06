package com.red.circle.other.app.listener.strategy;

import com.red.circle.common.business.core.enums.DataApprovalTypeEnum;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR> on 2020/7/23
 */
@Service
public class FactoryForApprovalStrategy {

  private final Map<String, ApprovalStrategy> strategyCurrentMap = new ConcurrentHashMap<>();

  public FactoryForApprovalStrategy(Map<String, ApprovalStrategy> strategyMap) {
    strategyCurrentMap.putAll(strategyMap);
    System.out.println("Injected strategy bean names: {}"+ strategyMap.keySet());
  }

  public ApprovalStrategy getStrategy(DataApprovalTypeEnum strategyName) {
    ApprovalStrategy strategy = strategyCurrentMap.get(strategyName.name().concat("_APPROVAL"));
    if (strategy == null) {
      throw new IllegalArgumentException("no strategy defined:" + strategyName);
    }
    return strategy;
  }

}
