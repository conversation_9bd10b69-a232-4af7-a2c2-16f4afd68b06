package com.red.circle.other.app.scheduler;

import com.red.circle.common.business.core.enums.SysOriginPlatformEnum;
import com.red.circle.component.redis.annotation.TaskCacheLock;
import com.red.circle.other.app.manager.activity.award.WeeklyRewardsSentManager;
import com.red.circle.other.infra.database.cache.service.user.CacheEnumConfigManagerService;
import com.red.circle.other.infra.database.rds.enums.OtherConfigEnum;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

/**
 * 每月固定活动榜单奖励发送.
 *
 * <AUTHOR> on 2021/10/27
 */
@Slf4j
@Component
@AllArgsConstructor
public class MonthFixedActivityTask {

  private final WeeklyRewardsSentManager weeklyRewardsSentManager;
  private final CacheEnumConfigManagerService cacheEnumConfigManagerService;

  /**
   * 每月一号 0点5分执行.
   */
//  @Scheduled(cron = "5 0 0 1 * ?", zone = "Asia/Riyadh")
  @TaskCacheLock(key = "MONTH_ACTIVITY_TASK", expireSecond = 86400)
//  @XxlJob("month_activity_task")
  public void startMonthActivityTask() {
    long startTime = System.currentTimeMillis();
    log.info("exec month_activity_task start");
    if (cacheEnumConfigManagerService.getValBool(OtherConfigEnum.SUSPEND_CONSUMPTION,
        SysOriginPlatformEnum.ASWAT.name())) {
      return;
    }

    // 代理活动 - 月
    weeklyRewardsSentManager.processSendRewardAgentActivity(SysOriginPlatformEnum.ASWAT, "MONTH",
        null);
    log.info("exec month_activity_task end with {}",System.currentTimeMillis()-startTime);
  }

}
