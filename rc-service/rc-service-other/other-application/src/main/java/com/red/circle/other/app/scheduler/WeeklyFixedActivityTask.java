package com.red.circle.other.app.scheduler;

import com.red.circle.common.business.core.enums.SysOriginPlatformEnum;
import com.red.circle.common.business.dto.cmd.UserIdCmd;
import com.red.circle.component.redis.annotation.TaskCacheLock;
import com.red.circle.framework.dto.ResultResponse;
import com.red.circle.other.app.convertor.user.UserProfileAppConvertor;
import com.red.circle.other.app.dto.clientobject.activity.ActivityUserCO;
import com.red.circle.other.app.dto.clientobject.user.user.UserRegionCO;
import com.red.circle.other.app.manager.activity.RefreshWeekActivityManager;
import com.red.circle.other.app.service.user.user.UserRegionService;
import com.red.circle.other.domain.gateway.user.UserProfileGateway;
import com.red.circle.other.infra.database.cache.service.other.FreightBalanceCacheService;
import com.red.circle.other.infra.database.rds.service.user.device.LatestMobileDeviceService;
import com.red.circle.other.inner.model.dto.user.UserProfileDTO;
import com.red.circle.tool.core.json.JacksonUtils;
import com.red.circle.wallet.inner.endpoint.freight.FreightBalanceClient;
import com.red.circle.wallet.inner.model.dto.UserFreightBalanceDTO;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 每周固定活动榜单刷新及奖励发送.
 *
 * <AUTHOR> 2024/06/25
 */
@Slf4j
@Component
@AllArgsConstructor
public class WeeklyFixedActivityTask {

  private final FreightBalanceClient freightBalanceClient;
  private final UserProfileGateway userProfileGateway;
  private final UserProfileAppConvertor userProfileAppConvertor;
  private final UserRegionService userRegionService;
  private final LatestMobileDeviceService latestMobileDeviceService;
  private final FreightBalanceCacheService freightBalanceCacheService;
  private final RefreshWeekActivityManager refreshWeekActivityManager;

  /**
   * 每周一 0点5分执行.
   */
//  @Scheduled(cron = "5 0 0 ? * MON", zone = "Asia/Riyadh")
  @TaskCacheLock(key = "WEEKLY_FIXED_ACTIVITY_TASK", expireSecond = 86400)
  public void startWeekActivityTask() {

    //调用其他每周活动，因为有些活动需要过滤周星礼物
    refreshWeekActivityManager.refreshWeeklyActivitiesWeekly();

    //发送全部活动奖励 - 周
    refreshWeekActivityManager.weeklyAllRewardsSent();

  }

  /**
   * 每15分钟执行一次,更新这周排行榜
   */
  @Scheduled(cron = "0 0/15 * * * ?", zone = "Asia/Riyadh")
  @TaskCacheLock(key = "WEEKLY_FIXED_ACTIVITY_15_REFRESH_TASK", expireSecond = 800)
  public void refreshActivity15Task() {

    //缓存余额大于1k的货运代理账户
    cacheFreightUser();

    //同时调用15分钟加载榜单业务
    refreshWeekActivityManager.refreshWeeklyActivitiesFifteenMin();

  }

  /**
   * 缓存余额大于1k的货运代理账户
   */
  private void cacheFreightUser() {

    SysOriginPlatformEnum.getVoiceSystems().forEach(origin -> {

      ResultResponse<List<UserFreightBalanceDTO>> balances = freightBalanceClient.listFreightByBalanceGt1K(origin);
      if (CollectionUtils.isEmpty(balances.getBody())) {
        return;
      }

      Map<Long, UserProfileDTO> userMap = userProfileAppConvertor.toMapUserProfileDTO(
              userProfileGateway.mapByUserIds(getUserIds(balances.getBody()))
      );

      freightBalanceCacheService.cacheFreightUserList(origin,
          JacksonUtils.toJson(
              balances.getBody().stream().map(balance -> {
                UserProfileDTO user = userMap.get(balance.getUserId());
                if (Objects.isNull(user)) {
                  return null;
                }
                UserIdCmd userIdCmd = new UserIdCmd();
                userIdCmd.setUserId(user.getId());
                UserRegionCO region = userRegionService.getUserRegionSetting(userIdCmd);
                return new ActivityUserCO()
                    .setUserId(balance.getUserId())
                    .setCountryName(user.getCountryName())
                    .setRegionCode(Objects.isNull(region) ? "0" : region.getRegionCode())
                    .setAccount(user.getActualAccount())
                    .setUserAvatar(user.getUserAvatar())
                    .setUserNickname(user.getUserNickname())
                    .setLanguage(latestMobileDeviceService.getLatestLanguage(user.getId()));
              }).filter(Objects::nonNull).collect(Collectors.toList()))
      );
    });
  }

  private Set<Long> getUserIds(List<UserFreightBalanceDTO> balances) {
    return balances.stream().map(UserFreightBalanceDTO::getUserId).collect(Collectors.toSet());
  }

}
