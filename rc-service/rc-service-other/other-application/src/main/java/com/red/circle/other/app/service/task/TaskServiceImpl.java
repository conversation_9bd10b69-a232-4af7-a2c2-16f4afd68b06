package com.red.circle.other.app.service.task;

import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.red.circle.common.business.core.enums.SysOriginPlatformEnum;
import com.red.circle.common.business.dto.cmd.AppExtCommand;
import com.red.circle.common.business.enums.SendPropsOrigin;
import com.red.circle.component.redis.service.RedisService;
import com.red.circle.other.app.dto.task.TaskDTO;
import com.red.circle.other.domain.gateway.user.UserProfileGateway;
import com.red.circle.other.domain.model.user.UserConsumptionLevel;
import com.red.circle.other.infra.database.rds.dao.task.TaskConfigDAO;
import com.red.circle.other.infra.database.rds.dao.task.UserTaskProgressDAO;
import com.red.circle.other.infra.database.rds.entity.family.FamilyMemberInfo;
import com.red.circle.other.infra.database.rds.entity.task.TaskConfig;
import com.red.circle.other.infra.database.rds.entity.task.UserTaskProgress;
import com.red.circle.other.infra.database.rds.entity.user.user.CpRelationship;
import com.red.circle.other.infra.database.rds.service.family.FamilyMemberInfoService;
import com.red.circle.other.infra.database.rds.service.user.user.CpRelationshipService;
import com.red.circle.other.inner.endpoint.activity.PropsActivityClient;
import com.red.circle.other.inner.model.cmd.activity.SendActivityRewardCmd;
import com.red.circle.other.inner.model.dto.activity.props.ActivityPropsGroup;
import com.red.circle.tool.core.date.TimestampUtils;
import com.red.circle.tool.core.sequence.IdWorkerUtils;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class TaskServiceImpl implements TaskService {


    private final TaskConfigDAO taskConfigDao;


    private final UserTaskProgressDAO userTaskProgressDao;
    private final PropsActivityClient propsActivityCnfClient;
    private final FamilyMemberInfoService familyMemberInfoService;
    private final CpRelationshipService cpRelationshipService;
    private final UserProfileGateway userProfileGateway;

    private final RedisService redisService;


//    private final TeamMemberTargetService teamMemberTargetService;

    /**
     * 获取任务列表
     *
     * @param cmd 复合对象
     * @return 任务列表的复合对象
     */
    @Override
    public List<TaskDTO> getTaskList(AppExtCommand cmd) {
        List<TaskDTO> resultList = new ArrayList<>();
        //获取当前天 yyyy-mm-dd 格式
        String date = DateUtil.format(new Date(), "yyyy-MM-dd");
        Long userId = cmd.getReqUserId();
        // 查询任务配置信息
        //获取每日
        List<TaskConfig> taskConfigs = taskConfigDao.selectList(
                new LambdaQueryWrapper<TaskConfig>()
                        .eq(TaskConfig::getTaskType, 1)
                        .orderByAsc(TaskConfig::getSortOrder));
        Map<Long, TaskConfig> taskMapByIds = taskConfigs.stream().collect(Collectors.toMap(TaskConfig::getTaskId, taskConfig -> taskConfig));
        // 每天有用户请求的时候创建对应的任务数据
        List<UserTaskProgress> userTaskList = createDailyTasksIfNotExists(userId, taskConfigs, date);
        if (CollectionUtils.isEmpty(userTaskList)) {
            //新增list为空就是 已经创建过了 直接拼接
            LambdaQueryWrapper<UserTaskProgress> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(UserTaskProgress::getUserId, userId);
            queryWrapper.eq(UserTaskProgress::getDayString, date);
            userTaskList = userTaskProgressDao.selectList(queryWrapper);
        }
        Set<Long> rewardIds = taskConfigs.stream().map(TaskConfig::getRewardId).collect(Collectors.toSet());
        //获取对应详情
        Map<Long, ActivityPropsGroup> activityMap = propsActivityCnfClient.mapActivityPropsGroup(cmd.getReqSysOrigin().getOrigin(), rewardIds).getBody();
        userTaskList.forEach(item -> {

            ActivityPropsGroup activity = activityMap.get(item.getRewardId());
//        String taskDesc = messageSource.getMessage("task.task_"+item.getTaskId()+".desc", null, locale);
            String taskDesc = ResourceBundle.getBundle("i18n.messages", new Locale(cmd.getReqLanguage())).getString("task.task_" + item.getTaskId() + ".desc");

            TaskDTO build = TaskDTO.builder()
                    .taskType(item.getTaskType())
                    .taskId(item.getTaskId())
//                    .taskDesc(taskMapByIds.get(item.getTaskId()).getTaskDesc())
                    .taskDesc(taskDesc)
                    .jumpPage(taskMapByIds.get(item.getTaskId()).getJumpPage())
                    .taskStatus(item.getTaskStatus())
                    .isRewardCollected(item.getIsRewardCollected())
                    .taskIcon(taskMapByIds.get(item.getTaskId()).getTaskIcon())
                    .taskStatus(item.getTaskStatus())
                    .conditionType(activity.getActivityRewardProps().get(0).getType())
                    .cover(activity.getActivityRewardProps().get(0).getCover())
                    .quantity(activity.getActivityRewardProps().get(0).getQuantity())
                    .build();
            //拼接数据
//            item.setActivityPropsGroup(activityMap.getData().get(item.getRewardId()));
//            item.setDayString(date);
            resultList.add(build);
        });
        //拼接任务
        return resultList;
    }


    /**
     * 获取任务状态
     *
     * @param userId 用户 ID
     * @return 是否有完成但未领取的任务
     */
    @Override
    public Boolean getTaskStatus(Long userId) {
        String date = DateUtil.format(new Date(), "yyyy-MM-dd");
        LambdaQueryWrapper<UserTaskProgress> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(UserTaskProgress::getUserId, userId);
        queryWrapper.eq(UserTaskProgress::getTaskStatus, 1);
        queryWrapper.eq(UserTaskProgress::getIsRewardCollected, 0);
//        queryWrapper.eq(UserTaskProgress::getDayString, date);
        //所有的
        List<UserTaskProgress> userTaskProgresses = userTaskProgressDao.selectList(queryWrapper);
        boolean todayTaskFlag = userTaskProgresses.stream()
                .anyMatch(task -> task.getTaskType() == 1 && Objects.equals(task.getDayString(), date));
        boolean taskFlag = userTaskProgresses.stream()
                .anyMatch(task -> task.getTaskType() == 2);
        return todayTaskFlag || taskFlag;
    }

    @Override
    public void updateTaskStatus(Integer status, Long userId, Long TaskId, String date) {
        UserTaskProgress userTaskProgress = new UserTaskProgress();
//    userTaskProgress.setTaskId(TaskId);
        userTaskProgress.setTaskStatus(status);

        LambdaUpdateWrapper<UserTaskProgress> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(UserTaskProgress::getUserId, userId);
        updateWrapper.eq(UserTaskProgress::getTaskId, TaskId);
        updateWrapper.eq(UserTaskProgress::getDayString, date);
        int update = userTaskProgressDao.update(userTaskProgress, updateWrapper);

//        List<UserTaskProgress> userTaskProgress = userTaskProgressDao.selectList(query);

    }

    @Override
    public void updateTaskStatus(Integer status, Long userId, Long TaskId) {
        UserTaskProgress userTaskProgress = new UserTaskProgress();
//    userTaskProgress.setTaskId(TaskId);
        userTaskProgress.setTaskStatus(status);

        LambdaUpdateWrapper<UserTaskProgress> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(UserTaskProgress::getUserId, userId);
        updateWrapper.eq(UserTaskProgress::getTaskId, TaskId);
        int update = userTaskProgressDao.update(userTaskProgress, updateWrapper);

//        List<UserTaskProgress> userTaskProgress = userTaskProgressDao.selectList(query);

    }

    @Override
    public Boolean checkTaskStatus(Long userId, Long TaskId, String date) {
        LambdaQueryWrapper<UserTaskProgress> query = new LambdaQueryWrapper<>();
        query.eq(UserTaskProgress::getUserId, userId);
        query.eq(UserTaskProgress::getTaskId, TaskId);
        query.eq(UserTaskProgress::getTaskStatus, 1);
        query.eq(UserTaskProgress::getDayString, date);
        List<UserTaskProgress> userTaskProgress = userTaskProgressDao.selectList(query);
//get 0   true  未完成 false 已完成
        return CollectionUtils.isEmpty(userTaskProgress);
    }

    @Override
    public Boolean checkTaskStatus(Long userId, Long TaskId) {
        LambdaQueryWrapper<UserTaskProgress> query = new LambdaQueryWrapper<>();
        query.eq(UserTaskProgress::getUserId, userId);
        query.eq(UserTaskProgress::getTaskId, TaskId);
        query.eq(UserTaskProgress::getTaskStatus, 1);
        List<UserTaskProgress> userTaskProgress = userTaskProgressDao.selectList(query);
//get 0   true  未完成 false 已完成
        return CollectionUtils.isEmpty(userTaskProgress);
    }


    /**
     * 领取任务奖励
     *
     * @param taskId 任务 ID
     * @return 操作结果
     */
    @Override
    public Boolean claimReward(AppExtCommand cmd, Long taskId) {
        String date = DateUtil.format(new Date(), "yyyy-MM-dd");
        String lockKey = "task:reward:" + date + ":" + cmd.getReqUserId() + ":" + taskId;

        List<TaskConfig> taskConfigs = taskConfigDao.selectList(new LambdaQueryWrapper<TaskConfig>().eq(TaskConfig::getTaskId, taskId));
        if (taskConfigs.isEmpty()) {
            return false;
        }
        try {
            if (redisService.lock(lockKey, 20)) {
                LambdaQueryWrapper<UserTaskProgress> eq = new LambdaQueryWrapper<UserTaskProgress>()
                        .eq(UserTaskProgress::getUserId, cmd.getReqUserId())
                        .eq(UserTaskProgress::getTaskId, taskId)
                        .eq(UserTaskProgress::getIsRewardCollected, 0);
                if (taskConfigs.get(0).getTaskType() == 1) {
                    //每日任务才需要时间
                    eq.eq(UserTaskProgress::getDayString, date);
                }
                List<UserTaskProgress> userTaskProgresses = userTaskProgressDao.selectList(eq);

                UserTaskProgress userTaskProgress = userTaskProgresses.get(0);
                if (!Objects.isNull(userTaskProgress)) {


                    // 发送道具
                    propsActivityCnfClient.sendActivityReward(new SendActivityRewardCmd()
                            .setTrackId(IdWorkerUtils.getId())
                            .setSysOrigin(SysOriginPlatformEnum.valueOf(cmd.getReqSysOrigin().getOrigin()))
                            .setAcceptUserId(cmd.getReqUserId())
                            .setSourceGroupId(taskConfigs.get(0).getRewardId())
                            .setOrigin(SendPropsOrigin.DAILY_TASK_REWARD)
                    );

                    userTaskProgress.setIsRewardCollected(1);
                    userTaskProgress.setCompletedTime(TimestampUtils.now());
                    userTaskProgressDao.updateById(userTaskProgress);
                }

            }
        } finally {
            redisService.unlock(lockKey);
        }

        return true;
    }

    @Override
    public List<TaskDTO> getTaskListV2(AppExtCommand cmd) {
        List<TaskDTO> resultList = new ArrayList<>();
        //获取当前天 yyyy-mm-dd 格式
        String date = DateUtil.format(new Date(), "yyyy-MM-dd");
        Long userId = cmd.getReqUserId();
        // 查询任务配置信息
        //获取所有任务
        List<TaskConfig> taskConfigs = taskConfigDao.selectList(
                new LambdaQueryWrapper<TaskConfig>()
                        .orderByAsc(TaskConfig::getSortOrder));
        Map<Long, TaskConfig> taskMapByIds = taskConfigs.stream().collect(Collectors.toMap(TaskConfig::getTaskId, taskConfig -> taskConfig));

        //判断是否初始化成长任务
        List<UserTaskProgress> userTaskList = createTasksIfNotExists(userId, taskConfigs);
        List<UserTaskProgress> allTask = new ArrayList<>(userTaskList);

        // 每天有用户请求的时候创建对应的任务数据
        List<UserTaskProgress> dailyUserTaskList = createDailyTasksIfNotExists(userId, taskConfigs, date);
        if (CollectionUtils.isEmpty(dailyUserTaskList)) {
            //新增list为空就是 已经创建过了 直接拼接
            LambdaQueryWrapper<UserTaskProgress> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(UserTaskProgress::getUserId, userId);
            queryWrapper.eq(UserTaskProgress::getDayString, date);
            dailyUserTaskList = userTaskProgressDao.selectList(queryWrapper);
        }
        allTask.addAll(dailyUserTaskList);
        Set<Long> rewardIds = taskConfigs.stream().map(TaskConfig::getRewardId).collect(Collectors.toSet());
        //获取对应详情
        Map<Long, ActivityPropsGroup> activityMap = propsActivityCnfClient.mapActivityPropsGroup(cmd.getReqSysOrigin().getOrigin(), rewardIds).getBody();
        allTask.forEach(item -> {

            ActivityPropsGroup activity = activityMap.get(item.getRewardId());
//        String taskDesc = messageSource.getMessage("task.task_"+item.getTaskId()+".desc", null, locale);
            String taskDesc = ResourceBundle.getBundle("i18n.messages", new Locale(cmd.getReqLanguage())).getString("task.task_" + item.getTaskId() + ".desc");

            TaskDTO build = TaskDTO.builder()
                    .taskType(item.getTaskType())
                    .taskId(item.getTaskId())
//                    .taskDesc(taskMapByIds.get(item.getTaskId()).getTaskDesc())
                    .taskDesc(taskDesc)
                    .jumpPage(taskMapByIds.get(item.getTaskId()).getJumpPage())
                    .taskStatus(item.getTaskStatus())
                    .isRewardCollected(item.getIsRewardCollected())
                    .taskIcon(taskMapByIds.get(item.getTaskId()).getTaskIcon())
                    .taskStatus(item.getTaskStatus())
                    .conditionType(activity.getActivityRewardProps().get(0).getType())
                    .cover(activity.getActivityRewardProps().get(0).getCover())
                    .quantity(activity.getActivityRewardProps().get(0).getQuantity())
                    .build();
            //拼接数据
//            item.setActivityPropsGroup(activityMap.getData().get(item.getRewardId()));
//            item.setDayString(date);
            resultList.add(build);
        });
        //拼接任务
        return resultList;
    }

    /**
     * 创建每日任务
     *
     * @param userId 用户 ID
     */
    private List<UserTaskProgress> createDailyTasksIfNotExists(Long userId, List<TaskConfig> list, String date) {
        // 这期判断是否需要创建每日任务
        LambdaQueryWrapper<UserTaskProgress> queryWrapper = new LambdaQueryWrapper<>();
        //查询是否有这一天的任务
        queryWrapper.eq(UserTaskProgress::getDayString, date);
        queryWrapper.eq(UserTaskProgress::getUserId, userId);
        List<UserTaskProgress> userTaskList = userTaskProgressDao.selectList(queryWrapper);

        //为空的时候新增任务
        List<UserTaskProgress> userTaskProgressList = new ArrayList<>();
        if (userTaskList.isEmpty()) {
            //创建一个list 批量新增
            for (TaskConfig taskConfig : list.stream().filter(task -> task.getTaskType() == 1).toList()) {

                UserTaskProgress userTaskProgress = new UserTaskProgress();
                userTaskProgress.setId(IdWorkerUtils.getId());
                userTaskProgress.setUserId(userId);
                userTaskProgress.setTaskId(taskConfig.getTaskId());
                userTaskProgress.setIsRewardCollected(0);
                userTaskProgress.setRewardId(taskConfig.getRewardId());
                userTaskProgress.setTaskType(taskConfig.getTaskType());
                userTaskProgress.setTaskStatus(0);
                userTaskProgress.setDayString(date);
                userTaskProgress.setCreateTimestamp(TimestampUtils.now());

                userTaskProgressList.add(userTaskProgress);
            }
            userTaskProgressDao.insertBatchSomeColumn(userTaskProgressList);
        }
        return userTaskProgressList;
      /*  for (TaskConfig taskConfig : dailyTasks) {
            UserTaskProgress userTaskProgress = userTaskProgressDao.selectByUserIdAndTaskId(userId, taskConfig.getTaskId());
            if (userTaskProgress == null) {
                userTaskProgress = new UserTaskProgress();
                userTaskProgress.setUserId(userId);
                userTaskProgress.setTaskId(taskConfig.getTaskId());
                userTaskProgress.setRewardId(taskConfig.getRewardId());
                userTaskProgress.setTaskType(taskConfig.getTaskType());
                userTaskProgress.setTaskStatus(0);
                userTaskProgress.setIsRewardCollected(0);
                userTaskProgressDao.insert(userTaskProgress);
            }
        }*/

    }

    private List<UserTaskProgress> createTasksIfNotExists(Long userId, List<TaskConfig> taskConfigs) {
        // 这期判断是否需要创建每日任务
        LambdaQueryWrapper<UserTaskProgress> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(UserTaskProgress::getUserId, userId);
        //成长任务
        queryWrapper.eq(UserTaskProgress::getTaskType, 2);
        List<UserTaskProgress> userTaskList = userTaskProgressDao.selectList(queryWrapper);

        //为空的时候新增任务
        List<UserTaskProgress> userTaskProgressList = new ArrayList<>();
        if (userTaskList.isEmpty()) {
            taskConfigs.stream().filter(task -> task.getTaskType() == 2).forEach(taskConfig -> {
                UserTaskProgress userTaskProgress = new UserTaskProgress();
                userTaskProgress.setId(IdWorkerUtils.getId());
                userTaskProgress.setUserId(userId);
                userTaskProgress.setTaskId(taskConfig.getTaskId());
                userTaskProgress.setIsRewardCollected(0);
                userTaskProgress.setRewardId(taskConfig.getRewardId());
                userTaskProgress.setTaskType(taskConfig.getTaskType());
                userTaskProgress.setTaskStatus(0);
                userTaskProgress.setCreateTimestamp(TimestampUtils.now());

                UserConsumptionLevel userLevel = userProfileGateway.getUserConsumptionLevel(
                        SysOriginPlatformEnum.valueOf("MARCIE"),
                        userId);
                //taskid =6  加入工会  7   组成cp  13 财富等级10  14 魅力等级10  初始化时就要判断是否完成
                switch (taskConfig.getTaskId().intValue()) {
                    case 6:
                        FamilyMemberInfo familyMemberInfo = familyMemberInfoService.getFamilyMemberByUserId(userId);
                        if (!Objects.isNull(familyMemberInfo)) {
                            userTaskProgress.setTaskStatus(1);
                        }
                        break;
                    case 7:
                        CpRelationship cp = cpRelationshipService.getByUserId(userId);
                        if (!Objects.isNull(cp)) {
                            userTaskProgress.setTaskStatus(1);
                        }
                        break;
                    case 13:
                        if (userLevel.getWealthLevel() >= 10) {
                            userTaskProgress.setTaskStatus(1);
                        }
                        //财富等级逻辑
                        break;
                    case 14:
                        //魅力等级逻辑
                        if (userLevel.getCharmLevel() >= 10) {
                            userTaskProgress.setTaskStatus(1);
                        }
                        break;
                    default:
                }
                userTaskProgressList.add(userTaskProgress);
            });

            userTaskProgressDao.insertBatchSomeColumn(userTaskProgressList);
            return userTaskProgressList;
        }
        return userTaskList;
    }

}


