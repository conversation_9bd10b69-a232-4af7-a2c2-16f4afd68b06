-- 工会钱包相关表创建脚本

-- 1. 主播周政策表
CREATE TABLE IF NOT EXISTS `anchor_weekly_policy` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `sys_origin` varchar(50) NOT NULL COMMENT '系统来源',
  `policy_name` varchar(100) NOT NULL COMMENT '政策名称',
  `min_gift_gold_value` bigint NOT NULL DEFAULT '0' COMMENT '最小礼物金币价值(分)',
  `max_gift_gold_value` bigint DEFAULT NULL COMMENT '最大礼物金币价值(分)',
  `member_diamond_ratio` decimal(5,4) NOT NULL COMMENT '成员钻石比例',
  `family_diamond_ratio` decimal(5,4) NOT NULL COMMENT '工会钻石比例',
  `priority` int NOT NULL DEFAULT '0' COMMENT '优先级(数字越小优先级越高)',
  `enabled` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否启用(0-禁用,1-启用)',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_sys_origin_enabled` (`sys_origin`, `enabled`),
  KEY `idx_priority` (`priority`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='主播周政策表';

-- 2. 工会钻石余额表
CREATE TABLE IF NOT EXISTS `family_diamond_balance` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `family_id` bigint NOT NULL COMMENT '工会ID',
  `sys_origin` varchar(50) NOT NULL COMMENT '系统来源',
  `balance` decimal(20,2) NOT NULL DEFAULT '0.00' COMMENT '钻石余额',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_family_sys` (`family_id`, `sys_origin`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='工会钻石余额表';

-- 3. 工会金币余额表
CREATE TABLE IF NOT EXISTS `family_gold_balance` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `family_id` bigint NOT NULL COMMENT '工会ID',
  `sys_origin` varchar(50) NOT NULL COMMENT '系统来源',
  `balance` bigint NOT NULL DEFAULT '0' COMMENT '金币余额(分)',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_family_sys` (`family_id`, `sys_origin`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='工会金币余额表';

-- 4. 工会钻石流水表
CREATE TABLE IF NOT EXISTS `family_diamond_running_water` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `family_id` bigint NOT NULL COMMENT '工会ID',
  `member_user_id` bigint DEFAULT NULL COMMENT '成员用户ID',
  `origin` varchar(50) NOT NULL COMMENT '来源类型',
  `origin_desc` varchar(200) DEFAULT NULL COMMENT '来源描述',
  `type` tinyint NOT NULL COMMENT '类型(0-收入,1-支出)',
  `gift_gold_value` bigint DEFAULT NULL COMMENT '礼物金币价值(分)',
  `member_diamond_amount` decimal(20,2) DEFAULT NULL COMMENT '成员钻石数量',
  `family_diamond_amount` decimal(20,2) NOT NULL COMMENT '工会钻石变动数量',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_family_id` (`family_id`),
  KEY `idx_member_user_id` (`member_user_id`),
  KEY `idx_create_time` (`create_time`),
  KEY `idx_origin_type` (`origin`, `type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='工会钻石流水表';

-- 5. 工会金币流水表
CREATE TABLE IF NOT EXISTS `family_gold_running_water` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `family_id` bigint NOT NULL COMMENT '工会ID',
  `member_user_id` bigint DEFAULT NULL COMMENT '成员用户ID',
  `origin` varchar(50) NOT NULL COMMENT '来源类型',
  `origin_desc` varchar(200) DEFAULT NULL COMMENT '来源描述',
  `type` tinyint NOT NULL COMMENT '类型(0-收入,1-支出)',
  `diamond_amount` decimal(20,2) DEFAULT NULL COMMENT '钻石数量',
  `gold_amount` bigint NOT NULL COMMENT '金币变动数量(分)',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_family_id` (`family_id`),
  KEY `idx_member_user_id` (`member_user_id`),
  KEY `idx_create_time` (`create_time`),
  KEY `idx_origin_type` (`origin`, `type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='工会金币流水表';

-- 插入默认的主播周政策数据
INSERT IGNORE INTO `anchor_weekly_policy` (`sys_origin`, `policy_name`, `min_gift_gold_value`, `max_gift_gold_value`, `member_diamond_ratio`, `family_diamond_ratio`, `priority`, `enabled`) VALUES
('MARCIE', '小额礼物政策', 0, 999, 0.5000, 0.1800, 1, 1),
('MARCIE', '中额礼物政策', 1000, 4999, 0.5000, 0.1800, 2, 1),
('MARCIE', '大额礼物政策', 5000, NULL, 0.5000, 0.1800, 3, 1);
