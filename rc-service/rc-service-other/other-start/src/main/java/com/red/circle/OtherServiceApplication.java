package com.red.circle;

import com.red.circle.component.redis.RedisAutoConfiguration;
import com.red.circle.other.app.scheduler.HotGameTask;
import lombok.extern.slf4j.Slf4j;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.scheduling.annotation.EnableScheduling;

/**
 * Spring Boot Starter.
 *
 * <AUTHOR> on 2023/5/7
 */
@Slf4j
@MapperScan("com.red.circle.other.infra.database.rds.dao.*")
@EnableScheduling
@EnableDiscoveryClient
@EnableFeignClients
@SpringBootApplication(
    exclude = RedisAutoConfiguration.class,
    scanBasePackages = {
        "com.red.circle.other",
        "com.red.circle.wallet.app.service.family",
        "com.red.circle.wallet.infra.database.rds.service.family",
        "com.red.circle.wallet.infra.database.rds.dao.family"
    }
)
public class OtherServiceApplication {

  public static void main(String[] args) {
    log.info("Begin to start Spring Boot Application");
    long startTime = System.currentTimeMillis();
   SpringApplication.run(OtherServiceApplication.class, args);
    long endTime = System.currentTimeMillis();

//    run.getBean(HotGameTask.class).calculateHotGameRatio();
//    run.getBean(HotGameTask.class).calculateHotGameVIP();
//    run.getBean(HotGameTask.class).calculateLossVIP();
    log.info("End starting Spring Boot Application, Time used: " + (endTime - startTime));
  }

}
