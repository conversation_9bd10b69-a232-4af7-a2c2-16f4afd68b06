package com.red.circle;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.CommandLineRunner;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class EnvPrinter implements CommandLineRunner {

    @Value("${rocketmq.accessKey}")
    private String nacosServerAddr;

    @Value("${rocketmq.secretKey}")
    private String namespace;

    @Value("${rocketmq.endpoints}")
    private String group;

    @Override
    public void run(String... args) {
        // 打印系统变量

        // 打印解析后的Nacos配置
        log.info("accessKey: {}", nacosServerAddr);
        log.info("secretKey: {}", namespace);
        log.info("endpoints: {}", group);

        // 打印所有环境变量（用于调试）
        System.getenv().forEach((k, v) -> {
            if (k.contains("NACOS") || k.contains("SERVER")) {
                log.info("环境变量 {}: {}", k, v);
            }
        });
    }
}
