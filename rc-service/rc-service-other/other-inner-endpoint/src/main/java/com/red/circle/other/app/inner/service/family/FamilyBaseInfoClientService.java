package com.red.circle.other.app.inner.service.family;


import com.red.circle.framework.dto.PageResult;
import com.red.circle.other.inner.model.cmd.famliy.FamilyBaseInfoQryCmd;
import com.red.circle.other.inner.model.dto.famliy.FamilyBaseInfoDTO;

/**
 * <p>
 * 工会基础信息表 服务类.
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-20
 */
public interface FamilyBaseInfoClientService {

  PageResult<FamilyBaseInfoDTO> pageData(FamilyBaseInfoQryCmd queryWhere);

  void delFamily(Long familyId);
}
