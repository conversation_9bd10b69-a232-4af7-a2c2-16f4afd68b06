package com.red.circle.other.app.inner.service.family.impl;

import com.red.circle.framework.core.asserts.ResponseAssert;
import com.red.circle.framework.core.response.CommonErrorCode;
import com.red.circle.framework.dto.PageResult;
import com.red.circle.other.app.inner.convertor.FamilyInnerConvertor;
import com.red.circle.other.app.inner.service.family.FamilyCreateRulesClientService;
import com.red.circle.other.infra.database.rds.entity.family.FamilyCreateRules;
import com.red.circle.other.infra.database.rds.service.family.FamilyCreateRulesService;
import com.red.circle.other.inner.model.cmd.famliy.FamilyCreateRuleCmd;
import com.red.circle.other.inner.model.cmd.famliy.FamilyCreateRuleQryCmd;
import com.red.circle.other.inner.model.dto.famliy.FamilyCreateRuleDTO;
import java.math.BigDecimal;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 工会创建要求(满足任意一个，那么用户就可以创建工会) 服务实现类.
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-21
 */
@Service
@RequiredArgsConstructor
public class FamilyCreateRulesClientServiceImpl implements
    FamilyCreateRulesClientService {

  private final FamilyInnerConvertor familyInnerConvertor;
  private final FamilyCreateRulesService familyCreateRulesService;


  @Override
  public void saveFamilyCreateRules(FamilyCreateRuleCmd param) {
    familyCreateRulesService.saveFamilyCreateRules(familyInnerConvertor.toFamilyCreateRules(param));
  }

  private void checkParam(FamilyCreateRules param) {
    ResponseAssert.isFalse(CommonErrorCode.DATA_ERROR,
        BigDecimal.ZERO.compareTo(param.getPayCandy()) >= 0 &&
            param.getUserWealthLevel() <= 0);
  }

  @Override
  public PageResult<FamilyCreateRuleDTO> pageData(FamilyCreateRuleQryCmd queryWhere) {
    return familyCreateRulesService.pageData(queryWhere)
        .convert(familyInnerConvertor::toFamilyCreateRuleDTO);
  }
}
