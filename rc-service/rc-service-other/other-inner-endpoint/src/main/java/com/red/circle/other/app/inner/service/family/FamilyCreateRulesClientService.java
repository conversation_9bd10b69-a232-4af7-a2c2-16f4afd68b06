package com.red.circle.other.app.inner.service.family;


import com.red.circle.framework.dto.PageResult;
import com.red.circle.other.inner.model.cmd.famliy.FamilyCreateRuleCmd;
import com.red.circle.other.inner.model.cmd.famliy.FamilyCreateRuleQryCmd;
import com.red.circle.other.inner.model.dto.famliy.FamilyCreateRuleDTO;

/**
 * <p>
 * 工会创建要求(满足任意一个，那么用户就可以创建工会) 服务类.
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-21
 */
public interface FamilyCreateRulesClientService {

  /**
   * 添加、更新.
   */
  void saveFamilyCreateRules(FamilyCreateRuleCmd param);

  /**
   * 分页.
   */
  PageResult<FamilyCreateRuleDTO> pageData(FamilyCreateRuleQryCmd queryWhere);
}
