package com.red.circle.other.app.inner.service.family;

import com.red.circle.other.inner.model.dto.famliy.FamilyDetailsDTO;
import com.red.circle.other.inner.model.dto.famliy.FamilyMemberInfoDTO;

/**
 * 工会客户端，服务.
 *
 * <AUTHOR> on 2023/6/5
 */
public interface FamilyClientService {

  /**
   * 获取工会成员.
   */
  FamilyMemberInfoDTO geMemberByUserId(Long userId);

  /**
   * 工会详情.
   */
  FamilyDetailsDTO getDetails(String sysOrigin, Long familyId);

}
