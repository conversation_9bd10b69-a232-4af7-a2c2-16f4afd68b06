package com.red.circle.other.app.inner.service.approval;

import com.red.circle.framework.dto.PageResult;
import com.red.circle.other.inner.model.cmd.approval.ApprovalFamilyCmd;
import com.red.circle.other.inner.model.cmd.approval.ApprovalProfileDescQryCmd;
import com.red.circle.other.inner.model.dto.approval.ApprovalFamilyDTO;


/**
 * 工会资料审批
 * <AUTHOR> on 2024/1/20
 */
public interface ApprovalFamilySettingDataClientService {

  PageResult<ApprovalFamilyDTO> pageFamilyApproval(ApprovalProfileDescQryCmd query);

  void notPass(ApprovalFamilyCmd cmd);
}
