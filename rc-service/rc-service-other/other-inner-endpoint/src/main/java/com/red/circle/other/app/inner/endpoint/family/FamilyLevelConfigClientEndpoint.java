package com.red.circle.other.app.inner.endpoint.family;

import com.red.circle.framework.dto.PageResult;
import com.red.circle.framework.dto.ResultResponse;
import com.red.circle.other.app.inner.service.family.FamilyLevelConfigClientService;
import com.red.circle.other.inner.endpoint.family.api.FamilyLevelConfigClientApi;
import com.red.circle.other.inner.model.cmd.famliy.FamilyLevelConfigCmd;
import com.red.circle.other.inner.model.cmd.famliy.FamilyLevelConfigQryCmd;
import com.red.circle.other.inner.model.dto.famliy.FamilyLevelConfigDTO;
import lombok.RequiredArgsConstructor;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 工会创建规则表 服务.
 *
 * <AUTHOR> on 2023/6/5
 */
@Validated
@RestController
@RequestMapping(value = FamilyLevelConfigClientApi.API_PREFIX, produces = MediaType.APPLICATION_JSON_VALUE)
@RequiredArgsConstructor
public class FamilyLevelConfigClientEndpoint implements FamilyLevelConfigClientApi {

  private final FamilyLevelConfigClientService familyLevelConfigClientService;


  /**
   * 分页.
   */
  @Override
  public ResultResponse<PageResult<FamilyLevelConfigDTO>> pageData(
      FamilyLevelConfigQryCmd queryWhere) {
    return ResultResponse.success(familyLevelConfigClientService.pageData(queryWhere));
  }

  /**
   * 添加、更新.
   */
  @Override
  public ResultResponse<Void> saveFamilyLevelConfig(FamilyLevelConfigCmd param) {
    familyLevelConfigClientService.saveFamilyLevelConfig(param);
    return ResultResponse.success();
  }

}
