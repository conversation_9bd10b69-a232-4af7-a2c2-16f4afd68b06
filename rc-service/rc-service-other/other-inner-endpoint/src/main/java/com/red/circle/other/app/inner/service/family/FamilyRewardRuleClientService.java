package com.red.circle.other.app.inner.service.family;


import com.red.circle.framework.dto.PageResult;
import com.red.circle.other.inner.model.cmd.famliy.FamilyRewardRuleCmd;
import com.red.circle.other.inner.model.cmd.famliy.FamilyRewardRuleQryCmd;
import com.red.circle.other.inner.model.dto.famliy.FamilyRewardRuleDTO;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * <p>
 * 工会奖励规则表 服务类.
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-20
 */
public interface FamilyRewardRuleClientService {

  PageResult<FamilyRewardRuleDTO> pageData(
      FamilyRewardRuleQryCmd queryWhere);

  void save(@RequestBody @Validated FamilyRewardRuleCmd param);
}
