package com.red.circle.other.app.inner.service.family.impl;

import com.red.circle.other.app.inner.convertor.FamilyInnerConvertor;
import com.red.circle.other.app.inner.service.family.FamilyClientService;
import com.red.circle.other.infra.common.family.FamilyCommon;
import com.red.circle.other.infra.database.rds.service.family.FamilyMemberInfoService;
import com.red.circle.other.inner.model.dto.famliy.FamilyDetailsDTO;
import com.red.circle.other.inner.model.dto.famliy.FamilyMemberInfoDTO;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * 工会客户端, 服务实现.
 *
 * <AUTHOR> on 2023/6/5
 */
@Service
@RequiredArgsConstructor
public class FamilyClientServiceImpl implements FamilyClientService {

  private final FamilyCommon familyCommon;
  private final FamilyInnerConvertor familyInnerConvertor;
  private final FamilyMemberInfoService familyMemberInfoService;

  @Override
  public FamilyMemberInfoDTO geMemberByUserId(Long userId) {
    return familyInnerConvertor.toFamilyMemberInfo(
        familyMemberInfoService.getFamilyMemberByUserId(userId));
  }

  @Override
  public FamilyDetailsDTO getDetails(String sysOrigin, Long familyId) {
    return familyCommon.getFamilyDetails(sysOrigin, familyId);
  }

}
