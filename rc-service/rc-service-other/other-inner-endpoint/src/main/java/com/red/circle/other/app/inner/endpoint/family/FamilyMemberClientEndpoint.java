package com.red.circle.other.app.inner.endpoint.family;

import com.red.circle.framework.dto.PageResult;
import com.red.circle.framework.dto.ResultResponse;
import com.red.circle.other.app.inner.service.family.FamilyMemberInfoClientService;
import com.red.circle.other.inner.endpoint.family.api.FamilyMemberClientApi;
import com.red.circle.other.inner.model.cmd.famliy.FamilyMemberQryCmd;
import com.red.circle.other.inner.model.dto.famliy.FamilyMemberDTO;
import lombok.RequiredArgsConstructor;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 工会成员表 服务.
 *
 * <AUTHOR> on 2023/6/5
 */
@Validated
@RestController
@RequestMapping(value = FamilyMemberClientApi.API_PREFIX, produces = MediaType.APPLICATION_JSON_VALUE)
@RequiredArgsConstructor
public class FamilyMemberClientEndpoint implements FamilyMemberClientApi {

  private final FamilyMemberInfoClientService familyLevelConfigClientService;


  /**
   * 分页.
   */
  @Override
  public ResultResponse<PageResult<FamilyMemberDTO>> pageData(
      FamilyMemberQryCmd queryWhere) {
    return ResultResponse.success(familyLevelConfigClientService.pageData(queryWhere));
  }

  /**
   * 移除工会成员.
   */
  @Override
  public ResultResponse<Void> delMember(Long familyId, Long memberId) {
    familyLevelConfigClientService.delMember(familyId, memberId);
    return ResultResponse.success();
  }

}
