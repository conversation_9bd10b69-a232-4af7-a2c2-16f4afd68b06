package com.red.circle.other.app.inner.service.family.impl;

import com.red.circle.framework.dto.PageResult;
import com.red.circle.other.app.inner.convertor.FamilyInnerConvertor;
import com.red.circle.other.app.inner.service.family.FamilyRewardRuleClientService;
import com.red.circle.other.infra.database.rds.service.family.FamilyRewardRuleService;
import com.red.circle.other.inner.model.cmd.famliy.FamilyRewardRuleCmd;
import com.red.circle.other.inner.model.cmd.famliy.FamilyRewardRuleQryCmd;
import com.red.circle.other.inner.model.dto.famliy.FamilyRewardRuleDTO;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * <p>
 * 工会奖励规则表 服务实现类.
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-21
 */
@Service
@RequiredArgsConstructor
public class FamilyRewardRuleClientServiceImpl implements
    FamilyRewardRuleClientService {

  private final FamilyInnerConvertor familyInnerConvertor;
  private final FamilyRewardRuleService familyRewardRuleService;


  /**
   * 分页.
   */
  @Override
  public PageResult<FamilyRewardRuleDTO> pageData(
      FamilyRewardRuleQryCmd queryWhere) {
    return familyRewardRuleService.pageData(queryWhere)
        .convert(familyInnerConvertor::toFamilyRewardRuleDTO);
  }

  /**
   * 添加、更新.
   */
  @Override
  public void save(@RequestBody @Validated FamilyRewardRuleCmd param) {
    familyRewardRuleService.saveFamilyRewardRule(familyInnerConvertor.toFamilyRewardRule(param));
  }

}
