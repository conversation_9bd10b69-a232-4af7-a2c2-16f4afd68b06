package com.red.circle.other.app.inner.service.family;


import com.red.circle.framework.dto.PageResult;
import com.red.circle.other.inner.model.cmd.famliy.FamilyMemberQryCmd;
import com.red.circle.other.inner.model.dto.famliy.FamilyMemberDTO;

/**
 * <p>
 * 工会创建要求(满足任意一个，那么用户就可以创建工会) 服务类.
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-21
 */
public interface FamilyMemberInfoClientService {

  PageResult<FamilyMemberDTO> pageData(FamilyMemberQryCmd queryWhere);

  void delMember(Long familyId, Long memberId);
}
