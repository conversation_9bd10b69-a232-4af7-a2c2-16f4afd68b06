package com.red.circle.other.app.inner.endpoint.family;

import com.red.circle.framework.dto.ResultResponse;
import com.red.circle.other.app.inner.service.family.FamilyClientService;
import com.red.circle.other.inner.endpoint.family.api.FamilyClientApi;
import com.red.circle.other.inner.model.dto.famliy.FamilyDetailsDTO;
import com.red.circle.other.inner.model.dto.famliy.FamilyMemberInfoDTO;
import lombok.RequiredArgsConstructor;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 工会服务.
 *
 * <AUTHOR> on 2023/6/5
 */
@Validated
@RestController
@RequestMapping(value = FamilyClientApi.API_PREFIX, produces = MediaType.APPLICATION_JSON_VALUE)
@RequiredArgsConstructor
public class FamilyClientEndpoint implements FamilyClientApi {

  private final FamilyClientService familyClientService;

  @Override
  public ResultResponse<FamilyMemberInfoDTO> geMemberByUserId(Long userId) {
    return ResultResponse.success(familyClientService.geMemberByUserId(userId));
  }

  @Override
  public ResultResponse<FamilyDetailsDTO> getDetails(String sysOrigin, Long familyId) {
    return ResultResponse.success(familyClientService.getDetails(sysOrigin, familyId));
  }

}
