package com.red.circle.other.app.inner.endpoint.family;

import com.red.circle.framework.dto.PageResult;
import com.red.circle.framework.dto.ResultResponse;
import com.red.circle.other.app.inner.service.family.FamilyRewardRuleClientService;
import com.red.circle.other.inner.endpoint.family.api.FamilyRewardRuleClientApi;
import com.red.circle.other.inner.model.cmd.famliy.FamilyRewardRuleCmd;
import com.red.circle.other.inner.model.cmd.famliy.FamilyRewardRuleQryCmd;
import com.red.circle.other.inner.model.dto.famliy.FamilyRewardRuleDTO;
import lombok.RequiredArgsConstructor;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 工会奖励规则表 服务.
 *
 * <AUTHOR> on 2023/6/5
 */
@Validated
@RestController
@RequestMapping(value = FamilyRewardRuleClientApi.API_PREFIX, produces = MediaType.APPLICATION_JSON_VALUE)
@RequiredArgsConstructor
public class FamilyRewardRuleClientEndpoint implements FamilyRewardRuleClientApi {

  private final FamilyRewardRuleClientService familyRewardRuleClientService;


  /**
   * 分页.
   */
  @Override
  public ResultResponse<PageResult<FamilyRewardRuleDTO>> pageData(
      FamilyRewardRuleQryCmd queryWhere) {
    return ResultResponse.success(familyRewardRuleClientService.pageData(queryWhere));
  }

  /**
   * 添加、更新.
   */
  @Override
  public ResultResponse<Void> save(@RequestBody @Validated FamilyRewardRuleCmd cmd) {
    familyRewardRuleClientService.save(cmd);
    return ResultResponse.success();
  }

}
