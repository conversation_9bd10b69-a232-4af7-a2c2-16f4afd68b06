package com.red.circle.other.app.inner.endpoint.family;

import com.red.circle.framework.dto.PageResult;
import com.red.circle.framework.dto.ResultResponse;
import com.red.circle.other.app.inner.service.family.FamilyBaseInfoClientService;
import com.red.circle.other.inner.endpoint.family.api.FamilyBaseInfoClientApi;
import com.red.circle.other.inner.model.cmd.famliy.FamilyBaseInfoQryCmd;
import com.red.circle.other.inner.model.dto.famliy.FamilyBaseInfoDTO;
import lombok.RequiredArgsConstructor;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 工会基础信息服务.
 *
 * <AUTHOR> on 2023/6/5
 */
@Validated
@RestController
@RequestMapping(value = FamilyBaseInfoClientApi.API_PREFIX, produces = MediaType.APPLICATION_JSON_VALUE)
@RequiredArgsConstructor
public class FamilyBaseInfoClientEndpoint implements FamilyBaseInfoClientApi {

  private final FamilyBaseInfoClientService familyBaseInfoService;


  /**
   * 分页.
   */
  @Override
  public ResultResponse<PageResult<FamilyBaseInfoDTO>> pageData(
      FamilyBaseInfoQryCmd queryWhere) {
    return ResultResponse.success(familyBaseInfoService.pageData(queryWhere));
  }

  /**
   * 解散工会.
   */
  @Override
  public ResultResponse<Void> delFamily(Long familyId) {
    familyBaseInfoService.delFamily(familyId);
    return ResultResponse.success();
  }

}
