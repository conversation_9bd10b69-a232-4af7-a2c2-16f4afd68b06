package com.red.circle.other.app.inner.service.family.impl;

import com.red.circle.framework.dto.PageResult;
import com.red.circle.other.app.inner.convertor.FamilyInnerConvertor;
import com.red.circle.other.app.inner.service.family.FamilyLevelConfigClientService;
import com.red.circle.other.infra.database.rds.service.family.FamilyLevelConfigService;
import com.red.circle.other.inner.model.cmd.famliy.FamilyLevelConfigCmd;
import com.red.circle.other.inner.model.cmd.famliy.FamilyLevelConfigQryCmd;
import com.red.circle.other.inner.model.dto.famliy.FamilyLevelConfigDTO;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 工会创建要求(满足任意一个，那么用户就可以创建工会) 服务实现类.
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-21
 */
@Service
@RequiredArgsConstructor
public class FamilyLevelConfigClientServiceImpl implements
    FamilyLevelConfigClientService {

  private final FamilyInnerConvertor familyInnerConvertor;
  private final FamilyLevelConfigService familyLevelConfigService;

  @Override
  public void saveFamilyLevelConfig(FamilyLevelConfigCmd param) {
    familyLevelConfigService.saveFamilyLevelConfig(familyInnerConvertor.toFamilyLevelConfig(param));
  }

  @Override
  public PageResult<FamilyLevelConfigDTO> pageData(FamilyLevelConfigQryCmd queryWhere) {
    return familyLevelConfigService.pageData(queryWhere)
        .convert(familyInnerConvertor::toFamilyLevelConfigDTO);
  }
}
