package com.red.circle.other.app.inner.service.family.impl;

import com.red.circle.external.inner.endpoint.message.ImGroupClient;
import com.red.circle.external.inner.model.cmd.message.DismissGroupCmd;
import com.red.circle.framework.core.asserts.ResponseAssert;
import com.red.circle.framework.core.response.CommonErrorCode;
import com.red.circle.framework.dto.PageResult;
import com.red.circle.other.app.inner.convertor.FamilyInnerConvertor;
import com.red.circle.other.app.inner.service.family.FamilyBaseInfoClientService;
import com.red.circle.other.domain.gateway.user.UserProfileGateway;
import com.red.circle.other.infra.database.rds.entity.family.FamilyBaseInfo;
import com.red.circle.other.infra.database.rds.entity.family.FamilyLevelConfig;
import com.red.circle.other.infra.database.rds.entity.family.FamilyMemberInfo;
import com.red.circle.other.infra.database.rds.service.approval.ApprovalUserSettingDataService;
import com.red.circle.other.infra.database.rds.service.badge.BadgeBackpackService;
import com.red.circle.other.infra.database.rds.service.family.FamilyBaseInfoService;
import com.red.circle.other.infra.database.rds.service.family.FamilyGroupChatPayRecordService;
import com.red.circle.other.infra.database.rds.service.family.FamilyLevelConfigService;
import com.red.circle.other.infra.database.rds.service.family.FamilyLevelExpService;
import com.red.circle.other.infra.database.rds.service.family.FamilyMemberInfoService;
import com.red.circle.other.infra.database.rds.service.family.FamilyMemberWeekAwardRecordService;
import com.red.circle.other.infra.database.rds.service.family.FamilyMemberWeekExpService;
import com.red.circle.other.infra.database.rds.service.family.FamilyMessageService;
import com.red.circle.other.infra.database.rds.service.family.FamilyMonthExpService;
import com.red.circle.other.infra.database.rds.service.family.FamilyWeekExpService;
import com.red.circle.other.infra.database.rds.service.props.PropsBackpackService;
import com.red.circle.other.inner.enums.material.PropsCommodityType;
import com.red.circle.other.inner.model.cmd.famliy.FamilyBaseInfoQryCmd;
import com.red.circle.other.inner.model.dto.famliy.FamilyBaseInfoDTO;
import com.red.circle.tool.core.collection.CollectionUtils;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * 工会客户端, 服务实现.
 *
 * <AUTHOR> on 2023/6/5
 */
@Service
@RequiredArgsConstructor
public class FamilyBaseInfoClientServiceImpl implements FamilyBaseInfoClientService {

  private final ImGroupClient imGroupClient;
  private final UserProfileGateway userProfileGateway;
  private final FamilyInnerConvertor familyInnerConvertor;
  private final BadgeBackpackService badgeBackpackService;
  private final FamilyMessageService familyMessageService;
  private final FamilyWeekExpService familyWeekExpService;
  private final PropsBackpackService propsBackpackService;
  private final FamilyBaseInfoService familyBaseInfoService;
  private final FamilyLevelExpService familyLevelExpService;
  private final FamilyMemberInfoService familyMemberService;
  private final FamilyMonthExpService familyMonthExpService;
  private final FamilyMemberInfoService familyMemberInfoService;
  private final FamilyLevelConfigService familyLevelConfigService;
  private final FamilyMemberWeekExpService familyMemberWeekExpService;
  private final ApprovalUserSettingDataService approvalUserSettingDataService;
  private final FamilyGroupChatPayRecordService familyGroupChatPayRecordService;
  private final FamilyMemberWeekAwardRecordService familyMemberWeekAwardRecordService;


  /**
   * 分页.
   */
  @Override
  public PageResult<FamilyBaseInfoDTO> pageData(
      FamilyBaseInfoQryCmd queryWhere) {

    PageResult<FamilyBaseInfoDTO> infoPage = familyBaseInfoService.pageData(queryWhere)
        .convert(familyInnerConvertor::toFamilyBaseInfoDTO);
    if (CollectionUtils.isEmpty(infoPage.getRecords())) {
      return infoPage;
    }

    Map<Long, BigDecimal> expMap = getExpMap(infoPage);
    Map<Long, Long> memberMap = getMemberMap(infoPage);
    Map<Long, FamilyLevelConfig> levelMap = getLevelMap(infoPage);

    return infoPage.convert(info -> {
      info.setFamilyExp(Optional.ofNullable(expMap.get(info.getId())).orElse(BigDecimal.ZERO));
      info.setMemberCount(memberMap.get(info.getId()));
      info.setLevelKey(levelMap.get(info.getFamilyLevelId()).getLevelKey());
      return info;
    });
  }

  /**
   * 解散工会.
   */
  @Override
  public void delFamily(Long familyId) {
    FamilyBaseInfo familyBaseInfo = familyBaseInfoService.query()
        .eq(FamilyBaseInfo::getId, familyId).getOne();
    ResponseAssert.notNull(CommonErrorCode.DATA_ERROR, familyBaseInfo);
    familyBaseInfoService.delFamily(familyId);
    delFamily(familyBaseInfo, getLevelConfigs(familyBaseInfo), getMembers(familyId));

    String groupId = "FAMILY_" + familyBaseInfo.getFamilyAccount();
    DismissGroupCmd dismissGroupCmd = new DismissGroupCmd();
    dismissGroupCmd.setGroupId(groupId);
    imGroupClient.destroyGroup(dismissGroupCmd);
    familyGroupChatPayRecordService.dismissGroupByFamilyId(familyId);

  }

  private Map<Long, FamilyLevelConfig> getLevelMap(PageResult<FamilyBaseInfoDTO> infoPage) {
    return familyLevelConfigService.mapByIds(
        infoPage.getRecords().stream().map(FamilyBaseInfoDTO::getFamilyLevelId).collect(
            Collectors.toSet()));
  }


  private Map<Long, BigDecimal> getExpMap(PageResult<FamilyBaseInfoDTO> infoPage) {
    return familyLevelExpService
        .mapTotalExpCountByFamilyIds(getFamilyIds(infoPage));
  }

  private Map<Long, Long> getMemberMap(PageResult<FamilyBaseInfoDTO> infoPage) {
    return familyMemberInfoService.mapMemberCountByFamilyIds(
        getFamilyIds(infoPage));
  }

  private Set<Long> getFamilyIds(PageResult<FamilyBaseInfoDTO> infoPage) {
    return infoPage.getRecords().stream().map(FamilyBaseInfoDTO::getId).collect(
        Collectors.toSet());
  }

  private List<FamilyMemberInfo> getMembers(Long familyId) {
    return familyMemberInfoService.listByFamilyId(familyId);
  }

  private void delFamily(FamilyBaseInfo familyBaseInfo, List<FamilyLevelConfig> levelConfigs,
      List<FamilyMemberInfo> members) {

    Long familyId = familyBaseInfo.getId();
    Set<Long> userIds = getUserIds(members);
    familyMemberService.deleteByFamilyId(familyId);
    familyMessageService.deleteByFamilyId(familyId);
    familyWeekExpService.deleteByFamilyId(familyId);
    familyMonthExpService.deleteByFamilyId(familyId);
    familyMemberWeekExpService.deleteByFamilyId(familyId);
    familyLevelExpService.deleteByFamilyId(familyId);
    familyMemberWeekAwardRecordService.deleteByFamilyId(familyId);
    approvalUserSettingDataService.deleteFamilyApproval(familyBaseInfo.getCreateUser());
    propsBackpackService.deleteByUserIdsAndPropsIds(userIds, getPropsIds(levelConfigs));
    badgeBackpackService
        .deleteBadge(userIds, getBadgeIds(levelConfigs));
    userProfileGateway.removeCacheAll(userIds);

    if (CollectionUtils.isNotEmpty(userIds)) {
      for (Long userId : userIds) {
        userProfileGateway.removeUseProps(
            userId, PropsCommodityType.AVATAR_FRAME.name()
        );
      }
    }
  }

  private Set<Long> getBadgeIds(List<FamilyLevelConfig> levelConfigs) {
    return levelConfigs.stream().map(FamilyLevelConfig::getBadgeId).collect(
        Collectors.toSet());
  }

  private List<FamilyLevelConfig> getLevelConfigs(FamilyBaseInfo familyBaseInfo) {
    return familyLevelConfigService.listLevelBySysOrigin(familyBaseInfo.getSysOrigin());
  }

  private Set<Long> getPropsIds(List<FamilyLevelConfig> levelConfigs) {
    return levelConfigs.stream().map(FamilyLevelConfig::getAvatarFrameId).collect(
        Collectors.toSet());
  }

  private Set<Long> getUserIds(List<FamilyMemberInfo> members) {
    return members.stream().map(FamilyMemberInfo::getMemberUserId).collect(
        Collectors.toSet());
  }
}
