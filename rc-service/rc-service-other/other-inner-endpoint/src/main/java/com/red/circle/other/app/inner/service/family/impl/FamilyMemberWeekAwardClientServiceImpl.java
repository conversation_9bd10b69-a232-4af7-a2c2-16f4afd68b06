package com.red.circle.other.app.inner.service.family.impl;

import com.red.circle.other.app.inner.service.family.FamilyMemberWeekAwardClientService;
import com.red.circle.other.infra.database.cache.service.other.FamilyCacheService;
import com.red.circle.other.infra.database.rds.service.family.FamilyMemberWeekAwardRecordService;
import com.red.circle.other.infra.database.rds.service.family.FamilyMemberWeekExpService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * 工会成员每周领奖.
 *
 * <AUTHOR> on 2023/11/16
 */
@Service
@RequiredArgsConstructor
public class FamilyMemberWeekAwardClientServiceImpl implements FamilyMemberWeekAwardClientService {

  private final FamilyCacheService familyCacheService;
  private final FamilyMemberWeekExpService familyMemberWeekExpService;
  private final FamilyMemberWeekAwardRecordService familyMemberWeekAwardRecordService;

  @Override
  public void deleteAllWeekActivity() {
    familyMemberWeekExpService.delete().execute();
    familyMemberWeekAwardRecordService.delete().execute();
  }

  @Override
  public void setFamilyWeekRewardTime(String dateStr) {
    familyCacheService.setFamilyWeekRewardTime(dateStr);
  }
}
