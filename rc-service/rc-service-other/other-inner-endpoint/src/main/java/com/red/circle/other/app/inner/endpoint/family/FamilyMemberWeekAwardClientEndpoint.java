package com.red.circle.other.app.inner.endpoint.family;

import com.red.circle.framework.dto.ResultResponse;
import com.red.circle.other.app.inner.service.family.FamilyMemberWeekAwardClientService;
import com.red.circle.other.inner.endpoint.family.api.FamilyMemberWeekAwardClientApi;
import lombok.RequiredArgsConstructor;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 工会成员每周领奖.
 *
 * <AUTHOR> on 2023/11/16
 */
@Validated
@RestController
@RequestMapping(value = FamilyMemberWeekAwardClientApi.API_PREFIX, produces = MediaType.APPLICATION_JSON_VALUE)
@RequiredArgsConstructor
public class FamilyMemberWeekAwardClientEndpoint implements FamilyMemberWeekAwardClientApi {

  private final FamilyMemberWeekAwardClientService familyMemberWeekAwardClientService;

  @Override
  public ResultResponse<Void> deleteAllWeekActivity() {
    familyMemberWeekAwardClientService.deleteAllWeekActivity();
    return ResultResponse.success();
  }

  @Override
  public ResultResponse<Void> setFamilyWeekRewardTime(String dateStr) {
    familyMemberWeekAwardClientService.setFamilyWeekRewardTime(dateStr);
    return ResultResponse.success();
  }

}
