package com.red.circle.other.app.inner.endpoint.family;

import com.red.circle.framework.dto.ResultResponse;
import com.red.circle.other.app.inner.service.family.FamilyDailyTaskTriggerRecordClientService;
import com.red.circle.other.inner.endpoint.family.api.FamilyDailyTaskTriggerRecordClientApi;
import lombok.RequiredArgsConstructor;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 工会每日任务触发记录.
 *
 * <AUTHOR> on 2023/10/22
 */
@Validated
@RestController
@RequestMapping(value = FamilyDailyTaskTriggerRecordClientApi.API_PREFIX, produces = MediaType.APPLICATION_JSON_VALUE)
@RequiredArgsConstructor
public class FamilyDailyTaskTriggerRecordClientEndpoint implements
    FamilyDailyTaskTriggerRecordClientApi {

  private final FamilyDailyTaskTriggerRecordClientService familyDailyTaskTriggerRecordClientService;

  @Override
  public ResultResponse<Void> deleteAll() {
    familyDailyTaskTriggerRecordClientService.deleteAll();
    return ResultResponse.success();
  }
}
