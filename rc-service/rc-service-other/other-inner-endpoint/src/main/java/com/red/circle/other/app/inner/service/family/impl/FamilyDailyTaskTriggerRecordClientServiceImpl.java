package com.red.circle.other.app.inner.service.family.impl;

import com.red.circle.other.app.inner.service.family.FamilyDailyTaskTriggerRecordClientService;
import com.red.circle.other.infra.database.rds.service.family.FamilyDailyTaskTriggerRecordService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * 工会每日任务触发记录.
 *
 * <AUTHOR> on 2023/10/22
 */
@Service
@RequiredArgsConstructor
public class FamilyDailyTaskTriggerRecordClientServiceImpl implements
    FamilyDailyTaskTriggerRecordClientService {

  private final FamilyDailyTaskTriggerRecordService familyDailyTaskTriggerRecordService;

  @Override
  public void deleteAll() {
    familyDailyTaskTriggerRecordService.deleteAll();
  }

}
