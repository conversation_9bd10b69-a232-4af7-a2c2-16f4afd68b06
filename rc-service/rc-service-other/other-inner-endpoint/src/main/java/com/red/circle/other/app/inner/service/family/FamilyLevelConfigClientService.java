package com.red.circle.other.app.inner.service.family;


import com.red.circle.framework.dto.PageResult;
import com.red.circle.other.inner.model.cmd.famliy.FamilyLevelConfigCmd;
import com.red.circle.other.inner.model.cmd.famliy.FamilyLevelConfigQryCmd;
import com.red.circle.other.inner.model.dto.famliy.FamilyLevelConfigDTO;

/**
 * <p>
 * 工会成员 服务类.
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-21
 */
public interface FamilyLevelConfigClientService {

  void saveFamilyLevelConfig(FamilyLevelConfigCmd param);

  PageResult<FamilyLevelConfigDTO> pageData(FamilyLevelConfigQryCmd queryWhere);
}
