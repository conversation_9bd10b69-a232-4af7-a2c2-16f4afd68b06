package com.red.circle.other.app.inner.endpoint.approval;

import com.red.circle.framework.dto.PageResult;
import com.red.circle.framework.dto.ResultResponse;
import com.red.circle.other.app.inner.service.approval.ApprovalFamilySettingDataClientService;

import com.red.circle.other.inner.endpoint.dynamic.api.ApprovalFamilySettingDataClientApi;
import com.red.circle.other.inner.model.cmd.approval.ApprovalFamilyCmd;
import com.red.circle.other.inner.model.cmd.approval.ApprovalProfileDescQryCmd;
import com.red.circle.other.inner.model.dto.approval.ApprovalFamilyDTO;
import lombok.RequiredArgsConstructor;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 工会资料审批
 *
 * <AUTHOR> on 2024/1/20
 */
@Validated
@RestController
@RequestMapping(value = ApprovalFamilySettingDataClientApi.API_PREFIX, produces = MediaType.APPLICATION_JSON_VALUE)
@RequiredArgsConstructor
public class ApprovalFamilySettingDataClientEndPoint implements ApprovalFamilySettingDataClientApi {

  private final ApprovalFamilySettingDataClientService approvalFamilySettingDataClientService;

  @Override
  public ResultResponse<PageResult<ApprovalFamilyDTO>> pageFamilyApproval(
      ApprovalProfileDescQryCmd query) {
    return ResultResponse.success(approvalFamilySettingDataClientService.pageFamilyApproval(query));
  }

  @Override
  public ResultResponse<Void> notPass(ApprovalFamilyCmd cmd) {
    approvalFamilySettingDataClientService.notPass(cmd);
    return ResultResponse.success();
  }
}
