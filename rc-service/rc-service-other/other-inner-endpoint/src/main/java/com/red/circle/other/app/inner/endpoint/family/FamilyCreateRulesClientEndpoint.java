package com.red.circle.other.app.inner.endpoint.family;

import com.red.circle.framework.dto.PageResult;
import com.red.circle.framework.dto.ResultResponse;
import com.red.circle.other.app.inner.service.family.FamilyCreateRulesClientService;
import com.red.circle.other.inner.endpoint.family.api.FamilyCreateRulesClientApi;
import com.red.circle.other.inner.model.cmd.famliy.FamilyCreateRuleCmd;
import com.red.circle.other.inner.model.cmd.famliy.FamilyCreateRuleQryCmd;
import com.red.circle.other.inner.model.dto.famliy.FamilyCreateRuleDTO;
import lombok.RequiredArgsConstructor;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 工会创建规则表 服务.
 *
 * <AUTHOR> on 2023/6/5
 */
@Validated
@RestController
@RequestMapping(value = FamilyCreateRulesClientApi.API_PREFIX, produces = MediaType.APPLICATION_JSON_VALUE)
@RequiredArgsConstructor
public class FamilyCreateRulesClientEndpoint implements FamilyCreateRulesClientApi {

  private final FamilyCreateRulesClientService familyCreateRulesClientService;


  /**
   * 分页.
   */
  @Override
  public ResultResponse<PageResult<FamilyCreateRuleDTO>> pageData(
      FamilyCreateRuleQryCmd queryWhere) {
    return ResultResponse.success(familyCreateRulesClientService.pageData(queryWhere));
  }

  /**
   * 添加、更新.
   */
  @Override
  public ResultResponse<Void> saveFamilyCreateRules(FamilyCreateRuleCmd cmd) {
    familyCreateRulesClientService.saveFamilyCreateRules(cmd);
    return ResultResponse.success();
  }

}
