package com.red.circle.other.app.inner.service.material.props;

import com.red.circle.framework.dto.PageResult;
import com.red.circle.other.inner.model.cmd.material.PropsResourcesMergeCmd;
import com.red.circle.other.inner.model.cmd.material.PropsSourceRecordQryCmd;
import com.red.circle.other.inner.model.dto.material.PropsRedPacketSkinDTO;
import com.red.circle.other.inner.model.dto.material.PropsResourcesDTO;
import com.red.circle.other.inner.model.dto.material.PropsResourcesMergeDTO;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 道具资源.
 *
 * <AUTHOR> on 2023/11/7
 */
public interface PropsSourceClientService {

  /**
   * 获取指定资源信息映射.
   */
  PropsResourcesDTO getById(Long id);

  /**
   * 获取指定一组资源信息映射.
   *
   * @param ids id集合
   * @return map
   */
  Map<Long, PropsResourcesDTO> mapByIds(Set<Long> ids);

  /**
   * 获取一批组合道具资源.
   */
  PropsResourcesMergeDTO mapMergeByIds(PropsResourcesMergeCmd cmd);

  /**
   * 查询平台道具列表
   */
  List<PropsResourcesDTO> listSysOriginPropsResources(String sysOrigin, String type);

  /**
   * 获取指定资源类型，不包含工会.
   */
  List<PropsResourcesDTO> listExcludeFamily(String sysOrigin, String type);

  /**
   * 资源上/下架.
   */
  void offShelfPropsResources(Long id, Boolean offShelf);

  /**
   * 道具资源分页列表(运营后台).
   */
  PageResult<PropsResourcesDTO> pagePropsResourcesOps(PropsSourceRecordQryCmd qryCmd);

  /**
   * 添加或修改道具资源.
   */
  void addOrUpdatePropsResources(PropsResourcesDTO dto);

  /**
   * 获得红包道具细节皮肤.
   */
  PropsRedPacketSkinDTO getPropsRedPacketSkin(Long id);

  void updateRedisCache(PropsResourcesDTO dto);
}
