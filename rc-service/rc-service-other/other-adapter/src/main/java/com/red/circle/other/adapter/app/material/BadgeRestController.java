package com.red.circle.other.adapter.app.material;

import com.red.circle.common.business.dto.cmd.app.AppRoomIdCmd;
import com.red.circle.common.business.dto.cmd.app.AppUserIdCmd;
import com.red.circle.framework.web.controller.BaseController;
import com.red.circle.other.app.dto.clientobject.material.BadgeBackpackCO;
import com.red.circle.other.app.dto.clientobject.material.BadgeGroupTableCO;
import com.red.circle.other.app.dto.clientobject.material.BadgeTableCO;
import com.red.circle.other.app.dto.cmd.material.BadgeProgressRateCmd;
import com.red.circle.other.app.dto.cmd.material.UserBadgeQueryCmd;
import com.red.circle.other.app.dto.cmd.material.ViewOtherBadgeQueryCmd;
import com.red.circle.other.app.service.material.BadgeService;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 徽章服务.
 *
 * <AUTHOR> on 2021/3/12
 * @eo.api-type http
 * @eo.groupName 道具服务.徽章
 * @eo.path /material/badge
 */
@RequiredArgsConstructor
@RestController
@RequestMapping(value = "/material/badge", produces = MediaType.APPLICATION_JSON_VALUE)
public class BadgeRestController extends BaseController {

  private final BadgeService badgeService;

  /**
   * 徽章列表.
   *
   * @eo.name 徽章列表.
   * @eo.url
   * @eo.method get
   * @eo.request-type formdata
   */
  @GetMapping
  public List<BadgeGroupTableCO> listBadgeTable(UserBadgeQueryCmd cmd) {
    return badgeService.listBadgeTable(cmd);
  }

  /**
   * 徽章进度情况.
   *
   * @eo.name 徽章进度情况.
   * @eo.url /progress-rate
   * @eo.method get
   * @eo.request-type formdata
   */
  @GetMapping("/progress-rate")
  public Long badgeProgressRate(@Validated BadgeProgressRateCmd cmd) {
    return badgeService.badgeProgressRate(cmd).longValue();
  }

  /**
   * 用户徽章背包.
   *
   * @eo.name 用户徽章背包.
   * @eo.url /backpack
   * @eo.method get
   * @eo.request-type formdata
   */
  @GetMapping("/backpack")
  public List<BadgeBackpackCO> listBadgeBackpack(@Validated AppUserIdCmd cmd) {
    return badgeService.listBadgeBackpack(cmd);
  }

  /**
   * 房间徽章.
   *
   * @eo.name 房间徽章.
   * @eo.url /backpack/room
   * @eo.method get
   * @eo.request-type formdata
   */
  @GetMapping("/backpack/room")
  public List<BadgeBackpackCO> listRoomBadgeBackpack(@Validated AppRoomIdCmd cmd) {
    return badgeService.listRoomBadgeBackpack(cmd);
  }

  /**
   * 查看他人徽章
   * @param cmd
   * @return
   */
  @GetMapping("/view/other")
  public List<BadgeGroupTableCO> viewOtherBadge(ViewOtherBadgeQueryCmd cmd) {
    return badgeService.viewOtherBadge(cmd);
  }

  /**
   * 获取用户工会徽章
   */
  @GetMapping("/family")
  public BadgeTableCO getFamilyBadge(@Validated AppUserIdCmd cmd){
    return badgeService.getFamilyBadge(cmd);
  }

}
