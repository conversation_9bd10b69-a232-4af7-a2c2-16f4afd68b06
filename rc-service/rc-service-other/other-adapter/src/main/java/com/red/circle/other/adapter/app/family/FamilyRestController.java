package com.red.circle.other.adapter.app.family;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.red.circle.common.business.dto.cmd.AppExtCommand;
import com.red.circle.framework.web.controller.BaseController;
import com.red.circle.other.app.dto.clientobject.family.FamilyBaseInfoCO;
import com.red.circle.other.app.dto.clientobject.family.FamilyCreateRulesCO;
import com.red.circle.other.app.dto.clientobject.family.FamilyLeaderboardCO;
import com.red.circle.other.app.dto.clientobject.family.FamilyLevelParentCacheCO;
import com.red.circle.other.app.dto.clientobject.family.FamilyMemberCO;
import com.red.circle.other.app.dto.clientobject.family.FamilyMsgListCO;
import com.red.circle.other.app.dto.clientobject.family.FamilyRewardCO;
import com.red.circle.other.app.dto.clientobject.family.FamilyUserInfoCO;
import com.red.circle.other.app.dto.clientobject.room.RoomVoiceProfileCO;
import com.red.circle.other.app.dto.cmd.family.ApplyJoinFamilyCmd;
import com.red.circle.other.app.dto.cmd.family.FamilyAdminQueryCmd;
import com.red.circle.other.app.dto.cmd.family.FamilyBaseInfoQueryCmd;
import com.red.circle.other.app.dto.cmd.family.FamilyCreateCmd;
import com.red.circle.other.app.dto.cmd.family.FamilyDailyTaskCmd;
import com.red.circle.other.app.dto.cmd.family.FamilyGrantUserRoleCmd;
import com.red.circle.other.app.dto.cmd.family.FamilyGroupChatPayCheckCmd;
import com.red.circle.other.app.dto.cmd.family.FamilyGroupChatQueryCmd;
import com.red.circle.other.app.dto.cmd.family.FamilyInfoEditCmd;
import com.red.circle.other.app.dto.cmd.family.FamilyMemberListCmd;
import com.red.circle.other.app.dto.cmd.family.FamilyMemberListQueryCmd;
import com.red.circle.other.app.dto.cmd.family.FamilyMessageHandleCmd;
import com.red.circle.other.app.dto.cmd.family.FamilyMessageListCmd;
import com.red.circle.other.app.dto.cmd.family.FamilyOnlineRoomQueryCmd;
import com.red.circle.other.app.dto.cmd.family.FamilyRemoveUserCmd;
import com.red.circle.other.app.dto.cmd.family.FamilyUserInfoQueryCmd;
import com.red.circle.other.app.dto.cmd.family.ReceiveFamilyAwardCmd;
import com.red.circle.other.app.service.family.FamilyService;
import java.math.BigDecimal;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <p>
 * 工会服务 前端控制器.
 * </p>
 *
 * <AUTHOR> on 2023/9/19
 * @eo.api-type http
 * @eo.groupName 工会服务
 * @eo.path /family
 */
@RequiredArgsConstructor
@RestController
@RequestMapping(value = "/family", produces = MediaType.APPLICATION_JSON_VALUE)
public class FamilyRestController extends BaseController {

  private final FamilyService familyService;

  /**
   * 创建工会.
   *
   * @eo.name 创建工会.
   * @eo.url /create
   * @eo.method post
   * @eo.request-type json
   */
  @PostMapping("/create")
  @JsonSerialize(using = ToStringSerializer.class)
  public Long create(@RequestBody @Validated FamilyCreateCmd cmd) {
    return familyService.create(cmd);
  }

  /**
   * 编辑工会信息.
   *
   * @eo.name 编辑工会信息.
   * @eo.url /edit
   * @eo.method post
   * @eo.request-type json
   */
  @PostMapping("/edit")
  public void edit(@RequestBody @Validated FamilyInfoEditCmd cmd) {
    familyService.edit(cmd);
  }

  /**
   * 工会族长信息(与工会成员信息 数据结构一致).
   *
   * @eo.name 工会族长信息(与工会成员信息 数据结构一致).
   * @eo.url /admin
   * @eo.method get
   * @eo.request-type formdata
   */
  @GetMapping("/admin")
  public FamilyMemberCO getAdmin(FamilyAdminQueryCmd cmd) {
    return familyService.getAdmin(cmd);
  }

  /**
   * 工会成员信息.(旧：list_member)
   *
   * @eo.name 工会成员信息.
   * @eo.url /list/member
   * @eo.method get
   * @eo.request-type formdata
   */
  @GetMapping("/list/member")
  public List<FamilyMemberCO> listMember(FamilyMemberListQueryCmd cmd) {
    return familyService.listMember(cmd);
  }

  /**
   * 工会成员信息-不分页.
   * @eo.name 工会成员信息.
   * @eo.url /members
   * @eo.method get
   * @eo.request-type formdata
   */
  @GetMapping("/members")
  public List<FamilyMemberCO> getMembers(FamilyMemberListCmd cmd) {
    return familyService.getMembers(cmd);
  }

  /**
   * 工会基本信息.（旧：base_info）
   *
   * @eo.name 工会基本信息.
   * @eo.url /base-info
   * @eo.method get
   * @eo.request-type formdata
   */
  @GetMapping("/base-info")
  public FamilyBaseInfoCO getBaseInfo(FamilyBaseInfoQueryCmd cmd) {
    return familyService.getBaseInfo(cmd);
  }

  /**
   * 授权用户工会权限.(旧：grant_user_role)
   *
   * @eo.name 授权用户工会权限.
   * @eo.url /grant-user-role
   * @eo.method post
   * @eo.request-type json
   */
  @PostMapping("/grant-user-role")
  public void grantUserRole(@RequestBody FamilyGrantUserRoleCmd cmd) {
    familyService.grantUserRole(cmd);
  }

  /**
   * 工会排行榜 周榜、月榜、自己工会信息.
   *
   * @eo.name 工会排行榜 周榜、月榜、自己工会信息.
   * @eo.url /leaderboard
   * @eo.method get
   * @eo.request-type formdata
   */
  @GetMapping("/leaderboard")
  public FamilyLeaderboardCO getLeaderboard(AppExtCommand cmd) {
    return familyService.getLeaderboard(cmd);
  }

  /**
   * 全部等级配置集合.
   *
   * @eo.name 全部等级配置集合.
   * @eo.url /list/level-config
   * @eo.method get
   * @eo.request-type formdata
   */
  @GetMapping("/list/level-config")
  public List<FamilyLevelParentCacheCO> listLevelConfig(AppExtCommand cmd) {
    return familyService.listLevelConfig(cmd);
  }

  /**
   * 申请加入工会.
   *
   * @eo.name 申请加入工会.
   * @eo.url /apply-join
   * @eo.method post
   * @eo.request-type json
   */
  @PostMapping("/apply-join")
  public void applyJoin(@RequestBody @Validated ApplyJoinFamilyCmd cmd) {
    familyService.applyJoin(cmd);
  }

  /**
   * 工会消息处理.
   *
   * @eo.name 工会消息处理.
   * @eo.url /message/handle
   * @eo.method post
   * @eo.request-type json
   */
  @PostMapping("/message/handle")
  public void messageHandle(@RequestBody @Validated FamilyMessageHandleCmd cmd) {
    familyService.messageHandle(cmd);
  }

  /**
   * 工会消息列表.
   *
   * @eo.name 工会消息列表.
   * @eo.url /list/message
   * @eo.method get
   * @eo.request-type formdata
   */
  @GetMapping("/list/message")
  public List<FamilyMsgListCO> listMessage(FamilyMessageListCmd cmd) {
    return familyService.listMessage(cmd);
  }

  /**
   * 工会在线房间.
   *
   * @eo.name 工会在线房间.
   * @eo.url /list/online-room
   * @eo.method get
   * @eo.request-type formdata
   */
  @GetMapping("/list/online-room")
  public List<RoomVoiceProfileCO> listOnlineRoom(@Validated FamilyOnlineRoomQueryCmd cmd) {
    return familyService.listOnlineRoom(cmd);
  }

  /**
   * 将移除工会用户.
   *
   * @eo.name 将移除工会用户.
   * @eo.url /remove-user
   * @eo.method get
   * @eo.request-type formdata
   */
  @GetMapping("/remove-user")
  public void removeUser(FamilyRemoveUserCmd cmd) {
    familyService.removeUser(cmd);
  }

  /**
   * 用户主动退出工会.
   *
   * @eo.name 用户主动退出工会.
   * @eo.url /user-exit
   * @eo.method get
   * @eo.request-type formdata
   */
  @GetMapping("/user-exit")
  public void userExit(AppExtCommand cmd) {
    familyService.userExit(cmd);
  }

  /**
   * 成员在工会中的基本信息.
   *
   * @eo.name 成员在工会中的基本信息.
   * @eo.url /member-user-info
   * @eo.method get
   * @eo.request-type formdata
   */
  @GetMapping("/member-user-info")
  public FamilyUserInfoCO memberUserInfo(@Validated FamilyUserInfoQueryCmd cmd) {
    return familyService.memberUserInfo(cmd);
  }

  /**
   * 工会每日任务.
   *
   * @eo.name 工会每日任务.
   * @eo.url /trigger-daily-task
   * @eo.method get
   * @eo.request-type formdata
   */
  @GetMapping("/trigger-daily-task")
  public void triggerDailyTask(FamilyDailyTaskCmd cmd) {
    familyService.triggerDailyTask(cmd);
  }

  /**
   * 创建工会规则条件.
   *
   * @eo.name 创建工会规则条件.
   * @eo.url /family-create-rule
   * @eo.method get
   * @eo.request-type formdata
   */
  @GetMapping("/family-create-rule")
  public FamilyCreateRulesCO familyCreateRule(AppExtCommand cmd) {
    return familyService.familyCreateRule(cmd);
  }

  /**
   * give 我的工会宝箱奖励.
   *
   * @eo.name give 我的工会宝箱奖励.
   * @eo.url /my-family-reward
   * @eo.method get
   * @eo.request-type formdata
   */
  @GetMapping("/my-family-reward")
  public FamilyRewardCO myFamilyReward(AppExtCommand cmd) {
    return familyService.myFamilyReward(cmd);
  }

  /**
   * 领取我的工会宝箱奖励.
   *
   * @eo.name 领取我的工会宝箱奖励.
   * @eo.url /receive-award
   * @eo.method post
   * @eo.request-type json
   */
  @PostMapping("/receive-award")
  public void receiveAward(@RequestBody @Validated ReceiveFamilyAwardCmd cmd) {
    familyService.receiveFamilyAward(cmd);
  }

  /**
   * 解锁工会群聊.
   *
   * @eo.name 解锁工会群聊.
   * @eo.url /purchasing/group-chat
   * @eo.method post
   * @eo.request-type json
   */
  @PostMapping("/purchasing/group-chat")
  public BigDecimal purchasingFamilyGroupChat(@RequestBody @Validated FamilyGroupChatQueryCmd cmd) {
    return familyService.purchasingFamilyGroupChat(cmd);
  }

  /**
   * 解散-工会群聊.
   */
  @PostMapping("/dismiss/group-chat")
  public void dismissFamilyGroupChat(@RequestBody FamilyGroupChatQueryCmd cmd) {

    familyService.dismissFamilyGroupChat(cmd);
  }

  /**
   * 工会群聊是否解锁.
   *
   * @eo.name 工会群聊是否解锁.
   * @eo.url /group/chat/exists
   * @eo.method get
   * @eo.request-type json
   */
  @GetMapping("/group/chat/exists")
  public Boolean getFamilyGroupChatIsPay(@Validated FamilyGroupChatPayCheckCmd cmd) {
    return familyService.getFamilyGroupChatIsPay(cmd);
  }

}
