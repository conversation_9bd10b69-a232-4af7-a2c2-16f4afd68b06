package com.red.circle.other.infra.database.rds.entity.family;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.red.circle.framework.mybatis.entity.TimestampBaseEntity;
import java.io.Serial;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 工会基础信息表.
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-20
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("family_base_info")
public class FamilyBaseInfo extends TimestampBaseEntity {

  @Serial
  private static final long serialVersionUID = 1L;

  /**
   * 主键标识.
   */
  @TableId(value = "id", type = IdType.ASSIGN_ID)
  private Long id;

  /**
   * 来源系统.
   */
  @TableField("sys_origin")
  private String sysOrigin;

  /**
   * 工会账号.
   */
  @TableField("family_account")
  private Long familyAccount;

  /**
   * 工会头像.
   */
  @TableField("family_avatar")
  private String familyAvatar;

  /**
   * 工会名称.
   */
  @TableField("family_name")
  private String familyName;

  /**
   * 工会等级ID.
   */
  @TableField("family_level_id")
  private Long familyLevelId;

  /**
   * 工会状态.
   */
  @TableField("family_status")
  private String familyStatus;

  /**
   * 工会公告.
   */
  @TableField("family_notice")
  private String familyNotice;
  @TableField("family_whatapp")
  private String familyWhatapp;
  @TableField("leader_id_card_photo")
  private String leaderIdCardPhoto;

}
