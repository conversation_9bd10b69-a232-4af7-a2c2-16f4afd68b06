package com.red.circle.other.infra.database.rds.service.family;


import com.red.circle.framework.dto.PageResult;
import com.red.circle.framework.mybatis.service.BaseService;
import com.red.circle.other.infra.database.rds.entity.family.FamilyCreateRules;
import com.red.circle.other.inner.model.cmd.famliy.FamilyCreateRuleQryCmd;

/**
 * <p>
 * 工会创建要求(满足任意一个，那么用户就可以创建工会) 服务类.
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-21
 */
public interface FamilyCreateRulesService extends BaseService<FamilyCreateRules> {

  FamilyCreateRules getFamilyCreateRules(String sysOrigin);

  void saveFamilyCreateRules(FamilyCreateRules param);

  PageResult<FamilyCreateRules> pageData(FamilyCreateRuleQryCmd queryWhere);
}
