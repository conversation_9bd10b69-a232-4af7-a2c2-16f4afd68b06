package com.red.circle.other.infra.database.rds.service.family.impl;


import com.red.circle.framework.mybatis.service.impl.BaseServiceImpl;
import com.red.circle.other.infra.database.rds.dao.family.FamilyMemberWeekAwardRecordDAO;
import com.red.circle.other.infra.database.rds.entity.family.FamilyMemberWeekAwardRecord;
import com.red.circle.other.infra.database.rds.service.family.FamilyMemberWeekAwardRecordService;
import com.red.circle.tool.core.collection.CollectionUtils;
import java.util.List;
import java.util.Objects;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 工会成员每周领奖记录(每周清空记录值).
 * </p>
 *
 * <AUTHOR>
 * @since 2021-08-06
 */
@Service
@RequiredArgsConstructor
public class FamilyMemberWeekAwardRecordServiceImpl extends
    BaseServiceImpl<FamilyMemberWeekAwardRecordDAO, FamilyMemberWeekAwardRecord> implements
    FamilyMemberWeekAwardRecordService {

  @Override
  public List<FamilyMemberWeekAwardRecord> listByFamily(Long familyId, Long memberId) {
    if (Objects.isNull(familyId) || Objects.isNull(memberId)) {
      return CollectionUtils.newArrayList();
    }
    return query()
        .eq(FamilyMemberWeekAwardRecord::getFamilyMemberId, memberId)
        .eq(FamilyMemberWeekAwardRecord::getFamilyId, familyId)
        .orderByDesc(FamilyMemberWeekAwardRecord::getCreateTime)
        .list();
  }

  @Override
  public Boolean isReceived(Long familyId, Long memberId, Long rewardRuleId) {
    if (Objects.isNull(familyId) || Objects.isNull(memberId) || Objects.isNull(rewardRuleId)) {
      return Boolean.FALSE;
    }
    return Objects.nonNull(query()
        .eq(FamilyMemberWeekAwardRecord::getFamilyMemberId, memberId)
        .eq(FamilyMemberWeekAwardRecord::getFamilyId, familyId)
        .eq(FamilyMemberWeekAwardRecord::getRewardRuleId, rewardRuleId)
        .getOne());
  }


  @Override
  public void deleteByFamilyId(Long familyId) {
    if (Objects.isNull(familyId)) {
      return;
    }
    delete().eq(FamilyMemberWeekAwardRecord::getFamilyId, familyId).execute();
  }


  @Override
  public void deleteByFamilyIdByMemberId(Long familyId, Long memberId) {
    if (Objects.isNull(familyId) || Objects.isNull(memberId)) {
      return;
    }
    delete()
        .eq(FamilyMemberWeekAwardRecord::getFamilyId, familyId)
        .eq(FamilyMemberWeekAwardRecord::getFamilyMemberId, memberId)
        .execute();
  }
}
