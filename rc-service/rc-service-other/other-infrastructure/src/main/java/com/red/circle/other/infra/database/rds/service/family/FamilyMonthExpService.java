package com.red.circle.other.infra.database.rds.service.family;


import com.red.circle.framework.mybatis.service.BaseService;
import com.red.circle.other.infra.database.rds.entity.family.FamilyMonthExp;
import java.util.List;

/**
 * <p>
 * 工会每月贡献值榜单 服务类.
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-21
 */
public interface FamilyMonthExpService extends BaseService<FamilyMonthExp> {

  /**
   * 添加本月经验.
   */
  void incrThisMonthExp(String sysOrigin, Long familyId, String regionId, Long exp);

  /**
   * 查询平台本月经验.
   */
  List<FamilyMonthExp> listThisMonthBySysOrigin(String sysOrigin, String regionId);

  /**
   * 获取工会本月经验.
   */
  FamilyMonthExp getThisMonthByFamilyId(Long familyId);

  void deleteByFamilyId(Long familyId);
}
