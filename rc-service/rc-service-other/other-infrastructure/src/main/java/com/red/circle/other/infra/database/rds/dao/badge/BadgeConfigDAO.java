package com.red.circle.other.infra.database.rds.dao.badge;

import com.red.circle.framework.mybatis.dao.BaseDAO;
import com.red.circle.other.infra.database.rds.entity.badge.BadgeConfig;
import com.red.circle.other.inner.enums.material.BadgeConfigTypeEnum;
import java.util.List;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * 徽章配置 Mapper 接口.
 * </p>
 *
 * <AUTHOR>
 * @since 2021-03-12
 */
public interface BadgeConfigDAO extends BaseDAO<BadgeConfig> {

  /**
   * 获取用户等级最高的工会徽章配置
   * @param userId
   * @return
   */
  BadgeConfig getUserMaxLevelFamilyBadge(@Param("userId") Long userId);

  /**
   * 获取徽章列表
   * @param type
   * @param sysOrgin
   * @return
   */
  List<BadgeConfig> getBadgeConfigList(
      @Param("type") BadgeConfigTypeEnum type, @Param("sysOrgin") String sysOrgin);
}
