package com.red.circle.other.infra.database.cache.key;

import com.red.circle.component.redis.RedisKeys;

/**
 * 工会状态.
 *
 * <AUTHOR>
 * @since 2021-07-21
 */
public enum GameRoomVoteKeys implements RedisKeys {

  /**
   * 投票游戏选项票数.
   */
  GAME_OPTION_VOTES,
  /**
   * 投票信息.
   */
  ROOM_VOTE,

  /**
   * 标记用户是否已投票.
   */
  ROOM_VOTE_USER_SIGN,

  /**
   * 标记用户投票记录id.
   */
  ROOM_VOTE_USER_OPTION_ID;

  @Override
  public String businessPrefix() {
    return "GAME_ROOM_VOTE";
  }

  @Override
  public String businessKey() {
    return this.name();
  }

}
