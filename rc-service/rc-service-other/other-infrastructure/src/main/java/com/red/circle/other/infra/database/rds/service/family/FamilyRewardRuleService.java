package com.red.circle.other.infra.database.rds.service.family;

import com.red.circle.framework.dto.PageResult;
import com.red.circle.framework.mybatis.service.BaseService;
import com.red.circle.other.infra.database.rds.entity.family.FamilyRewardRule;
import com.red.circle.other.inner.model.cmd.famliy.FamilyRewardRuleQryCmd;
import java.util.List;

/**
 * <p>
 * 工会奖励规则表 服务类.
 * </p>
 *
 * <AUTHOR>
 * @since 2021-08-06
 */
public interface FamilyRewardRuleService extends BaseService<FamilyRewardRule> {

  List<FamilyRewardRule> listBySysOrigin(String sysOrigin);

  FamilyRewardRule getRuleById(Long id);


  void saveFamilyRewardRule(FamilyRewardRule familyRewardRule);

  PageResult<FamilyRewardRule> pageData(FamilyRewardRuleQryCmd queryWhere);
}
