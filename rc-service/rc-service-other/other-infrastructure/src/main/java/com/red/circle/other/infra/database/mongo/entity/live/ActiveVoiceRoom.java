package com.red.circle.other.infra.database.mongo.entity.live;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.red.circle.other.infra.database.mongo.entity.live.assist.FamilyProfile;
import com.red.circle.other.infra.database.mongo.entity.live.assist.RunGame;
import java.io.Serial;
import java.io.Serializable;
import java.sql.Timestamp;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.Accessors;
import lombok.experimental.FieldDefaults;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

/**
 * 活跃语音房间.
 *
 * <AUTHOR> on 2020/12/11
 */
@Data
@Accessors(chain = true)
@Document("active_voice_room")
@FieldDefaults(level = AccessLevel.PRIVATE)
public class ActiveVoiceRoom implements Serializable {

  @Serial
  private static final long serialVersionUID = 1L;

  /**
   * id.
   */
  @Id
  @JsonSerialize(using = ToStringSerializer.class)
  Long id;

  /**
   * 时序ID每次创建刷新.
   */
  @JsonSerialize(using = ToStringSerializer.class)
  Long timeId;

  /**
   * 归属系统平台.
   */
  String sysOrigin;

  /**
   * 房主id.
   */
  @JsonSerialize(using = ToStringSerializer.class)
  Long userId;

  /**
   * 房间account.
   */
  String roomAccount;

  /**
   * 工会资料.
   */
  FamilyProfile familyProfile;

  /**
   * 运行游戏.
   */
  RunGame runGame;

  /**
   * SVip等级.
   */
  String superVipLevel;

  /**
   * 热门房间（true 不是热门， false 热门）.
   */
  Boolean hot;

  /**
   * 置顶房间.
   */
  Integer fixedWeights;

  /**
   * 国家code.
   */
  String countryCode;

  /**
   * 国家名字.
   */
  String countryName;

  /**
   * 区域.
   */
  String region;

  /**
   * 在线数量.
   */
  Long onlineQuantity;

  /**
   * 权重.
   */
  Integer weights;

  /**
   * 过期时间.
   */
  Timestamp expiredTime;

  /**
   * 创建时间.
   */
  Timestamp createTime;

  /**
   * 修改时间.
   */
  Timestamp updateTime;

}
