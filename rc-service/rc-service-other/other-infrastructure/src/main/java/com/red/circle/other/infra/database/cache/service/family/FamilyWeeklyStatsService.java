package com.red.circle.other.infra.database.cache.service.family;

import com.red.circle.other.infra.database.rds.entity.family.AnchorWeeklyPolicy;
import java.util.List;

/**
 * 工会周统计服务.
 *
 * <AUTHOR>
 */
public interface FamilyWeeklyStatsService {

  /**
   * 增加用户本周收礼金币统计.
   *
   * @param familyId 工会ID
   * @param userId 用户ID
   * @param giftGoldValue 礼物金币价值(分)
   * @return 本周累计收礼金币数量(分)
   */
  Long addWeeklyGiftGold(Long familyId, Long userId, Long giftGoldValue);

  /**
   * 获取用户本周收礼金币统计.
   *
   * @param familyId 工会ID
   * @param userId 用户ID
   * @return 本周累计收礼金币数量(分)
   */
  Long getWeeklyGiftGold(Long familyId, Long userId);

  /**
   * 获取缓存的政策列表.
   *
   * @return 政策列表
   */
  List<AnchorWeeklyPolicy> getCachedPolicies();

  /**
   * 缓存政策列表.
   *
   * @param policies 政策列表
   */
  void cachePolicies(List<AnchorWeeklyPolicy> policies);

  /**
   * 根据累计金额匹配政策.
   *
   * @param weeklyGiftGold 本周累计收礼金币(分)
   * @return 匹配的政策
   */
  AnchorWeeklyPolicy findMatchingPolicy(Long weeklyGiftGold);

  /**
   * 获取当前周标识.
   *
   * @return 周标识 (格式: yyyy-ww)
   */
  String getCurrentWeek();

}
