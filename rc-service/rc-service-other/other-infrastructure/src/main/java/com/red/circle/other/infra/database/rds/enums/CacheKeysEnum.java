package com.red.circle.other.infra.database.rds.enums;

import java.util.Arrays;
import java.util.stream.Collectors;

/**
 * 缓存keys.
 *
 * <AUTHOR> on 2020/9/30
 * @since 2022/6/22  公用缓存key声明废弃，使用 enum implements RedisKeys 替换.
 */
@Deprecated
public enum CacheKeysEnum {
  /**
   * 短信验证码.
   */
  SMS_CODE("SMS_CODE"),

  /**
   * 修改国家.
   */
  UPDATE_COUNTRY_CODE("UPDATE_COUNTRY_CODE"),

  /**
   * 用户资料key.
   */
  USER_PROFILE("U"),

  /**
   * 申请好友.
   */
  APPLY_FRIEND("AF"),

  /**
   * 短账号.
   */
  USER_ACCOUNT_SHORT("USER_ACCOUNT_SHORT"),

  /**
   * 短账号-开发测试.
   */
  USER_ACCOUNT_SHORT_DEV("USER_ACCOUNT_SHORT_DEV"),

  /**
   * 礼物缓存信息.
   */
  GIFT_INFO("GIFT_INFO"),

  /**
   * 产品配置列表.
   */
  PRODUCT_CONFIGS("PRODUCT_CONFIGS"),

  /**
   * 枚举配置.
   */
  ENUM_CONFIG("ENUM_CONFIG_V2"),

  /**
   * 7日打卡.
   */
  SEVEN_CHECK_IN_V2("SEVEN_CHECK_IN_V2"),

  /**
   * 7日签到标记.
   */
  DAY_SEVEN_CHECK_IN_V2("DAY_SEVEN_CHECK_IN_V2"),

  /**
   * 每日打卡.
   */
  DAILY_CHECK_IN("DAILY_CHECK_IN"),

  /**
   * 订阅计数.
   */
  COUNTER_SUBSCRIPTION("COUNTER_SUBSCRIPTION"),

  /**
   * 粉丝计数.
   */
  COUNTER_FANS("COUNTER_FANS"),

  /**
   * 朋友计数.
   */
  COUNTER_FRIEND("COUNTER_FRIEND"),

  /**
   * 每日任务.
   */
  DAILY_TASK("DAILY_TASK"),

  /**
   * 每日任务-转盘.
   */
  DAILY_TASK_TURNTABLE("DAILY_TASK_TURNTABLE"),

  /**
   * 标记.
   */
  MARK("MARK"),

  /**
   * 水果游戏赚取金币.
   */
  SLOT_MACHINE_OBTAIN_GOLDS("SLOT_MACHINE_OBTAIN_GOLDS"),

  /**
   * 烧烤游戏赚取金币.
   */
  GAME_BARBECUE_OBTAIN_GOLDS("GAME_BARBECUE_OBTAIN_GOLDS"),

  /**
   * 用户在房间角色.
   */
  ROOM_ROLES("UR_ROLES"),

  /**
   * 周星倒计时.
   */
  WEEK_COUNT_DOWN("WEEK_COUNT_DOWN"),

  /**
   * 周星开始结束时间.
   */
  WEEK_STAR_START_END_DATE("WEEK_STAR_START_END_DATE"),

  /**
   * 国王/王后倒计时.
   */
  KQ_COUNT_DOWN("KQ_COUNT_DOWN"),

  /**
   * 国王/王后开始结束时间.
   */
  KQ_START_END_DATE("KQ_START_END_DATE"),

  /**
   * 每周用户金币消耗排行榜倒计时.
   */
  WEEK_GOLD_CONSUME_COUNT_DOWN("WEEK_GOLD_CONSUME_COUNT_DOWN"),

  /**
   * 每周用户金币消耗排行开始结束时间.
   */
  WEEK_GOLD_CONSUME_START_END_DATE("WEEK_GOLD_CONSUME_START_END_DATE"),

  /**
   * 锁.
   */
  LOCK("LOCK"),

  /**
   * 爆水晶机游戏.
   */
  GAME_BURST_CRYSTAL("GAME_BURST_CRYSTAL"),

  /**
   * 爆水晶机游戏累计.
   */
  GAME_BURST_CRYSTAL_INCR("GAME_BURST_CRYSTAL_INCR"),

  /**
   * 是否开奖.
   */
  GAME_BURST_CRYSTAL_PREHEAT("GAME_BURST_CRYSTAL_PREHEAT"),

  /**
   * 是否已开奖.
   */
  GAME_BURST_CRYSTAL_DRAW("GAME_BURST_CRYSTAL_DRAW"),

  /**
   * 是否初始化游戏.
   */
  GAME_BURST_CRYSTAL_INIT("GAME_BURST_CRYSTAL_INIT"),

  /**
   * 水晶游戏进度.
   */
  GAME_BURST_CRYSTAL_CONTRIBUTE("GAME_BURST_CRYSTAL_CONTRIBUTE"),

  /**
   * 这周TOP10.
   */
  THIS_WEEK_USER_TOP10("THIS_WEEK_USER_TOP10"),

  /**
   * 工会-奖励规则.
   */
  FAMILY_REWARD_RULE("FAMILY_REWARD_RULE"),

  /**
   * 工会-等级配置.
   */
  FAMILY_LEVEL_CONFIG("FAMILY_LEVEL_CONFIG"),

  /**
   * 房间数量.
   */
  ROOM_NUM("ROOM_NUM"),

  /**
   * 工会-工会短账号.
   */
  FAMILY_ACCOUNT_SHORT("FAMILY_ACCOUNT_SHORT"),

  /**
   * 工会-工会当前等级信息.
   */
  FAMILY_CURRENT_LEVEL_INFO("FAMILY_CURRENT_LEVEL_INFO"),

  /**
   * ludo游戏相关缓存.
   */
  GAME_LUDO("GAME_LUDO"),

  /**
   * 工会-所有等级.
   */
  FAMILY_ALL_LEVEL("FAMILY_ALL_LEVEL"),

  /**
   * 工会今日贡献值榜单.
   */
  FAMILY_NOW_RANKING("FAMILY_NOW_RANKING"),

  /**
   * 周奖励开始结束时间 周一 / 周日.
   */
  FAMILY_WEEK_REWARD_TIME("FAMILY_WEEK_REWARD_TIME"),

  /**
   * 表情.
   */
  SYS_EMOJI("SYS_EMOJI"),

  /**
   * 钻石兑换金币.
   */
  DIAMOND_EXCHANGE_GOLD("DIAMOND_EXCHANGE_GOLD"),

  /**
   * banner.
   */
  BANNER("BANNER"),

  /**
   * boos seat.
   */
  BOOS_SEAT("BOOS_SEAT"),

  /**
   * 正在pk中的双方对战信息.
   */
  ROOM_PK("ROOM_PK"),

  /**
   * 房间内团队pk基础信息.
   */
  INDOOR_TEAM_PK_BASE_INFO("INDOOR_TEAM_PK_BASE_INFO"),

  /**
   * 房间内团队pk送礼物计分.
   */
  INDOOR_TEAM_PK_GIFT_SCORE("INDOOR_TEAM_PK_GIFT_SCORE"),

  /**
   * 投票信息.
   */
  ROOM_VOTE("ROOM_VOTE"),

  /**
   * 标记用户是否已投票.
   */
  ROOM_VOTE_USER_SIGN("ROOM_VOTE_USER_SIGN"),

  /**
   * 标记用户投票记录id.
   */
  ROOM_VOTE_USER_OPTION_ID("ROOM_VOTE_USER_OPTION_ID"),

  /**
   * 上周PK信息.
   */
  ROOM_PK_LAST_WEEK("ROOM_PK_LAST_WEEK"),

  /**
   * 本周PK信息.
   */
  ROOM_PK_THIS_WEEK("ROOM_PK_THIS_WEEK"),

  /**
   * 活动抽奖配置.
   */
  ACTIVITY_LOTTERY("ACTIVITY_LOTTERY"),

  /**
   * 砸金蛋前20名.
   */
  GAME_EGG_TOP20("GAME_EGG_TOP20"),

  /**
   * 转盘游戏前20名.
   */
  GAME_TURNTABLE_TOP20("GAME_TURNTABLE_TOP20"),

  /**
   * 奖金池.
   */
  GAME_PRIZE_POOL("GAME_PRIZE_POOL"),

  /**
   * 消费奖金池要求
   */
  CONSUME_PRIZE_POOL("CONSUME_PRIZE_POOL"),

  /**
   * 游戏王活动赛局进度.
   */
  GAME_KING_ACTIVITY("GAME_KING_ACTIVITY"),

  /**
   * 游戏王比赛开始时间.
   */
  GAME_KING_START_TIME("GAME_KING_START_TIME"),

  /**
   * 游戏王历史记录.
   */
  GAME_KING_HISTORY("GAME_KING_HISTORY"),

  /**
   * 游戏王轮数.
   */
  GAME_KING_TODAY_ROUNDS("GAME_KING_TODAY_ROUNDS"),

  /**
   * 游戏王倒计时.
   */
  GAME_KING_COUNT_DOWN("GAME_KING_COUNT_DOWN"),

  /**
   * 游戏王比赛进程.
   */
  GAME_KING_PROGRESS("GAME_KING_PROGRESS"),

  /**
   * 投票游戏选项票数.
   */
  GAME_OPTION_VOTES("GAME_OPTION_VOTES"),

  /**
   * 货运代理大于1k用户列表.
   */
  FREIGHT_BALANCE_GT_1K_LIST("FREIGHT_BALANCE_GT_1K_LIST"),

  /**
   * 活动抽奖配置.
   */
  @Deprecated
  ACTIVITY_LOTTERY_CONFIG("ACTIVITY_LOTTERY_CONFIG"),

  /**
   * 上周专属礼物消耗榜单.
   */
  LAST_WEEK_EXCLUSIVE_LIST("LAST_WEEK_EXCLUSIVE_LIST"),

  /**
   * 本周专属礼物消耗榜单.
   */
  THIS_WEEK_EXCLUSIVE_LIST("THIS_WEEK_EXCLUSIVE_LIST"),

  /**
   * 请求黑名单.
   */
  REQUEST_BLACKLIST("REQUEST_BLACKLIST"),

  /**
   * 今天累计充值.
   */
  CUMULATIVE_RECHARGE_LOTTERY("CUMULATIVE_RECHARGE_LOTTERY"),

  /**
   * 活跃用户统计-主播.
   */
  ACTIVE_USER_ANCHOR_COUNT("ACTIVE_USER_ANCHOR_COUNT"),

  /**
   * 活跃用户统计-普通.
   */
  ACTIVE_USER_ORDINARY_COUNT("ACTIVE_USER_ORDINARY_COUNT"),

  /**
   * 置顶房间.
   */
  SET_TOP_ROOM("SET_TOP_ROOM"),

  /**
   * 动态-评论数量
   */
  DYNAMIC_COMMENT_QUANTITY("DYNAMIC_COMMENT_QUANTITY"),

  /**
   * 动态-点赞数量
   */
  DYNAMIC_LIKE_QUANTITY("DYNAMIC_LIKE_QUANTITY"),

  /**
   * 动态-点赞评论数量
   */
  DYNAMIC_LIKE_COMMENT_QUANTITY("DYNAMIC_LIKE_COMMENT_QUANTITY"),

  /**
   * 热门房间.
   */
  SET_HOT_ROOM("SET_HOT_ROOM"),

  /**
   * 获得组成cp时赠送的礼物.
   */
  PAIR_CP_GIVE_GIFT_ID("PAIR_CP_GIVE_GIFT_ID"),
  /**
   * 新运彩票.
   */
  LOTTERY_NUMBERS("LOTTERY_NUMBERS"),

  /**
   * 动态-标签.
   */
  DYNAMIC_TAG("DYNAMIC_TAG"),

  /**
   * 动态-置顶.
   */
  DYNAMIC_TOP("DYNAMIC_TOP"),

  /**
   * 动态-用户今日发送动态输.
   */
  DYNAMIC_USER_SEND_COUNT("DYNAMIC_USER_SEND_COUNT"),

  /**
   * 生成到新运数字号码.
   */
  LOTTERY_NUMBERS_GEN("LOTTERY_NUMBERS_GEN"),

  /**
   * 幸运数字奖金.
   */
  LOTTERY_NUMBERS_BONUS("LOTTERY_NUMBERS_BONUS"),

  /**
   * 购买新运数字用户.
   */
  LOTTERY_NUMBERS_USER("LOTTERY_NUMBERS_USER"),

  /**
   * 押注记录数.
   */
  LOTTERY_NUMBERS_BET_SIZE("LOTTERY_NUMBERS_BET_SIZE"),

  /**
   * 幸运数字.
   */
  LOTTERY_NUMBERS_BET_NUM("LOTTERY_NUMBERS_BET_NUM"),

  /**
   * 幸运数字倒计时.
   */
  LOTTERY_NUMBERS_BET_COUNT_DOWN("LOTTERY_NUMBERS_BET_COUNT_DOWN"),

  /**
   * 热门动态权重-点赞计分.
   */
  DYNAMIC_LIKE_WEIGHTS_SCORE("DYNAMIC_LIKE_WEIGHTS_SCORE"),

  /**
   * 热门动态权重-评论计分.
   */
  DYNAMIC_COMMENT_WEIGHTS_SCORE("DYNAMIC_COMMENT_WEIGHTS_SCORE"),

  /**
   * 财富、魅力等级多少级才能发布动态.
   */
  DYNAMIC_SEND_LEVEL_LIMIT("DYNAMIC_SEND_LEVEL_LIMIT"),

  /**
   * 每个用户当天允许发送多少条动态.
   */
  DYNAMIC_SEND_QUANTITY_LIMIT("DYNAMIC_SEND_QUANTITY_LIMIT"),

  /**
   * 用户当天发送动态数量达到限制,继续发送则每条动态支付多少金币？.
   */
  DYNAMIC_SEND_FEES("DYNAMIC_SEND_FEES"),

  /**
   * LuckyBox 收入.
   */
  @Deprecated
  LUCKY_BOX_INCOME("LUCKY_BOX_INCOME"),

  /**
   * LuckyBox 幸运榜单用户.
   */
  @Deprecated
  LUCKY_BOX_TOP_USER("LUCKY_BOX_TOP_USER"),

  /**
   * LuckyBox 支出.
   */
  LUCKY_BOX_EXPENDITURE("LUCKY_BOX_EXPENDITURE"),

  /**
   * 用户cp送礼物总额.
   */
  USER_CP_RANK_INTEGRAL("USER_CP_RANK_INTEGRAL"),

  /**
   * ASWAT 周年紀念日
   */
  ASWAT_MEMORIAL_DAY("ASWAT MEMORIAL DAY"),

  /**
   * 已领取邀请用户奖励集合.
   */
  INVITE_USER_REWARD_RECEIVED_LIST("INVITE_USER_REWARD_RECEIVED_LIST"),

  /**
   * 邀请用户佣金比例.
   */
  INVITE_USER_COMMISSION_RATIO("INVITE_USER_COMMISSION_RATIO"),

  /**
   * 邀请用户可获得佣金天数.
   */
  INVITE_USER_COMMISSION_DAYS("INVITE_USER_COMMISSION_DAYS"),

  /**
   * 累计足球活动用户累计充值.
   */
  INCR_SUPPORT_GOLDS("INCR_SUPPORT_GOLDS"),

  /**
   * 累计足球活动结果.
   */
  INCR_TEAM_SUPPORT_GOLDS("INCR_TEAM_SUPPORT_GOLDS"),

  /**
   * 系统用户友谊卡配置.
   */
  USER_FRIENDSHIP_CARD_CONFIG("USER_FRIENDSHIP_CARD_CONFIG"),

  /**
   * 是否有用户下注
   */
  GAME_USER_EXIST_BET("GAME_USER_EXIST_BET"),

  /**
   * 马甲信息
   */
  VEST_PROFILE("VEST_PROFILE"),

  /**
   * 游戏列表.
   */
  GAME_LIST("GAME_LIST"),

  /**
   * 团队新消息.
   */
  TEAM_NEW_NOTICE("TEAM_NEW_NOTICE"),

  /**
   * KTV等待倒计时.
   *  KTV等待倒计时.
   */
  GAME_KTV_WAIT_COUNT_DOWN("GAME_KTV_WAIT_COUNT_DOWN"),

  /**
   * KTV播放中歌曲剩余时间.
   */
  GAME_KTV_SONG_COUNT_DOWN("GAME_KTV_SONG_COUNT_DOWN"),

  /**
   * ktv歌曲.
   */
  KTV_SONG_CONFIG("KTV_SONG_CONFIG"),

  /**
   * ktv每日免费积分上限.
   */
  KTV_FREE_INTEGRAL_MAX("KTV_FREE_INTEGRAL_MAX"),

  /**
   * 周星周倒计时.
   */
  AGENT_ACTIVITY_WEEK_COUNT_DOWN("AGENT_ACTIVITY_WEEK_COUNT_DOWN"),

  /**
   * 周星月倒计时.
   */
  AGENT_ACTIVITY_MONTH_COUNT_DOWN("AGENT_ACTIVITY_MONTH_COUNT_DOWN"),

  /**
   * 累计重置倒计时.
   */
  CONSUME_ACTIVITY_COUNT_DOWN("CONSUME_ACTIVITY_COUNT_DOWN"),

  /**
   * 房间粉丝人气票活动倒计时.
   */
  ROOM_FAN_VOTES_ACTIVITY_IN_COUNT_DOWN("ROOM_FAN_VOTES_ACTIVITY_IN_COUNT_DOWN"),
  ROOM_FAN_VOTES_ACTIVITY_ID_COUNT_DOWN("ROOM_FAN_VOTES_ACTIVITY_ID_COUNT_DOWN"),
  ROOM_FAN_VOTES_ACTIVITY_AR_COUNT_DOWN("ROOM_FAN_VOTES_ACTIVITY_AR_COUNT_DOWN"),
  /**
   * CP点亮.
   */
  LIGHT_CP("LIGHT_CP"),
  /**
   * CP点亮.-指定礼物
   */
  LIGHT_CP_APPOINT("LIGHT_CP_APPOINT"),

  /**
   * 用户cp祝福值.
   */
  USER_CP_BLESS_VALUE("USER_CP_BLESS_VALUE"),

  /**
   * 红包邀新缓存.
   */
  RED_PACKET_INVITE_USER_CAROUSEL_AWARDS("RED_PACKET_INVITE_USER_CAROUSEL_AWARDS"),

  /**
   * 邀新配置配置信息.
   */
  RED_PACKET_INVITE_USER_CONFIG("RED_PACKET_INVITE_USER_CONFIG"),

  /**
   * 红包剩余奖项.
   */
  RED_PACKET_INVITE_AWARDS("RED_PACKET_INVITE_AWARDS"),

  /**
   * 红包剩余抽奖次数.
   */
  RED_PACKET_LOTTERY_COUNT("RED_PACKET_LOTTERY_COUNT"),

  /**
   * 今日助力用户.
   */
  RED_PACKET_TODAY_HELP_USERS("RED_PACKET_TODAY_HELP_USERS"),

  /**
   * 全局累计已抽奖次数.
   */
  RED_PACKET_ALL_INCR_LOTTERY_COUNT("RED_PACKET_ALL_INCR_LOTTERY_COUNT"),

  /**
   * 标记立即提现奖项.
   */
  RED_PACKET_WITHDRAW_PRIZES("RED_PACKET_WITHDRAW_PRIZES"),

  /**
   * 回归用户助力.
   */
  RED_PACKET_RETURN_USER_HELP("RED_PACKET_RETURN_USER_HELP"),

  /**
   * 用户成功收集红包的次数
   */
  COLLECT_THE_AMOUNT_OF_RED_PACKETS("COLLECT_THE_AMOUNT_OF_RED_PACKETS"),

  /**
   * 今天用户成功收集红包的次数
   */
  THE_NUMBER_OF_RED_ENVELOPES_COLLECTED_TODAY("THE_NUMBER_OF_RED_ENVELOPES_COLLECTED_TODAY")
  ;

  private final String prefix;

  CacheKeysEnum(String prefix) {
    this.prefix = prefix;
  }

  public String getKey() {
    return this.prefix;
  }

  public String getAppendKey(Long id) {
    return CacheKeyStructure.of(this.prefix, id).getCacheKey();
  }

  public String getAppendKey(String key) {
    return CacheKeyStructure.of(this.prefix, key).getCacheKey();
  }

  public String getAppendKey(String key, Long id) {
    return CacheKeyStructure.of(CacheKeyStructure.of(this.prefix, key).getCacheKey(), id)
        .getCacheKey();
  }

  public String getAppendKeyMultiple(Long... ids) {
    return CacheKeyStructure
        .of(this.prefix, Arrays.stream(ids).map(String::valueOf).collect(Collectors.joining("_")))
        .getCacheKey();
  }

  public String getAppendKeyMultipleSortedAsc(Long... ids) {
    return CacheKeyStructure.of(this.prefix,
            Arrays.stream(ids).sorted().map(String::valueOf).collect(Collectors.joining("_")))
        .getCacheKey();
  }
}
