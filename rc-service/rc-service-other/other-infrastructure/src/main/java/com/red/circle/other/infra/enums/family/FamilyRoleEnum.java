package com.red.circle.other.infra.enums.family;

/**
 * 工会角色.
 *
 * <AUTHOR>
 * @since 2021-07-20
 */
public enum FamilyRoleEnum {
  /**
   * 族长.
   */
  ADMIN("ADMIN", "族长"),
  /**
   * 管理员.
   */
  MANAGE("MANAGE", "管理员"),
  /**
   * 成员.
   */
  MEMBER("MEMBER", "成员");


  private final String key;
  private final String description;

  FamilyRoleEnum(String key, String description) {
    this.key = key;
    this.description = description;
  }

  public String getKey() {
    return key;
  }

  public String getDescription() {
    return description;
  }
}
