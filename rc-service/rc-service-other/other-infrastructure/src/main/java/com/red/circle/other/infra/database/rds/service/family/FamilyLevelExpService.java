package com.red.circle.other.infra.database.rds.service.family;


import com.red.circle.framework.mybatis.service.BaseService;
import com.red.circle.other.infra.database.rds.entity.family.FamilyLevelExp;
import java.math.BigDecimal;
import java.util.Map;
import java.util.Set;

/**
 * <p>
 * 工会等级经验 服务类.
 * </p>
 *
 * <AUTHOR>
 * @since 2022-08-04
 */
public interface FamilyLevelExpService extends BaseService<FamilyLevelExp> {


  /**
   * 根据工会id获得EXP-总经验.
   *
   * @param familyIds 工会ID
   * @return EXP
   */
  Map<Long, BigDecimal> mapTotalExpCountByFamilyIds(Set<Long> familyIds);

  /**
   * 累加.
   */
  void incrExp(Long familyId, Long familyLevelId, BigDecimal exp);

  /**
   * 获取经验.
   */
  BigDecimal getExp(Long familyId, Long familyLevelId);

  void deleteByFamilyId(Long familyId);
}
