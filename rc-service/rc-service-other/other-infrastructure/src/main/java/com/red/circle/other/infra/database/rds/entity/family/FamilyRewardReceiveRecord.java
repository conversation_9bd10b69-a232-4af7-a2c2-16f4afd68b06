package com.red.circle.other.infra.database.rds.entity.family;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.red.circle.framework.mybatis.entity.TimestampBaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * .
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-20
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("family_reward_receive_record")
public class FamilyRewardReceiveRecord extends TimestampBaseEntity {

  private static final long serialVersionUID = 1L;

  /**
   * 主键标识.
   */
  @TableId(value = "id", type = IdType.ASSIGN_ID)
  private Long id;

  /**
   * 来源系统.
   */
  @TableField("sys_origin")
  private String sysOrigin;

  /**
   * 工会ID.
   */
  @TableField("family_id")
  private Long familyId;

  /**
   * 工会成员表ID.
   */
  @TableField("family_member_id")
  private Long familyMemberId;

  /**
   * 奖励规则ID.
   */
  @TableField("reward_rule_id")
  private Long rewardRuleId;

  /**
   * 领取金额.
   */
  @TableField("receive_quantity")
  private Integer receiveQuantity;

}
