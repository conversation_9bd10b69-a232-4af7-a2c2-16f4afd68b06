package com.red.circle.other.infra.database.rds.service.family.impl;

import com.red.circle.framework.mybatis.constant.PageConstant;
import com.red.circle.framework.mybatis.service.impl.BaseServiceImpl;
import com.red.circle.other.infra.database.rds.dao.family.FamilyMemberWeekExpDAO;
import com.red.circle.other.infra.database.rds.dto.family.FamilyMemberExpDTO;
import com.red.circle.other.infra.database.rds.entity.family.FamilyMemberWeekExp;
import com.red.circle.other.infra.database.rds.service.family.FamilyMemberWeekExpService;
import com.red.circle.other.infra.enums.family.FamilyRoleEnum;
import com.red.circle.tool.core.collection.CollectionUtils;
import com.red.circle.tool.core.text.StringUtils;
import java.util.List;
import java.util.Objects;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 工会成员每周贡献值榜单(每周清空贡献值) 服务实现类.
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-21
 */
@Service
@RequiredArgsConstructor
public class FamilyMemberWeekExpServiceImpl extends
    BaseServiceImpl<FamilyMemberWeekExpDAO, FamilyMemberWeekExp> implements
    FamilyMemberWeekExpService {

  private final FamilyMemberWeekExpDAO familyMemberWeekExpDAO;

  @Override
  public void deleteBySysOriginMemberId(Long memberId, String sysOrigin) {
    if (Objects.isNull(memberId) || StringUtils.isBlank(sysOrigin)) {
      return;
    }
    delete().eq(FamilyMemberWeekExp::getFamilyMemberId, memberId).execute();
  }

  @Override
  public void incrExp(String sysOrigin, Long familyId, Long userId, Long exp) {

    if (StringUtils.isBlank(sysOrigin) ||
        Objects.isNull(familyId) ||
        Objects.isNull(userId) ||
        exp <= 0) {
      return;
    }
    FamilyMemberWeekExp familyMemberWeekExp = query()
        .select(FamilyMemberWeekExp::getId)
        .eq(FamilyMemberWeekExp::getSysOrigin, sysOrigin)
        .eq(FamilyMemberWeekExp::getFamilyId, familyId)
        .eq(FamilyMemberWeekExp::getFamilyMemberId, userId)
        .last(PageConstant.LIMIT_ONE)
        .getOne();

    if (Objects.isNull(familyMemberWeekExp) || Objects.isNull(familyMemberWeekExp.getId())) {
      save(new FamilyMemberWeekExp()
          .setSysOrigin(sysOrigin)
          .setFamilyId(familyId)
          .setFamilyMemberId(userId)
          .setExp(exp)
      );
      return;
    }

    update()
        .setSql("exp=exp+" + exp)
        .eq(FamilyMemberWeekExp::getId, familyMemberWeekExp.getId())
        .execute();
  }

  @Override
  public void saveOrUpdate(String sysOrigin, Long familyId, Long userId, Long exp) {
    if (StringUtils.isBlank(sysOrigin) ||
        Objects.isNull(familyId) ||
        Objects.isNull(userId) ||
        exp <= 0) {
      return;
    }
    FamilyMemberWeekExp familyMemberWeekExp = query()
        .eq(FamilyMemberWeekExp::getSysOrigin, sysOrigin)
        .eq(FamilyMemberWeekExp::getFamilyId, familyId)
        .eq(FamilyMemberWeekExp::getFamilyMemberId, userId)
        .last(PageConstant.LIMIT_ONE)
        .getOne();

    if (Objects.isNull(familyMemberWeekExp)) {
      save(new FamilyMemberWeekExp()
          .setSysOrigin(sysOrigin)
          .setFamilyId(familyId)
          .setFamilyMemberId(userId)
          .setExp(exp)
      );
      return;
    }
    familyMemberWeekExp.setExp(exp);
    updateSelectiveById(familyMemberWeekExp);
  }

  @Override
  public FamilyMemberWeekExp getFamilyMemberWeekExp(Long familyId, Long memberUserId) {
    if (Objects.isNull(familyId) || Objects.isNull(memberUserId)) {
      return null;
    }
    return query().eq(FamilyMemberWeekExp::getFamilyId, familyId)
        .eq(FamilyMemberWeekExp::getFamilyMemberId, memberUserId)
        .last(PageConstant.LIMIT_ONE)
        .getOne();
  }

  @Override
  public List<FamilyMemberExpDTO> pageByFamilyId(Long familyId, List<FamilyRoleEnum> roles,
      Integer pageNo, Integer pageSize) {
    if (Objects.isNull(familyId) || Objects.isNull(pageNo)) {
      return CollectionUtils.newArrayList();
    }
    pageSize = Objects.isNull(pageSize) ? PageConstant.DEFAULT_LIMIT_SIZE : pageSize;
    return familyMemberWeekExpDAO.pageByFamilyId(familyId, roles, pageNo * pageSize, pageSize);
  }


  @Override
  public void deleteByFamilyId(Long familyId) {
    if (Objects.isNull(familyId)) {
      return;
    }
    delete().eq(FamilyMemberWeekExp::getFamilyId, familyId).execute();
  }

  @Override
  public void deleteByFamilyIdByMemberId(Long familyId, Long memberId) {
    if (Objects.isNull(familyId) || Objects.isNull(memberId)) {
      return;
    }
    delete()
        .eq(FamilyMemberWeekExp::getFamilyId, familyId)
        .eq(FamilyMemberWeekExp::getFamilyMemberId, memberId)
        .execute();
  }

  @Override
  public List<FamilyMemberExpDTO> listByFamilyId(Long familyId, List<FamilyRoleEnum> roles) {
    if (Objects.isNull(familyId)) {
      return CollectionUtils.newArrayList();
    }
    return familyMemberWeekExpDAO.listByFamilyId(familyId, roles);
  }
}
