package com.red.circle.other.infra.database.cache.key;

import com.red.circle.component.redis.RedisKeys;

/**
 * 工会key.
 *
 * <AUTHOR> on 2023/6/6
 */
public enum FamilyKeys implements RedisKeys {

  /**
   * 工会-工会当前等级信息.
   */
  CURRENT_LEVEL_INFO,

  /**
   * 工会今日贡献值榜单.
   */
  NOW_RANKING,

  /**
   * 工会-所有等级.
   */
  ALL_LEVEL,
  /**
   * 周奖励开始结束时间 周一 / 周日.
   */
  FAMILY_WEEK_REWARD_TIME;

  @Override
  public String businessPrefix() {
    return "FAMILY";
  }

  @Override
  public String businessKey() {
    return this.name();
  }
}
