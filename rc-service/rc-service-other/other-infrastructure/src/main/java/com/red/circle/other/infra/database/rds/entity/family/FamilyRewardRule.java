package com.red.circle.other.infra.database.rds.entity.family;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.red.circle.framework.mybatis.entity.TimestampBaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 工会奖励规则表.
 * </p>
 *
 * <AUTHOR>
 * @since 2021-08-06
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("family_reward_rule")
public class FamilyRewardRule extends TimestampBaseEntity {

  private static final long serialVersionUID = 1L;

  /**
   * 主键标识.
   */
  @TableId("id")
  private Long id;

  /**
   * 来源系统.
   */
  @TableField("sys_origin")
  private String sysOrigin;

  /**
   * 要求经验值.
   */
  @TableField("goal_exp")
  private Integer goalExp;

  /**
   * 奖励金币数量.
   */
  @TableField("reward_quantity")
  private Integer rewardQuantity;

  /**
   * 规则描述.
   */
  @TableField("description")
  private String description;

  /**
   * 顺序.
   */
  @TableField("sort")
  private Integer sort;

}
