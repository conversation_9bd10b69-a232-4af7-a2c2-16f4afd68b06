package com.red.circle.other.infra.database.rds.entity.family;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.red.circle.framework.mybatis.entity.TimestampBaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 工会等级配置表.
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-20
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("family_level_config")
public class FamilyLevelConfig extends TimestampBaseEntity {

  private static final long serialVersionUID = 1L;

  /**
   * 主键标识.
   */
  @TableId(value = "id", type = IdType.ASSIGN_ID)
  private Long id;

  /**
   * 来源系统.
   */
  @TableField("sys_origin")
  private String sysOrigin;

  /**
   * 等级key.
   */
  @TableField("level_key")
  private String levelKey;

  /**
   * 头像框ID.
   */
  @TableField("avatar_frame_id")
  private Long avatarFrameId;

  /**
   * 徽章ID.
   */
  @TableField("badge_id")
  private Long badgeId;

  /**
   * 礼物ID.
   */
  @TableField("gift_id")
  private Long giftId;

  /**
   * 等级经验值.
   */
  @TableField("level_exp")
  private Integer levelExp;

  /**
   * 最大成员数.
   */
  @TableField("max_member")
  private Integer maxMember;

  /**
   * 最大管理员数.
   */
  @TableField("max_manager")
  private Integer maxManager;

  /**
   * 等级背景图.
   */
  @TableField("level_background_picture")
  private String levelBackgroundPicture;

  /**
   * 等级顺序.
   */
  @TableField("sort")
  private Integer sort;

  /**
   * 等级类型(BLACK_IRON.黑铁 BRONZE.青铜 SILVER.白银 GOLD.黄金 PLATINUM.铂金).
   */
  @TableField("level_type")
  private String levelType;

}
