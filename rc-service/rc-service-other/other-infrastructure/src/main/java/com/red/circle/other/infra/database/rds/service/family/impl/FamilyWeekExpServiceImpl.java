package com.red.circle.other.infra.database.rds.service.family.impl;

import com.red.circle.framework.mybatis.constant.PageConstant;
import com.red.circle.framework.mybatis.service.impl.BaseServiceImpl;
import com.red.circle.other.infra.database.rds.dao.family.FamilyWeekExpDAO;
import com.red.circle.other.infra.database.rds.entity.family.FamilyWeekExp;
import com.red.circle.other.infra.database.rds.service.family.FamilyWeekExpService;
import com.red.circle.tool.core.collection.CollectionUtils;
import com.red.circle.tool.core.date.LocalDateUtils;
import com.red.circle.tool.core.text.StringUtils;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 工会每周贡献值榜单 服务实现类.
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-21
 */
@Service
public class FamilyWeekExpServiceImpl extends
    BaseServiceImpl<FamilyWeekExpDAO, FamilyWeekExp> implements FamilyWeekExpService {

  @Override
  public void incrThisWeekExp(String sysOrigin, Long familyId, String regionId, Long exp) {

    if (StringUtils.isBlank(sysOrigin) || Objects.isNull(familyId) || exp <= 0) {
      return;
    }

    Integer dateSplit = LocalDateUtils.nowWeekMondayToInteger();

    FamilyWeekExp familyWeekExp = query()
        .select(FamilyWeekExp::getId)
        .eq(FamilyWeekExp::getFamilyId, familyId)
        .eq(FamilyWeekExp::getSysOrigin, sysOrigin)
        .eq(FamilyWeekExp::getDateSplit, dateSplit)
        .last(PageConstant.LIMIT_ONE)
        .getOne();

    if (Objects.isNull(familyWeekExp) || Objects.isNull(familyWeekExp.getId())) {
      super.save(new FamilyWeekExp()
          .setSysOrigin(sysOrigin)
          .setRegions(regionId)
          .setFamilyId(familyId)
          .setExp(exp)
          .setDateSplit(dateSplit)
      );
      return;
    }

    update()
        .setSql("exp=exp+" + exp)
        .eq(FamilyWeekExp::getId, familyWeekExp.getId())
        .execute();
  }

  @Override
  public List<FamilyWeekExp> listThisWeekBySysOrigin(String sysOrigin, String regionId) {
    return Optional.ofNullable(
            query()
                .eq(FamilyWeekExp::getSysOrigin, sysOrigin)
                .eq(FamilyWeekExp::getRegions, regionId)
                .eq(FamilyWeekExp::getDateSplit, LocalDateUtils.nowWeekMondayToInteger())
                .orderByDesc(FamilyWeekExp::getExp)
                .list()
        )
        .map(weeExp -> weeExp.stream().limit(20).collect(Collectors.toList()))
        .orElse(CollectionUtils.newArrayList());
  }

  @Override
  public FamilyWeekExp getThisWeekByFamilyId(Long familyId) {
    return Optional.ofNullable(query()
            .eq(FamilyWeekExp::getFamilyId, familyId)
            .eq(FamilyWeekExp::getDateSplit, LocalDateUtils.nowWeekMondayToInteger())
            .last(PageConstant.LIMIT_ONE)
            .getOne())
        .orElse(new FamilyWeekExp());
  }


  @Override
  public void deleteByFamilyId(Long familyId) {
    if (Objects.isNull(familyId)) {
      return;
    }
    delete().eq(FamilyWeekExp::getFamilyId, familyId).execute();
  }
}
