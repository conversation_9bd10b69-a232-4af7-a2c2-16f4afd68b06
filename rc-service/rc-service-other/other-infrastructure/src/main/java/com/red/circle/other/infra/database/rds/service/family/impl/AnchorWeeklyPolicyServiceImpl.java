package com.red.circle.other.infra.database.rds.service.family.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.red.circle.other.infra.database.rds.entity.family.AnchorWeeklyPolicy;
import com.red.circle.other.infra.database.rds.mapper.family.AnchorWeeklyPolicyMapper;
import com.red.circle.other.infra.database.rds.service.family.AnchorWeeklyPolicyService;
import java.util.List;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 主播周政策Service实现.
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class AnchorWeeklyPolicyServiceImpl extends ServiceImpl<AnchorWeeklyPolicyMapper, AnchorWeeklyPolicy>
    implements AnchorWeeklyPolicyService {

  @Override
  public List<AnchorWeeklyPolicy> listEnabledPoliciesOrderByPriority() {
    return lambdaQuery()
        .eq(AnchorWeeklyPolicy::getStatus, 1) // 启用状态
        .orderByDesc(AnchorWeeklyPolicy::getPriority) // 按优先级降序
        .orderByDesc(AnchorWeeklyPolicy::getGiftGoldThreshold) // 按门槛降序
        .list();
  }

  @Override
  public AnchorWeeklyPolicy findMatchingPolicy(Long giftGoldAmount) {
    if (giftGoldAmount == null || giftGoldAmount <= 0) {
      return null;
    }

    List<AnchorWeeklyPolicy> policies = listEnabledPoliciesOrderByPriority();
    
    // 从最高档位开始匹配，找到第一个满足条件的政策
    for (AnchorWeeklyPolicy policy : policies) {
      if (giftGoldAmount >= policy.getGiftGoldThreshold()) {
        log.debug("匹配到政策: {}, 收礼金币: {}, 门槛: {}", 
            policy.getPolicyName(), giftGoldAmount, policy.getGiftGoldThreshold());
        return policy;
      }
    }
    
    log.debug("未匹配到任何政策, 收礼金币: {}", giftGoldAmount);
    return null;
  }

}
