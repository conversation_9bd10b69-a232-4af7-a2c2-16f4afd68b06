package com.red.circle.other.infra.database.rds.service.family;


import com.red.circle.framework.mybatis.service.BaseService;
import com.red.circle.other.infra.database.rds.entity.family.FamilyBatterIntegralRecord;
import java.util.List;

/**
 * <p>
 * 工会batter积分表 服务类.
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-10
 */
public interface FamilyBatterIntegralRecordService extends BaseService<FamilyBatterIntegralRecord> {

  List<FamilyBatterIntegralRecord> listRanking(String sysOrigin);

}
