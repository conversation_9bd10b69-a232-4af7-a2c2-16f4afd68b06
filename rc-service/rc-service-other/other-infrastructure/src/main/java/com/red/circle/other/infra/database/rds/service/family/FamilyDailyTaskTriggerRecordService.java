package com.red.circle.other.infra.database.rds.service.family;


import com.red.circle.framework.mybatis.service.BaseService;
import com.red.circle.other.infra.database.rds.entity.family.FamilyDailyTaskTriggerRecord;
import java.util.List;

/**
 * <p>
 * 工会每日任务 服务类.
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-26
 */
public interface FamilyDailyTaskTriggerRecordService extends
    BaseService<FamilyDailyTaskTriggerRecord> {

  /**
   * 删除所有.
   */
  void deleteAll();

  List<FamilyDailyTaskTriggerRecord> listByCondition(FamilyDailyTaskTriggerRecord triggerRecord);

}
