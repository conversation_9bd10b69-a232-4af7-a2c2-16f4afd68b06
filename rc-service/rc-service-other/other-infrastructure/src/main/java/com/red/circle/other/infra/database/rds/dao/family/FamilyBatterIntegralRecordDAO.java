package com.red.circle.other.infra.database.rds.dao.family;


import com.red.circle.framework.mybatis.dao.BaseDAO;
import com.red.circle.other.infra.database.rds.entity.family.FamilyBatterIntegralRecord;
import java.util.List;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * 工会batter积分表 Mapper 接口.
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-10
 */
public interface FamilyBatterIntegralRecordDAO extends BaseDAO<FamilyBatterIntegralRecord> {

  List<FamilyBatterIntegralRecord> listRanking(@Param("sysOrigin") String sysOrigin);
}
