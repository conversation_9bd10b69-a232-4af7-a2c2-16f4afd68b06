package com.red.circle.other.infra.database.cache.service.other;

/**
 * <p>
 * 工会当前的最新的等级信息.
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-20
 */
public interface FamilyLevelCacheService {

  /**
   * 工会当前等级信息.
   *
   * @param key  系统平台+工会ID   例如： ASWAT135425255342232342
   * @param data 数据
   */
  void setFamilyLevelData(String key, Object data);

  /**
   * 工会当前等级信息.
   *
   * @param key   系统平台+工会ID    例如： ASWAT135425255342232342
   * @param clazz 类型
   * @param <T>   ignore
   * @return ignore
   */
  <T> T getFamilyLevelData(String key, Class<T> clazz);


  /**
   * 工会当前等级信息.
   *
   * @param sysOrigin 系统平台
   * @return ignore
   */
  String getFamilyLevelParent(String sysOrigin);

  /**
   * 工会当前等级信息.
   *
   * @param sysOrigin 系统平台
   * @param data      数据
   */
  void setFamilyLevelParent(String sysOrigin, String data);


}
