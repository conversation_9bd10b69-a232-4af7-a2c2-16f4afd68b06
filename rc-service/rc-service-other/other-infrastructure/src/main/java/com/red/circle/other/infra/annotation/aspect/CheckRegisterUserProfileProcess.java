package com.red.circle.other.infra.annotation.aspect;

import com.neovisionaries.i18n.CountryCode;
import com.red.circle.common.business.core.ImageSizeConst;
import com.red.circle.common.business.core.constant.KeywordConstant;
import com.red.circle.external.inner.endpoint.oss.OssServiceClient;
import com.red.circle.framework.core.asserts.ResponseAssert;
import com.red.circle.other.domain.model.user.UserConstant;
import com.red.circle.other.infra.database.rds.entity.sys.SysCountryCode;
import com.red.circle.other.infra.database.rds.service.sys.SysCountryCodeService;
import com.red.circle.tool.core.date.AgeUtils;
import com.red.circle.tool.core.text.StringUtils;
import com.red.circle.other.inner.model.cmd.user.UserProfileCmd;
import com.red.circle.other.inner.model.cmd.user.account.CreateAccountCmd;
import java.util.Objects;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

/**
 * 检查注册资料信息,核对矫正等.
 *
 * <AUTHOR> on 2020/11/5
 */
@Component
@RequiredArgsConstructor
public class CheckRegisterUserProfileProcess {

  private final OssServiceClient ossServiceClient;
  private final SysCountryCodeService sysCountryCodeService;

  public void process(CreateAccountCmd cmd) {
    UserProfileCmd profileCmd = cmd.getProfile();
    setUserCountryInfo(profileCmd);
    calculationAge(profileCmd);
    completeAvatarUrl(profileCmd);
    checkGenerateDefaultNickname(profileCmd);
  }

  private void checkGenerateDefaultNickname(UserProfileCmd cmd) {
    // 创建随机昵称
    if (StringUtils.isBlank(cmd.getUserNickname())) {
      cmd.setUserNickname(UserConstant.genRandomNickname());
      return;
    }
    // 过滤关键字
    cmd.setUserNickname(KeywordConstant.filter(cmd.getUserNickname()));
  }

  private void completeAvatarUrl(UserProfileCmd cmd) {
 //   if (StringUtils.isNotBlank(cmd.getUserAvatar())) {
//      cmd.setUserAvatar(ResponseAssert.requiredSuccess(
//          ossServiceClient.processImgSaveAsCompressZoom(cmd.getUserAvatar(),
//              ImageSizeConst.COVER_HEIGHT)
//      ));
  //  }
  }

  // 计算年龄
  private void calculationAge(UserProfileCmd cmd) {
    if (Objects.isNull(cmd.getAge())) {
      cmd.setAge(AgeUtils.getAge(cmd.getBornYear(), cmd.getBornMonth(), cmd.getBornDay()));
    }
  }

  // 是否存在国家code参数
  private void setUserCountryInfo(UserProfileCmd cmd) {

    // 存在国家id
    if (Objects.nonNull(cmd.getCountryId())) {
      SysCountryCode sysCountryCode = sysCountryCodeService.getById(cmd.getCountryId());

      if (Objects.nonNull(sysCountryCode)) {
        cmd.setCountryName(sysCountryCode.getCountryName());
        cmd.setCountryCode(sysCountryCode.getAlphaTwo());
        return;
      }
    }

    // 存在国家区域编码
    if (cmd.checkExistsCountryCode() || Objects.nonNull(cmd.getCountryCode())) {
      SysCountryCode sysCountryCode = sysCountryCodeService.getByCode(cmd.getCountryCode());

      if (Objects.nonNull(sysCountryCode)) {
        cmd.setCountryId(sysCountryCode.getId());
        cmd.setCountryName(sysCountryCode.getCountryName());
        return;
      }
    }

    // 没有获取到国家默认美国
    SysCountryCode sysCountryCode = sysCountryCodeService.getByCode(CountryCode.US.getAlpha2());
    if (Objects.nonNull(sysCountryCode)) {
      cmd.setCountryId(sysCountryCode.getId());
      cmd.setCountryName(sysCountryCode.getCountryName());
      cmd.setCountryCode(sysCountryCode.getAlphaTwo());
    }
  }

}
