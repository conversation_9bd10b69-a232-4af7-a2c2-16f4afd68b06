package com.red.circle.other.infra.database.rds.service.family;


import com.red.circle.framework.dto.PageResult;
import com.red.circle.framework.mybatis.service.BaseService;
import com.red.circle.other.infra.database.rds.entity.family.FamilyLevelConfig;
import com.red.circle.other.inner.model.cmd.famliy.FamilyLevelConfigQryCmd;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <p>
 * 工会等级配置表 服务类.
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-20
 */
public interface FamilyLevelConfigService extends BaseService<FamilyLevelConfig> {

  FamilyLevelConfig getFamilyLevelConfig(Long id);

  Map<Long, FamilyLevelConfig> mapByIds(Set<Long> ids);

  FamilyLevelConfig getIntiLevel(String sysOrigin);

  List<FamilyLevelConfig> listLevelBySysOrigin(String sysOrigin);

  List<FamilyLevelConfig> listLevelConfig(String sysOrigin);

  FamilyLevelConfig getNextLevel(String sysOrigin, Integer sort);

  List<FamilyLevelConfig> listLevelConfig();

  void saveFamilyLevelConfig(FamilyLevelConfig param);

  PageResult<FamilyLevelConfig> pageData(FamilyLevelConfigQryCmd queryWhere);

  List<FamilyLevelConfig> listAllLevel();

}
