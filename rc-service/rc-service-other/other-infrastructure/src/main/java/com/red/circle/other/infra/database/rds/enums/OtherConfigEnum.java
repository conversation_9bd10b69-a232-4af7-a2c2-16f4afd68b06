package com.red.circle.other.infra.database.rds.enums;


import com.red.circle.common.business.core.SysConfigEnum;

/**
 * 其他糖果相关的.
 *
 * <AUTHOR> on 2020/7/30
 */
public enum OtherConfigEnum implements SysConfigEnum {

  /**
   * 邀请用户注册..
   */
  INVITE_USER_REGISTER("Invite users to register"),

  /**
   * 举报成功补偿1颗.
   */
  @Deprecated
  VIDEO_VIOLATION_ONE("Report success"),

  /**
   * 自定义房间主题.
   */
  CUSTOM_ROOM_THEME("Custom room theme"),


  /**
   * 邀请新用户注册奖励.
   */
  INVITED_NEW_USER_REWARD("Invited new user reward"),

  /**
   * 邀请用户首次充值奖励.
   */
  INVITED_USER_FIRST_RECHARGE_REWARD("Invited user first recharge reward"),

  /**
   * 邀请用户每笔充值奖励百分比.
   */
  INVITED_USER_EACH_RECHARGE("Invited user each recharge"),

  /**
   * 钻石兑换倍数.
   */
  DIAMOND_EXCHANGE_MULTIPLE("Diamond exchange multiple"),

  /**
   * 钻石最大兑换金币.
   */
  DIAMOND_EXCHANGE_MAX_GOLD("Diamond exchange max gold"),

  /**
   * 颗钻石=金币
   */
  DIAMOND_EQ_GOLD("Diamond eq gold"),

  /**
   * 播报金额.
   */
  BROADCAST_AMOUNT("Announced amount"),

  /**
   * 房间锁金额.
   */
  ROOM_LOCK_AMOUNT("Room lock amount"),

  /**
   * 活动房间特殊麦位解锁金额
   */
  ROOM_MIKE_AMOUNT_ASWAT("Room Mike amount"),

  /**
   * 购买房间麦位数量较少解锁金额
   */
  ROOM_MIKE_NUM_MEDIUM_AMOUNT_ASWAT("Room Mike Number Medium amount"),

  /**
   * 购买房间麦位数量较多解锁金额
   */
  ROOM_MIKE_NUM_LARGE_AMOUNT_ASWAT("Room Mike Number Large amount"),

  /**
   * 隐身道具金额.
   */
  PROPS_STEALTH_BROWSE_AMOUNT("Stealth browse props amount"),

  /**
   * 好友在线显示金额.
   */
  PROPS_FRIEND_ONLINE_DISPLAY_AMOUNT("Friend online display props amount"),

  /**
   * 工会群聊付费解锁.
   */
  FAMILY_GROUP_CHAT_AMOUNT("Family group chat amount"),

  /**
   * 服务通知账号.
   *  SERVER_NOTICE_ACCOUNT("Server Notice account"),
   */

  /**
   * cp价格.
   */
  CP_PRICE("cp price"),

  /**
   * Aswat 超级管理员ID.
   */
  ASWAT_ADMINS("Aswat Super administrator ids"),

  /**
   * boos位置起步金额.
   */
  BOOS_STARTING_AMOUNT("Boos Starting amount"),

  /**
   * yahlla 豆子转金币汇率.
   */
  YAHLLA_BEN_TO_GOLD_RATE("Yahlla Bean to gold rate"),

  /**
   * 瓜分金币押注金额.
   */
  LOTTERY_NUMBER_BET("Divide gold coins"),

  /**
   * aswat水果游戏播报金额.
   */
  ASWAT_FRUIT_GAME_SWITCH("Aswat fruit game switch"),

  /**
   * 频道喇叭消费.
   */
  ROOM_SEND_TRUMPET("Send speaker"),

  /**
   * 发送频道喇叭消费要求等级.
   */
  ROOM_SEND_TRUMPET_LEVEL("Send trumpet required level"),

  /**
   * 高价值礼物墙要求金额.
   */
  HIGH_VALUE_GIFT_STANDARD("High Value Gift Wall Standard Requirements"),

  /**
   * 游戏飘窗金额.
   */
  GAME_BROADCAST_AMOUNT("Game Broadcast amount"),

  /**
   * aswat 个人红包抽成比例 %.
   */
  ASWAT_USER_SV_USER_RED_PACKET_RATIO("one-to-one bonus"),

  /**
   * yahlla 个人红包抽成比例 %.
   */
  YAHLLA_USER_SV_USER_RED_PACKET_RATIO("one-to-one bonus"),

  /**
   * 房间内发送红包数量
   */
  ASWAT_RED_PACKET_NUMBER("Red packet number"),

  /**
   * 世界红包金额范围
   */
  ASWAT_WORLD_RED_PACKET_AMOUNT("World red packet amount"),

  /**
   * 国家红包金额范围
   */
  ASWAT_COUNTRY_RED_PACKET_AMOUNT("Country red packet amount"),

  /**
   * 其他国家红包金额范围
   */
  ASWAT_OTHER_COUNTRY_RED_PACKET_AMOUNT("Other red packet amount"),

  /**
   * 幸运礼物飘窗倍数要求.
   */
  LUCKY_GIFT_POPUP_MULTIPLE("Lucky gift window multiple requirements"),

  /**
   * 幸运礼物飘窗倍数要求1.
   */
  LUCKY_GIFT_POPUP_MULTIPLE_ONE("Lucky gift window multiple requirements one"),

  /**
   * 幸运礼物飘窗倍数要求7.
   */
  LUCKY_GIFT_POPUP_MULTIPLE_TWO("Lucky gift window multiple requirements two"),

  /**
   * 幸运礼物飘窗倍数要求77.
   */
  LUCKY_GIFT_POPUP_MULTIPLE_THREE("Lucky gift window multiple requirements three"),

  /**
   * 幸运礼物飘窗倍数要求177.
   */
  LUCKY_GIFT_POPUP_MULTIPLE_FOUR("Lucky gift window multiple requirements four"),

  /**
   * 幸运礼物飘窗倍数要求777.
   */
  LUCKY_GIFT_POPUP_MULTIPLE_FIVE("Lucky gift window multiple requirements five"),


  /**
   * 经销商充值给货运代理最低金额.
   */
  ASWAT_DEALER_RECHARGE("Minimum amount for dealer recharge"),

  /**
   * 不发CP与代理奖励区域Code.
   */
  NOT_CP_AGENT_REWARD("Unable to obtain rewards"),

  /**
   * 接受普通礼物目标比例.
   */
  ACCEPT_ORDINARY_GIFT_TARGET_RATIO("Target proportion of receiving regular gifts"),

  /**
   * 禁止消费(true = 不能消费).
   */
  SUSPEND_CONSUMPTION("Suspend Consumption"),

  /**
   * 点亮cp爱心聊天数量
   */
  USER_CP_LOVE_NUMBER("Cp Number of Love"),

  /**
   * 是否允许Facebook，openId登录(true = 允许).
   */
  USER_LOGIN_BY_OPEN_ID_FACEBOOK("Suspend facebook openId logger"),

  /**
   * 房间内快捷入口-游戏ID.
   */
  ROOM_SHORTCUT_GAME_ID("room shortcut game id"),


  /**
   * 充值抽奖活动需充值金额才可获得一次抽奖机会.
   */
  USER_RECHARGE_DRAW_ACTIVITY("Recharge draw activity amount"),

  /**
   * 是否开启红包飘窗
   */
  WHETHER_TO_OPEN_THE_RED_ENVELOPE_BAY_WINDOW("Whether to open the red envelope bay window")

  ;

  private final String desc;

  OtherConfigEnum(String desc) {
    this.desc = desc;
  }

  @Override
  public String getKey() {
    return this.name();
  }

  @Override
  public String getDesc() {
    return this.desc;
  }
}
