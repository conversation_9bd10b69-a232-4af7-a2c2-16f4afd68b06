package com.red.circle.other.infra.database.rds.service.family;


import com.red.circle.framework.mybatis.service.BaseService;
import com.red.circle.other.infra.database.rds.entity.family.FamilyWeekExp;
import java.util.List;

/**
 * <p>
 * 工会每周贡献值榜单 服务类.
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-21
 */
public interface FamilyWeekExpService extends BaseService<FamilyWeekExp> {

  /**
   * 添加本周经验.
   */
  void incrThisWeekExp(String sysOrigin, Long familyId, String regionId, Long exp);

  /**
   * 获取平台本周经验.
   */
  List<FamilyWeekExp> listThisWeekBySysOrigin(String sysOrigin, String regionId);

  /**
   * 获取工会本周经验.
   */
  FamilyWeekExp getThisWeekByFamilyId(Long familyId);

  void deleteByFamilyId(Long familyId);
}
