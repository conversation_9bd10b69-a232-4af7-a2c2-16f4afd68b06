package com.red.circle.other.infra.database.rds.service.family.impl;


import com.alibaba.nacos.shaded.com.google.common.collect.Lists;
import com.red.circle.framework.core.asserts.ResponseAssert;
import com.red.circle.framework.dto.PageResult;
import com.red.circle.framework.mybatis.service.impl.BaseServiceImpl;
import com.red.circle.other.infra.database.rds.dao.family.FamilyRewardRuleDAO;
import com.red.circle.other.infra.database.rds.entity.family.FamilyRewardRule;
import com.red.circle.other.infra.database.rds.service.family.FamilyRewardRuleService;
import com.red.circle.other.inner.asserts.FamilyErrorCode;
import com.red.circle.other.inner.model.cmd.famliy.FamilyRewardRuleQryCmd;
import com.red.circle.tool.core.collection.CollectionUtils;
import com.red.circle.tool.core.text.StringUtils;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 工会奖励规则表 服务实现类.
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-20
 */
@Service
public class FamilyRewardRuleServiceImpl extends
    BaseServiceImpl<FamilyRewardRuleDAO, FamilyRewardRule> implements FamilyRewardRuleService {

  @Override
  public List<FamilyRewardRule> listBySysOrigin(String sysOrigin) {
    if (StringUtils.isBlank(sysOrigin)) {
      return CollectionUtils.newArrayList();
    }
    return query()
        .eq(FamilyRewardRule::getSysOrigin, sysOrigin)
        .orderByAsc(FamilyRewardRule::getSort)
        .list();
  }

  @Override
  public FamilyRewardRule getRuleById(Long id) {
    if (Objects.isNull(id)) {
      return new FamilyRewardRule();
    }
    return query().eq(FamilyRewardRule::getId, id).getOne();
  }


  @Override
  public void saveFamilyRewardRule(FamilyRewardRule familyRewardRule) {
    if (Objects.isNull(familyRewardRule.getId())) {

      List<FamilyRewardRule> rules = getRules(familyRewardRule);
      ResponseAssert.isFalse(FamilyErrorCode.UP_TO_THREE_RULES, rules.size() >= 3);
      save(familyRewardRule);
      return;
    }

    updateSelectiveById(familyRewardRule);

  }

  @Override
  public PageResult<FamilyRewardRule> pageData(FamilyRewardRuleQryCmd queryWhere) {

    return query()
        .eq(Objects.nonNull(queryWhere.getId()), FamilyRewardRule::getId, queryWhere.getId())
        .eq(com.baomidou.mybatisplus.core.toolkit.StringUtils.isNotEmpty(queryWhere.getSysOrigin()),
            FamilyRewardRule::getSysOrigin,
            queryWhere.getSysOrigin())
        .orderByAsc(FamilyRewardRule::getSort)
        .page(queryWhere.getPageQuery());
  }

  private List<FamilyRewardRule> getRules(FamilyRewardRule familyRewardRule) {
    return Optional.ofNullable(query()
        .eq(FamilyRewardRule::getSysOrigin, familyRewardRule.getSysOrigin())
        .list()).orElse(Lists.newArrayList());
  }


}
