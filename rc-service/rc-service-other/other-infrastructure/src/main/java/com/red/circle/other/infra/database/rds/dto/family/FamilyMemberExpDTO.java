package com.red.circle.other.infra.database.rds.dto.family;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import java.io.Serializable;
import java.math.BigDecimal;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR> on 2022/8/8
 */
@Data
@Accessors(chain = true)
public class FamilyMemberExpDTO implements Serializable {

  private static final long serialVersionUID = 1L;

  /**
   * 主键标识.
   */
  @JsonSerialize(using = ToStringSerializer.class)
  private Long id;

  /**
   * 来源系统.
   */
  private String sysOrigin;

  /**
   * 工会ID.
   */
  @JsonSerialize(using = ToStringSerializer.class)
  private Long familyId;

  /**
   * 工会成员userId.
   */
  @JsonSerialize(using = ToStringSerializer.class)
  private Long memberUserId;

  /**
   * 角色.
   */
  private String memberRole;

  /**
   * 经验.
   */
  private BigDecimal exp;

}
