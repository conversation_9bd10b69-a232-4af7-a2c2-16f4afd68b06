package com.red.circle.other.infra.database.rds.service.family;


import com.red.circle.framework.mybatis.service.BaseService;
import com.red.circle.other.infra.database.rds.entity.family.FamilyMessage;
import java.util.List;

/**
 * <p>
 * 工会消息表 服务类.
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-23
 */
public interface FamilyMessageService extends BaseService<FamilyMessage> {

  List<FamilyMessage> listUntreatedById(Long familyId, Long lastId);

  Boolean isExistApplyingMsg(Long familyId, Long userId);

  void deleteByFamilyId(Long familyId);

  void deleteByFamilyIdByUserId(Long familyId, Long memberUserId);
}
