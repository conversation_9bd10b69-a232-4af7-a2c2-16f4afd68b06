package com.red.circle.other.infra.database.cache.service.other.impl;

import com.red.circle.component.redis.service.RedisService;
import com.red.circle.other.infra.database.cache.key.FamilyKeys;
import com.red.circle.other.infra.database.cache.service.other.FamilyCacheService;
import java.util.concurrent.TimeUnit;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * 工会.
 *
 * <AUTHOR> on 2021/8/9
 */
@Service
@RequiredArgsConstructor
public class FamilyCacheServiceImpl implements FamilyCacheService {

  private final RedisService redisService;

  @Override
  public String getFamilyWeekRewardTime() {
    return redisService.getString(FamilyKeys.FAMILY_WEEK_REWARD_TIME.businessKey());
  }

  @Override
  public void setFamilyWeekRewardTime(String dateStr) {
    redisService.setString(FamilyKeys.FAMILY_WEEK_REWARD_TIME.businessKey(), dateStr, 7,
        TimeUnit.DAYS);
  }

}
