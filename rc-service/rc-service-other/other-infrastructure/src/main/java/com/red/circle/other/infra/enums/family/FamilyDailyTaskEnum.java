package com.red.circle.other.infra.enums.family;

/**
 * 工会每日任务.
 *
 * <AUTHOR>
 * @since 2021-07-26
 */
public enum FamilyDailyTaskEnum {
  /**
   * 每天登录一次.
   */
  DAILY_LOGIN("DAILY_LOGIN", 20L, 1, "每天登录"),
  /**
   * 每天看直播十分钟.
   */
  WATCH_LIVE_TEN_MIN("WATCH_LIVE_TEN_MIN", 50L, 5, "每天看直播十分钟"),
  /**
   * 每天上麦十分钟.
   */
  MICROPHONE_TEN_MIN("MICROPHONE_TEN_MIN", 150L, 5, "每天上麦十分钟");

  /**
   * 任务类型Key
   */
  private final String key;
  /**
   * 经验值
   */
  private final Long exp;
  /**
   * 每天有效触发次数
   */
  private final Integer opportunity;
  /**
   * 描述
   */
  private final String description;

  FamilyDailyTaskEnum(String key, Long exp, Integer opportunity, String description) {
    this.key = key;
    this.exp = exp;
    this.opportunity = opportunity;
    this.description = description;
  }

  public String getKey() {
    return key;
  }

  public String getDescription() {
    return description;
  }

  public Long getExp() {
    return exp;
  }

  public Integer getOpportunity() {
    return opportunity;
  }
}
