package com.red.circle.other.infra.database.rds.service.family;


import com.red.circle.framework.mybatis.service.BaseService;
import com.red.circle.other.infra.database.rds.entity.family.FamilyMemberWeekAwardRecord;
import java.util.List;

/**
 * <p>
 * 工会成员每周领奖记录(每周清空记录值).
 * </p>
 *
 * <AUTHOR>
 * @since 2021-08-06
 */
public interface FamilyMemberWeekAwardRecordService extends
    BaseService<FamilyMemberWeekAwardRecord> {

  List<FamilyMemberWeekAwardRecord> listByFamily(Long familyId, Long memberId);

  Boolean isReceived(Long familyId, Long memberId, Long rewardRuleId);

  void deleteByFamilyId(Long familyId);

  void deleteByFamilyIdByMemberId(Long familyId, Long id);
}
