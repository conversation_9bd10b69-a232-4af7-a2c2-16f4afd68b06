package com.red.circle.other.infra.database.rds.entity.family;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.red.circle.framework.mybatis.entity.TimestampBaseEntity;
import java.io.Serial;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 工会群聊付费解锁记录.
 * </p>
 *
 * <AUTHOR> on 2023-12-21 17:56
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@TableName("family_group_chat_pay_record")
public class FamilyGroupChatPayRecord extends TimestampBaseEntity {

  @Serial
  private static final long serialVersionUID = 1L;

  /**
   * 主键标识.
   */
  @TableId(value = "id", type = IdType.ASSIGN_ID)
  private Long id;

  /**
   * 工会id.
   */
  @TableField("family_id")
  private Long familyId;

  /**
   * 用户ID.
   */
  @TableField("user_id")
  private Long userId;

  /**
   * 群聊组状态(true正常, false已解散).
   */
  @TableField("group_state")
  private Boolean groupState;

}
