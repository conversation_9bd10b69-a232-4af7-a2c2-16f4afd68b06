package com.red.circle.other.infra.database.mongo.service.live;

import com.red.circle.common.business.core.enums.SysOriginPlatformEnum;
import com.red.circle.other.infra.database.mongo.entity.live.ActiveVoiceRoom;
import com.red.circle.other.inner.model.cmd.live.AppActiveVoiceRoomQryCmd;
import java.util.List;

/**
 * 活跃语音房间.
 *
 * <AUTHOR> on 2020/12/11
 */
public interface ActiveVoiceRoomService {

  /**
   * 添加房间在线用户数量.
   *
   * @param param 参数
   */
  void saveOrUpdate(ActiveVoiceRoom param);

  /**
   * 是否存在.
   *
   * @param roomAccount 房间account
   * @return true 存在，false 不存在
   */
  boolean existsByRoomAccount(String roomAccount);

  /**
   * 是否存在.
   *
   * @param roomId 房间id
   * @return true 存在，false 不存在
   */
  boolean existsByRoomId(Long roomId);

  /**
   * 修改数量.
   *
   * @param roomAccount 房间账号
   * @param quantity    数量
   */
  void updateQuantityByRoomAccount(String roomAccount, Long quantity);

  /**
   * 移除首页房间.
   *
   * @param ids 用户id
   */
  void removeByIds(List<Long> ids);

  /**
   * 移除房间.
   *
   * @param roomAccount 房间account
   */
  void removeByRoomAccount(String roomAccount);

  /**
   * 获取活跃房间.
   */
  List<ActiveVoiceRoom> listOps(AppActiveVoiceRoomQryCmd query);

  /**
   * 获取发现页数据.
   */
  List<ActiveVoiceRoom> listDiscover(String sysOrigin, String region, Integer limit);

  /**
   * 获取区域房间.
   */
  List<ActiveVoiceRoom> listSearchCountryRoom(String sysOrigin, String countryCode, Integer limit);

  /**
   * 获取工会在线房间.
   */
  List<ActiveVoiceRoom> listByFamilyId(Long familyId, Integer limit);

  /**
   * 统计.
   */
  Long count(String sysOrigin);

  /**
   * 获取活跃房间,排除自己国家的
   */
  List<ActiveVoiceRoom> listExcludeCountry(AppActiveVoiceRoomQryCmd query);


  List<ActiveVoiceRoom> listGetActiveRooms(List<Long> roomIds,String sysOrigin,Integer onlineQuantity);


}
