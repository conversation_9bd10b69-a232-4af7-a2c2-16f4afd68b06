package com.red.circle.other.infra.database.cache.key;

import com.red.circle.component.redis.RedisKeys;

/**
 * 工会周统计Redis Key.
 *
 * <AUTHOR>
 */
public enum FamilyWeeklyKey implements RedisKeys {

  /**
   * 用户周收礼统计.
   * Key格式: FAMILY:WEEKLY:STATS:{familyId}:{userId}:{week}
   */
  WEEKLY_STATS,

  /**
   * 政策缓存.
   * Key格式: FAMILY:POLICY:CACHE:{week}
   */
  POLICY_CACHE;

  @Override
  public String businessPrefix() {
    return "FAMILY";
  }

  @Override
  public String businessKey() {
    return this.name();
  }

}
