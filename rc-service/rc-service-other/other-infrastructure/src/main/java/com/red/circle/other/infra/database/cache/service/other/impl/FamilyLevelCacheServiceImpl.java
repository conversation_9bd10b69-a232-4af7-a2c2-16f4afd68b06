package com.red.circle.other.infra.database.cache.service.other.impl;

import com.red.circle.component.redis.service.RedisService;
import com.red.circle.other.infra.database.cache.key.FamilyKeys;
import com.red.circle.other.infra.database.cache.service.other.FamilyLevelCacheService;
import java.util.concurrent.TimeUnit;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * 工会当前的最新的等级信息.
 *
 * <AUTHOR> on 2021/5/18
 */
@Service
@RequiredArgsConstructor
public class FamilyLevelCacheServiceImpl implements FamilyLevelCacheService {

  private final RedisService redisService;

  @Override
  public void setFamilyLevelData(String key, Object data) {
    redisService.setString(FamilyKeys.CURRENT_LEVEL_INFO.getKey(key),
        data,
        7,
        TimeUnit.DAYS);
  }

  @Override
  public <T> T getFamilyLevelData(String key, Class<T> clazz) {
    return redisService
        .getStringToObject(FamilyKeys.CURRENT_LEVEL_INFO.getKey(key), clazz);
  }

  @Override
  public String getFamilyLevelParent(String sysOrigin) {
    return redisService
        .getString(FamilyKeys.ALL_LEVEL.getKey(sysOrigin));
  }

  @Override
  public void setFamilyLevelParent(String sysOrigin, String data) {
    redisService.setString(FamilyKeys.ALL_LEVEL.getKey(sysOrigin),
        data,
        7,
        TimeUnit.DAYS);
  }

}
