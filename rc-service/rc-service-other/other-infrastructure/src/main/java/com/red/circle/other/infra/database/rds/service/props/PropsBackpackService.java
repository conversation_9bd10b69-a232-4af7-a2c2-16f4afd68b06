package com.red.circle.other.infra.database.rds.service.props;

import com.red.circle.framework.mybatis.service.BaseService;
import com.red.circle.other.infra.database.rds.entity.props.PropsBackpack;
import com.red.circle.other.inner.enums.material.PropsCommodityType;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <p>
 * 房间用户道具 服务类.
 * </p>
 *
 * <AUTHOR>
 * @since 2021-01-22
 */
public interface PropsBackpackService extends BaseService<PropsBackpack> {

  /**
   * 移除工会道具.
   */
  void deleteProps(Collection<Long> userIds, Collection<Long> propsIds,
      PropsCommodityType propsType);


  /**
   * 删除背包道具.
   *
   * @param userId   用户id
   * @param propsIds 徽章id
   */
  void deleteProps(Long userId, Collection<Long> propsIds);

  /**
   * 移除用户徽章.
   *
   * @param userIds  用户id
   * @param propsIds 徽章id
   */
  void deleteByUserIdsAndPropsIds(Collection<Long> userIds, Collection<Long> propsIds);

  /**
   * 存在正在使用且没有过期的道具.
   *
   * @param userId    用户id
   * @param propsType 类型
   * @return true 存在，false 不存在
   */
  boolean existsUseNotExpiredProps(Long userId, PropsCommodityType propsType);

  /**
   * 用户是否存在没有过期的指定类型道具.
   *
   * @param userId    用户id
   * @param propsType 道具类型
   * @return true 存在， false 不存在
   */
  boolean existsNotExpiredProps(Long userId, PropsCommodityType propsType);


  /**
   * 获取用户没有过期的vip.
   *
   * @param userIds 用户ids
   * @return ignore
   */
  Map<Long, List<PropsBackpack>> mapUserNotExpiredNobleVip(Set<Long> userIds);

  /**
   * 获取未过期的指定道具类型.
   *
   * @param userId 用户id
   * @param type   类型
   * @return ignore
   */
  List<PropsBackpack> listNotExpiredByType(Long userId, PropsCommodityType type);

  /**
   * 获取佩戴的未过期道具.
   *
   * @param userId 用户id
   * @param type   道具类型
   * @return ignore
   */
  PropsBackpack getUserNotExpiredUseProps(Long userId, PropsCommodityType type);

  /**
   * 获取用户资源信息.
   *
   * @param userId  用户id
   * @param propsId 资源id
   * @param type    道具类型
   * @return ignore
   */
  PropsBackpack getUserProps(Long userId, Long propsId, PropsCommodityType type);

  /**
   * 获取用户指定类型道具未过期列表.
   *
   * @param userId 用户id
   * @param type   类型
   * @return ignore
   */
  List<PropsBackpack> listNotExpiredByUserIdAndType(Long userId, PropsCommodityType type);

  /**
   * 切换道具使用.
   *
   * @param userId  用户id
   * @param propsId 道具id
   * @param type    类型
   */
  void switchUseProps(Long userId, Long propsId, PropsCommodityType type);

  /**
   * 卸下道具用户指定类型道具.
   *
   * @param userId 用户id
   * @param type   道具id
   */
  void unloadUseProps(Long userId, PropsCommodityType type);

  /**
   * 获取正在使用道具id.
   *
   * @param userId 用户id
   * @return id
   */
  List<PropsBackpack> listNotExpiredUseProps(Long userId);

  /**
   * 获取一组正在使用道具信息.
   */
  List<PropsBackpack> listNotExpiredUseProps(Set<Long> userIds);

  /**
   * 获取一组正在使用道具信息.
   *
   * @param userIds 用户id集合
   * @param types   类型
   * @return ignore
   */
  List<PropsBackpack> listNotExpiredUseProps(Set<Long> userIds, List<PropsCommodityType> types);

  /**
   * 添加背包.
   *
   * @param propsBackpack 背包信息
   */
  void addPropsBackpack(PropsBackpack propsBackpack);

  /**
   * 删除背包道具.
   *
   * @param propsId 道具ID
   * @param userId  用户ID
   */
  void deleteByPropsId(Long propsId, Long userId);

  /**
   * 获得用户道具.
   *
   * @param userId  用户id
   * @param propsId 道具类型
   * @return ignore
   */
  PropsBackpack getUserPropsByUserIdByPropsId(Long userId, Long propsId);

  /**
   * 获取一个正在使用道具id.
   *
   * @param userId 用户id
   * @param type   道具类型
   * @return id
   */
  Long getUsePropsId(Long userId, PropsCommodityType type);


  /**
   * 将一组用户道具到期时间减少指定天数，并且将其卸下.
   *
   * @param userId     用户.
   * @param propsIds   道具ids
   * @param reduceDays 将有效时间减少*天.
   */
  void reduceDaysAndUnUse(Long userId, Set<Long> propsIds, Integer reduceDays);

  /**
   * 获得同时佩戴超过一个有效头像的用户id.
   */
  Set<Long> getWearMultipleAvatarFrameUserIds();

  /**
   * 用户赠送道具
   *
   * @param acceptUserId 接收用户id
   * @param userId       用户id
   * @param propsId      道具id
   * @param type         类型
   */
  void giveAwayProps(Long acceptUserId, Long userId, Long propsId, String type);

  /**
   * 统计用户贵族道具数量.
   *
   * @param userId 用户.
   */
  Boolean countUserNobleProps(Long userId);
}
