package com.red.circle.other.infra.database.rds.service.family.impl;

import com.red.circle.framework.mybatis.constant.PageConstant;
import com.red.circle.framework.mybatis.service.impl.BaseServiceImpl;
import com.red.circle.other.infra.database.rds.dao.family.FamilyGroupChatPayRecordDAO;
import com.red.circle.other.infra.database.rds.entity.family.FamilyGroupChatPayRecord;
import com.red.circle.other.infra.database.rds.service.family.FamilyGroupChatPayRecordService;
import com.red.circle.tool.core.date.TimestampUtils;
import java.util.Objects;
import java.util.Optional;
import org.springframework.stereotype.Service;


/**
 * <p>
 * 工会群聊付费解锁记录 服务实现类.
 * </p>
 *
 * <AUTHOR> on 2023-12-21 17:56
 */
@Service
public class FamilyGroupChatPayRecordServiceImpl extends
    BaseServiceImpl<FamilyGroupChatPayRecordDAO, FamilyGroupChatPayRecord> implements
    FamilyGroupChatPayRecordService {

  @Override
  public Boolean getByFamilyIdId(Long familyId) {
    return Optional.ofNullable(
            query()
                .eq(FamilyGroupChatPayRecord::getFamilyId, familyId)
                .eq(FamilyGroupChatPayRecord::getGroupState, Boolean.TRUE)
                .last(PageConstant.LIMIT_ONE)
                .getOne()
        )
        .map(obj -> Objects.nonNull(obj.getId())).orElse(Boolean.FALSE);
  }

  @Override
  public void dismissGroupByFamilyId(Long familyId) {

    update()
        .set(FamilyGroupChatPayRecord::getGroupState, Boolean.FALSE)
        .set(FamilyGroupChatPayRecord::getUpdateTime, TimestampUtils.now())
        .eq(FamilyGroupChatPayRecord::getFamilyId, familyId)
        .eq(FamilyGroupChatPayRecord::getGroupState, Boolean.TRUE)
        .execute();
  }

}
