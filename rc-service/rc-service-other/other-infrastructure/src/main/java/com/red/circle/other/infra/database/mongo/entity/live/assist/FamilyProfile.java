package com.red.circle.other.infra.database.mongo.entity.live.assist;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import java.io.Serializable;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.Accessors;
import lombok.experimental.FieldDefaults;

/**
 * 工会资料.
 *
 * <AUTHOR> on 2022/11/11
 */
@Data
@Accessors(chain = true)
@FieldDefaults(level = AccessLevel.PRIVATE)
public class FamilyProfile implements Serializable {

  private static final long serialVersionUID = 1L;

  /**
   * 工会ID.
   */
  @JsonSerialize(using = ToStringSerializer.class)
  Long id;

  /**
   * 工会account.
   */
  Long familyAccount;


  /**
   * 工会名称.
   */
  String familyName;

  /**
   * 工会头像.
   */
  String familyAvatar;

  /**
   * 当前工会徽章.
   */
  String badgeCover;

  /**
   * 当前工会徽章资源图.
   */
  String badgeSource;

}
