package com.red.circle.other.infra.database.rds.service.family.impl;

import com.red.circle.framework.mybatis.service.impl.BaseServiceImpl;
import com.red.circle.other.infra.database.rds.dao.family.FamilyBatterIntegralRecordDAO;
import com.red.circle.other.infra.database.rds.entity.family.FamilyBatterIntegralRecord;
import com.red.circle.other.infra.database.rds.service.family.FamilyBatterIntegralRecordService;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 工会经验值增加记录表 服务实现类.
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-20
 */
@Service
@RequiredArgsConstructor
public class FamilyBatterIntegralRecordServiceImpl extends
    BaseServiceImpl<FamilyBatterIntegralRecordDAO, FamilyBatterIntegralRecord> implements
    FamilyBatterIntegralRecordService {

  private final FamilyBatterIntegralRecordDAO familyBatterIntegralRecordDAO;

  @Override
  public List<FamilyBatterIntegralRecord> listRanking(String sysOrigin) {
    return familyBatterIntegralRecordDAO.listRanking(sysOrigin);
  }
}
