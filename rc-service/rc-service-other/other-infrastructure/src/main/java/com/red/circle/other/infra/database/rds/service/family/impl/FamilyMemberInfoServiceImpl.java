package com.red.circle.other.infra.database.rds.service.family.impl;

import com.google.common.collect.Maps;
import com.red.circle.framework.dto.PageResult;
import com.red.circle.framework.mybatis.constant.PageConstant;
import com.red.circle.framework.mybatis.service.impl.BaseServiceImpl;
import com.red.circle.other.infra.database.rds.dao.family.FamilyMemberInfoDAO;
import com.red.circle.other.infra.database.rds.entity.family.FamilyMemberInfo;
import com.red.circle.other.infra.database.rds.service.family.FamilyMemberInfoService;
import com.red.circle.other.infra.enums.family.FamilyRoleEnum;
import com.red.circle.other.inner.model.cmd.famliy.FamilyMemberQryCmd;
import com.red.circle.tool.core.collection.CollectionUtils;
import com.red.circle.tool.core.text.StringUtils;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 工会成员表 服务实现类.
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-20
 */
@Service
public class FamilyMemberInfoServiceImpl extends
    BaseServiceImpl<FamilyMemberInfoDAO, FamilyMemberInfo> implements FamilyMemberInfoService {

  @Override
  public Long getFamilyIdByUserId(Long userId) {
    return Optional.ofNullable(query().select(FamilyMemberInfo::getFamilyId)
            .eq(FamilyMemberInfo::getMemberUserId, userId)
            .last(PageConstant.LIMIT_ONE)
            .getOne())
        .map(FamilyMemberInfo::getFamilyId)
        .orElse(null);
  }

  @Override
  public void deleteByFamilyId(Long familyId) {
    delete().eq(FamilyMemberInfo::getFamilyId, familyId).execute();
  }

  @Override
  public void deleteMemberById(FamilyMemberInfo familyMember) {
    delete().eq(FamilyMemberInfo::getId, familyMember.getId()).execute();
  }


  @Override
  public PageResult<FamilyMemberInfo> pageData(FamilyMemberQryCmd queryWhere) {
    return query()
        .eq(Objects.nonNull(queryWhere.getUserId()), FamilyMemberInfo::getMemberUserId,
            queryWhere.getUserId())
        .eq(Objects.nonNull(queryWhere.getFamilyId()), FamilyMemberInfo::getFamilyId,
            queryWhere.getFamilyId())
        .orderByDesc(FamilyMemberInfo::getCreateTime)
        .page(queryWhere.getPageQuery());
  }

  @Override
  public FamilyMemberInfo getFamilyMemberByUserId(Long userId) {
    return query().eq(FamilyMemberInfo::getMemberUserId, userId)
        .last(PageConstant.LIMIT_ONE)
        .getOne();
  }

  @Override
  public FamilyMemberInfo getFamilyMember(Long familyId, Long memberId) {
    return query()
        .eq(FamilyMemberInfo::getFamilyId, familyId)
        .eq(FamilyMemberInfo::getId, memberId)
        .getOne();
  }

  @Override
  public FamilyMemberInfo geMemberByMemberId(Long memberId) {
    return query().eq(FamilyMemberInfo::getId, memberId)
        .last(PageConstant.LIMIT_ONE)
        .getOne();
  }

  @Override
  public Integer getFamilyMemberCount(Long familyId) {
    return Optional.ofNullable(query().eq(FamilyMemberInfo::getFamilyId, familyId)
            //.in(FamilyMemberInfo::getMemberRole, "ADMIN","MEMBER")
            .count())
        .map(Long::intValue)
        .orElse(0);
  }


  @Override
  public Map<Long, Long> mapMemberCountByFamilyIds(Set<Long> familyIds) {

    if (CollectionUtils.isEmpty(familyIds)) {
      return Maps.newHashMap();
    }

    return Optional.ofNullable(query().in(FamilyMemberInfo::getFamilyId, familyIds).list())
        .map(members -> members.stream()
            .collect(Collectors.groupingBy(FamilyMemberInfo::getFamilyId, Collectors.counting())))
        .orElse(Maps.newHashMap());
  }

  @Override
  public void deleteByUserId(Long userId) {
    delete().eq(FamilyMemberInfo::getMemberUserId, userId).execute();
  }

  @Override
  public void updateRoleById(Long memberId, String role) {
    if (Objects.isNull(memberId) || StringUtils.isBlank(role)) {
      return;
    }
    updateSelectiveById(new FamilyMemberInfo()
        .setId(memberId)
        .setMemberRole(role)
    );
  }

  @Override
  public Map<Long, FamilyMemberInfo> mapBaseInfo(Collection<Long> ids) {
    if (CollectionUtils.isEmpty(ids)) {
      return CollectionUtils.newHashMap();
    }
    return Optional.ofNullable(query().in(FamilyMemberInfo::getId, ids).list()
        ).map(baseInfo -> baseInfo.stream().collect(Collectors.toMap(FamilyMemberInfo::getId, v -> v)))
        .orElse(CollectionUtils.newHashMap());
  }

  @Override
  public List<FamilyMemberInfo> listByIds(Collection<Long> ids) {
    if (CollectionUtils.isEmpty(ids)) {
      return CollectionUtils.newArrayList();
    }
    return Optional.ofNullable(query().in(FamilyMemberInfo::getId, ids).list()
    ).orElse(CollectionUtils.newArrayList());
  }

  @Override
  public FamilyMemberInfo getAdmin(Long familyId, String role) {
    return query().eq(FamilyMemberInfo::getFamilyId, familyId)
        .eq(FamilyMemberInfo::getMemberRole, role)
        .last(PageConstant.LIMIT_ONE)
        .getOne();
  }

  @Override
  public Map<Long, FamilyMemberInfo> mapUserIdBaseInfo(Long familyId, Collection<Long> userIds) {
    if (CollectionUtils.isEmpty(userIds) || Objects.isNull(familyId)) {
      return CollectionUtils.newHashMap();
    }
    return Optional.ofNullable(query().in(FamilyMemberInfo::getMemberUserId, userIds).list()
        ).map(baseInfo -> baseInfo.stream()
            .collect(
                Collectors.toMap(FamilyMemberInfo::getMemberUserId, Function.identity(), (k, v) -> v)))
        .orElse(CollectionUtils.newHashMap());
  }

  @Override
  public List<FamilyMemberInfo> listBySysOrigin(String sysOrigin) {
    if (StringUtils.isBlank(sysOrigin)) {
      return CollectionUtils.newArrayList();
    }
    return Optional.ofNullable(query().eq(FamilyMemberInfo::getSysOrigin, sysOrigin).list())
        .orElse(CollectionUtils.newArrayList());
  }

  @Override
  public Integer getManagerCount(Long familyId) {
    return Optional.ofNullable(query().eq(FamilyMemberInfo::getFamilyId, familyId)
            .eq(FamilyMemberInfo::getMemberRole, FamilyRoleEnum.MANAGE.getKey()).count())
        .map(Long::intValue).orElse(0);
  }

  @Override
  public List<FamilyMemberInfo> listByFamilyId(Long familyId) {
    if (Objects.isNull(familyId)) {
      return CollectionUtils.newArrayList();
    }
    return query().eq(FamilyMemberInfo::getFamilyId, familyId).last(PageConstant.formatLimit(5000))
        .list();
  }

  @Override
  public List<FamilyMemberInfo> listFamilyMembers(Long familyId, Long lastId, Integer pageSize) {
    if (Objects.isNull(familyId)) {
      return CollectionUtils.newArrayList();
    }
    return query().eq(FamilyMemberInfo::getFamilyId, familyId)
        .lt(Objects.nonNull(lastId), FamilyMemberInfo::getId, lastId)
        .orderByDesc(FamilyMemberInfo::getId)
        .last(PageConstant.formatLimit(pageSize))
        .list();
  }

}
