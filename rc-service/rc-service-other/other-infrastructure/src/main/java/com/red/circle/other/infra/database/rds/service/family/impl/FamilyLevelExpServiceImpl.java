package com.red.circle.other.infra.database.rds.service.family.impl;


import com.google.common.collect.Maps;
import com.red.circle.framework.mybatis.constant.PageConstant;
import com.red.circle.framework.mybatis.service.impl.BaseServiceImpl;
import com.red.circle.other.infra.database.rds.dao.family.FamilyLevelExpDAO;
import com.red.circle.other.infra.database.rds.entity.family.FamilyLevelExp;
import com.red.circle.other.infra.database.rds.service.family.FamilyLevelExpService;
import com.red.circle.tool.core.collection.CollectionUtils;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 工会等级经验 服务实现类.
 * </p>
 *
 * <AUTHOR>
 * @since 2022-08-04
 */
@Service
public class FamilyLevelExpServiceImpl extends
    BaseServiceImpl<FamilyLevelExpDAO, FamilyLevelExp> implements
    FamilyLevelExpService {


  @Override
  public Map<Long, BigDecimal> mapTotalExpCountByFamilyIds(Set<Long> familyIds) {

    if (CollectionUtils.isEmpty(familyIds)) {
      return Maps.newHashMap();
    }

    List<FamilyLevelExp> familyLevelExps = query()
        .in(FamilyLevelExp::getFamilyId, familyIds)
        .list();

    if (CollectionUtils.isEmpty(familyLevelExps)) {
      return Maps.newHashMap();
    }

    Map<Long, List<FamilyLevelExp>> exps = familyLevelExps.stream()
        .collect(Collectors.groupingBy(FamilyLevelExp::getFamilyId));

    Map<Long, BigDecimal> totalMap = Maps.newHashMap();
    exps.forEach((key, val) -> {
      if (CollectionUtils.isEmpty(val)) {
        return;
      }
      totalMap.put(key,
          val.stream().map(FamilyLevelExp::getExp).reduce(BigDecimal.ZERO, BigDecimal::add)
              .setScale(0, RoundingMode.DOWN));
    });
    return totalMap;
  }

  @Override
  public void incrExp(Long familyId, Long familyLevelId, BigDecimal exp) {
    if (Objects.isNull(familyId) || Objects.isNull(familyLevelId)) {
      return;
    }
    FamilyLevelExp balance = query().select(FamilyLevelExp::getId)
        .eq(FamilyLevelExp::getFamilyId, familyId)
        .eq(FamilyLevelExp::getFamilyLevelId, familyLevelId)
        .last(PageConstant.LIMIT_ONE)
        .getOne();
    if (Objects.isNull(balance) || Objects.isNull(balance.getId())) {
      save(new FamilyLevelExp()
          .setFamilyId(familyId)
          .setFamilyLevelId(familyLevelId)
          .setExp(exp)
      );
      return;
    }
    update()
        .setSql("exp=exp+" + exp)
        .eq(FamilyLevelExp::getId, balance.getId())
        .execute();
  }

  @Override
  public BigDecimal getExp(Long familyId, Long familyLevelId) {
    return Optional.ofNullable(
            query()
                .eq(FamilyLevelExp::getFamilyId, familyId)
                .eq(FamilyLevelExp::getFamilyLevelId, familyLevelId)
                .last(PageConstant.LIMIT_ONE)
                .getOne()
        )
        .map(FamilyLevelExp::getExp)
        .orElse(BigDecimal.ZERO);
  }


  @Override
  public void deleteByFamilyId(Long familyId) {
    delete().eq(FamilyLevelExp::getFamilyId, familyId).execute();
  }
}
