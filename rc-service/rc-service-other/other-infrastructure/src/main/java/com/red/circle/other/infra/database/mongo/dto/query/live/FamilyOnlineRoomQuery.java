package com.red.circle.other.infra.database.mongo.dto.query.live;

import java.io.Serializable;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 活跃工会房间搜索条件.
 *
 * <AUTHOR> on 2021/7/23
 */
@Data
@Accessors(chain = true)
public class FamilyOnlineRoomQuery implements Serializable {

  private static final long serialVersionUID = 1L;

  /**
   * 工会ID.
   */
  Long familyId;

  /**
   * 工会房间数量
   */
  Integer pageSize;
}
