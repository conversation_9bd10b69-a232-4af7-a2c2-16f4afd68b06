package com.red.circle.other.infra.database.rds.entity.family;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.red.circle.framework.mybatis.entity.TimestampBaseEntity;
import java.math.BigDecimal;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 工会等级经验.
 * </p>
 *
 * <AUTHOR>
 * @since 2022-08-04
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("family_level_exp")
public class FamilyLevelExp extends TimestampBaseEntity {

  private static final long serialVersionUID = 1L;

  /**
   * 主键id.
   */
  @TableId(value = "id", type = IdType.ASSIGN_ID)
  private Long id;

  /**
   * 工会id.
   */
  @TableField("family_id")
  private Long familyId;

  /**
   * 工会等级.
   */
  @TableField("family_level_id")
  private Long familyLevelId;

  /**
   * 贡献.
   */
  @TableField("exp")
  private BigDecimal exp;

}
