package com.red.circle.other.infra.database.rds.service.badge;

import com.red.circle.framework.dto.PageResult;
import com.red.circle.framework.mybatis.service.BaseService;
import com.red.circle.other.infra.database.rds.entity.badge.BadgeBackpack;
import com.red.circle.other.infra.database.rds.entity.badge.BadgeConfig;
import com.red.circle.other.inner.enums.material.BadgeConfigTypeEnum;
import com.red.circle.other.inner.enums.material.BadgeKeyEnum;
import com.red.circle.other.inner.enums.sys.SysBadgeConfigTypeEnum;
import com.red.circle.other.inner.model.cmd.sys.GiveBadgeConfigQryCmd;
import com.red.circle.other.inner.model.cmd.sys.SysBadgeConfigQryCmd;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <p>
 * 徽章配置 服务类.
 * </p>
 *
 * <AUTHOR>
 * @since 2021-03-12
 */
public interface BadgeConfigService extends BaseService<BadgeConfig> {

  /**
   * 获取管理员徽章id.
   *
   * @return ignore
   */
  Long getAdminBadge();

  List<BadgeConfig> listBadgeByType(SysBadgeConfigTypeEnum type);

  /**
   * 获得货运代理徽章ID.
   *
   * @return 徽章ID.
   */
  Long getFreightBadgeId();

  /**
   * 回去管理员徽章id集合.
   *
   * @return ignore
   */
  List<Long> listAdministratorIds();

  /**
   * 查询管理员徽章
   */
  List<Long> listAdminBadgeIds();

  /**
   * 获取徽章个等级信息.
   *
   * @param badgeKey 类型
   * @return ignore
   */
  List<BadgeConfig> listByBadgeKey(BadgeKeyEnum badgeKey);

  /**
   * 获取所有配置徽章信息.
   *
   * @return ignore
   */
  List<BadgeConfig> listBadgeConfig(BadgeConfigTypeEnum type);

  /**
   * 获取指定在一组徽章信息.
   *
   * @param ids 参数id
   * @return ignore
   */
  List<BadgeConfig> listByIds(List<Long> ids);

  /**
   * 获取指定一组徽章映射信息.
   *
   * @param ids id
   * @return ignore
   */
  Map<Long, BadgeConfig> mapByIds(Set<Long> ids);


  /**
   * 分页获取徽章信息.
   */
  PageResult<BadgeConfig> getGiveBadgePage(GiveBadgeConfigQryCmd query, Set<Long> collect);

  /**
   * 获取徽章信息.
   */
  List<BadgeConfig> listBadgeByIds(SysBadgeConfigTypeEnum type, Set<Long> collect);

  /**
   * 删除徽章信息.
   */
  void deleteBadge(Long id);

  /**
   * 修改徽章信息.
   */
  void updateBadgeConfig(BadgeConfig toBadgeConfig);

  /**
   * 分页获取徽章信息.
   */
  PageResult<BadgeConfig> getBadgeConfig(SysBadgeConfigQryCmd query);

  /**
   * 获取管理员徽章
   */
  Long getAdminBadgeId(String roles);

  /**
   * 获取用户等级最高的工会徽章配置
   */
  BadgeConfig getUserMaxLevelFamilyBadge(Long userId);

  /**
   * 获取徽章列表
   * @param type
   * @param s
   * @return
   */
  List<BadgeConfig> getBadgeConfigList(BadgeConfigTypeEnum type, String s);
}
