package com.red.circle.other.infra.database.rds.entity.family;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.red.circle.framework.mybatis.entity.TimestampBaseEntity;
import java.io.Serial;
import java.math.BigDecimal;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 主播周政策表.
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("anchor_weekly_policy")
public class AnchorWeeklyPolicy extends TimestampBaseEntity {

  @Serial
  private static final long serialVersionUID = 1L;

  /**
   * 主键ID.
   */
  @TableId(value = "id", type = IdType.ASSIGN_ID)
  private Long id;

  /**
   * 政策名称.
   */
  @TableField("policy_name")
  private String policyName;

  /**
   * 收礼档位金币门槛(分).
   */
  @TableField("gift_gold_threshold")
  private Long giftGoldThreshold;

  /**
   * 主播提成比例(%).
   */
  @TableField("anchor_commission_ratio")
  private BigDecimal anchorCommissionRatio;

  /**
   * 工会提成比例(%).
   */
  @TableField("family_commission_ratio")
  private BigDecimal familyCommissionRatio;

  /**
   * 金币转钻石比例(%).
   */
  @TableField("gold_to_diamond_ratio")
  private BigDecimal goldToDiamondRatio;

  /**
   * 优先级(数字越大优先级越高).
   */
  @TableField("priority")
  private Integer priority;

  /**
   * 状态 0.禁用 1.启用.
   */
  @TableField("status")
  private Integer status;

}
