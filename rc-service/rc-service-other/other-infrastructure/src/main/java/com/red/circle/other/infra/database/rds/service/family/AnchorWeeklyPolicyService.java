package com.red.circle.other.infra.database.rds.service.family;

import com.baomidou.mybatisplus.extension.service.IService;
import com.red.circle.other.infra.database.rds.entity.family.AnchorWeeklyPolicy;
import java.util.List;

/**
 * 主播周政策Service.
 *
 * <AUTHOR>
 */
public interface AnchorWeeklyPolicyService extends IService<AnchorWeeklyPolicy> {

  /**
   * 获取所有启用的政策，按优先级降序排列.
   *
   * @return 政策列表
   */
  List<AnchorWeeklyPolicy> listEnabledPoliciesOrderByPriority();

  /**
   * 根据收礼金币数量匹配最合适的政策.
   *
   * @param giftGoldAmount 收礼金币数量(分)
   * @return 匹配的政策，如果没有匹配返回null
   */
  AnchorWeeklyPolicy findMatchingPolicy(Long giftGoldAmount);

}
