package com.red.circle.other.infra.database.rds.entity.family;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.red.circle.framework.mybatis.entity.TimestampBaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 工会每月贡献值榜单.
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-21
 * @since pengliang 2022-8-4  modify
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("family_month_exp")
public class FamilyMonthExp extends TimestampBaseEntity {

  private static final long serialVersionUID = 1L;

  /**
   * 主键标识.
   */
  @TableId(value = "id", type = IdType.ASSIGN_ID)
  private Long id;

  /**
   * 来源系统.
   */
  @TableField("sys_origin")
  private String sysOrigin;

  /**
   * 工会ID.
   */
  @TableField("family_id")
  private Long familyId;

  /**
   * 工会贡献值.
   */
  @TableField("exp")
  private Long exp;

  /**
   * 月份日期 yyyyMM.
   */
  @TableField("date_split")
  private Integer dateSplit;

  /**
   * 区域.
   */
  @TableField("regions")
  private String regions;

}
