package com.red.circle.other.infra.database.rds.service.family;


import com.red.circle.framework.mybatis.service.BaseService;
import com.red.circle.other.infra.database.rds.dto.family.FamilyMemberExpDTO;
import com.red.circle.other.infra.database.rds.entity.family.FamilyMemberWeekExp;
import com.red.circle.other.infra.enums.family.FamilyRoleEnum;
import java.util.List;

/**
 * <p>
 * 工会成员每周贡献值榜单(每周清空贡献值) 服务类.
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-21
 */
public interface FamilyMemberWeekExpService extends BaseService<FamilyMemberWeekExp> {

  void deleteBySysOriginMemberId(Long memberId, String sysOrigin);

  /**
   * 添加成员经验.
   */
  void incrExp(String sysOrigin, Long familyId, Long userId, Long exp);

  /**
   * 保存或修改经验.
   */
  void saveOrUpdate(String sysOrigin, Long familyId, Long userId, Long exp);

  FamilyMemberWeekExp getFamilyMemberWeekExp(Long familyId, Long memberUserId);

  List<FamilyMemberExpDTO> pageByFamilyId(Long familyId, List<FamilyRoleEnum> roles,
      Integer pageNo, Integer pageSize);

  void deleteByFamilyId(Long familyId);

  void deleteByFamilyIdByMemberId(Long familyId, Long id);

  List<FamilyMemberExpDTO> listByFamilyId(Long familyId, List<FamilyRoleEnum> roles);
}
