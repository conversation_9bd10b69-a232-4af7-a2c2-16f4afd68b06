package com.red.circle.other.infra.database.rds.service.family.impl;


import com.red.circle.framework.mybatis.service.impl.BaseServiceImpl;
import com.red.circle.other.infra.database.rds.dao.family.FamilyDailyTaskTriggerRecordDAO;
import com.red.circle.other.infra.database.rds.entity.family.FamilyDailyTaskTriggerRecord;
import com.red.circle.other.infra.database.rds.service.family.FamilyDailyTaskTriggerRecordService;
import com.red.circle.tool.core.collection.CollectionUtils;
import java.util.List;
import java.util.Optional;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 工会每日任务 服务实现类.
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-26
 */
@Service
public class FamilyDailyTaskTriggerRecordServiceImpl extends
    BaseServiceImpl<FamilyDailyTaskTriggerRecordDAO, FamilyDailyTaskTriggerRecord> implements
    FamilyDailyTaskTriggerRecordService {

  @Override
  public void deleteAll() {
    delete().execute();
  }

  @Override
  public List<FamilyDailyTaskTriggerRecord> listByCondition(
      FamilyDailyTaskTriggerRecord triggerRecord) {
    return Optional.ofNullable(query()
        .eq(FamilyDailyTaskTriggerRecord::getSysOrigin, triggerRecord.getSysOrigin())
        .eq(FamilyDailyTaskTriggerRecord::getTaskType, triggerRecord.getTaskType())
        .eq(FamilyDailyTaskTriggerRecord::getFamilyMemberId, triggerRecord.getFamilyMemberId())
        .list()).orElse(CollectionUtils.newArrayList());
  }
}
