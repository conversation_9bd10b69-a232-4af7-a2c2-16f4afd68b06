package com.red.circle.other.infra.database.rds.service.family;


import com.red.circle.framework.dto.PageResult;
import com.red.circle.framework.mybatis.service.BaseService;
import com.red.circle.other.infra.database.rds.entity.family.FamilyMemberInfo;
import com.red.circle.other.inner.model.cmd.famliy.FamilyMemberQryCmd;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <p>
 * 工会成员表 服务类.
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-20
 */
public interface FamilyMemberInfoService extends BaseService<FamilyMemberInfo> {

  Long getFamilyIdByUserId(Long userId);

  void deleteByFamilyId(Long familyId);

  void deleteMemberById(FamilyMemberInfo familyMember);

  PageResult<FamilyMemberInfo> pageData(FamilyMemberQryCmd queryWhere);

  FamilyMemberInfo getFamilyMemberByUserId(Long userId);

  FamilyMemberInfo getFamilyMember(Long familyId, Long memberId);

  FamilyMemberInfo geMemberByMemberId(Long memberId);

  Integer getFamilyMemberCount(Long familyId);

  Map<Long, Long> mapMemberCountByFamilyIds(Set<Long> familyIds);

  void deleteByUserId(Long userId);

  void updateRoleById(Long memberId, String role);

  Map<Long, FamilyMemberInfo> mapBaseInfo(Collection<Long> ids);

  List<FamilyMemberInfo> listByIds(Collection<Long> ids);

  FamilyMemberInfo getAdmin(Long familyId, String role);

  Map<Long, FamilyMemberInfo> mapUserIdBaseInfo(Long familyId, Collection<Long> userIds);

  List<FamilyMemberInfo> listBySysOrigin(String sysOrigin);

  Integer getManagerCount(Long familyId);

  List<FamilyMemberInfo> listByFamilyId(Long familyId);

  /**
   * 获取工会成员.
   */
  List<FamilyMemberInfo> listFamilyMembers(Long familyId, Long lastId, Integer pageSize);


}
