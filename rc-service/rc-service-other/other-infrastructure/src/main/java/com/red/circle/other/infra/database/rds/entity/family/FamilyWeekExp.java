package com.red.circle.other.infra.database.rds.entity.family;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.red.circle.framework.mybatis.entity.TimestampBaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 工会每周贡献值榜单.
 * </p>
 *
 * <AUTHOR> on 2021-07-21
 * @since pengliang on 2022-8-4 modify
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("family_week_exp")
public class FamilyWeekExp extends TimestampBaseEntity {

  private static final long serialVersionUID = 1L;

  /**
   * 主键标识.
   */
  @TableId(value = "id", type = IdType.ASSIGN_ID)
  private Long id;

  /**
   * 来源系统.
   */
  @TableField("sys_origin")
  private String sysOrigin;

  /**
   * 工会ID.
   */
  @TableField("family_id")
  private Long familyId;

  /**
   * 工会贡献值.
   */
  @TableField("exp")
  private Long exp;

  /**
   * 日期分割.
   */
  @TableField("date_split")
  private Integer dateSplit;

  /**
   * 区域.
   */
  @TableField("regions")
  private String regions;

}
