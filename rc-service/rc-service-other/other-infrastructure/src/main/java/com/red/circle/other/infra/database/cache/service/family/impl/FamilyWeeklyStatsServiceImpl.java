package com.red.circle.other.infra.database.cache.service.family.impl;

import com.fasterxml.jackson.core.type.TypeReference;
import com.red.circle.component.redis.service.RedisService;
import com.red.circle.other.infra.database.cache.key.FamilyWeeklyKey;
import com.red.circle.other.infra.database.cache.service.family.FamilyWeeklyStatsService;
import com.red.circle.other.infra.database.rds.entity.family.AnchorWeeklyPolicy;
import com.red.circle.other.infra.database.rds.service.family.AnchorWeeklyPolicyService;
import com.red.circle.tool.core.json.JacksonUtils;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.temporal.WeekFields;
import java.util.List;
import java.util.Locale;
import java.util.concurrent.TimeUnit;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

/**
 * 工会周统计服务实现.
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class FamilyWeeklyStatsServiceImpl implements FamilyWeeklyStatsService {

  private final RedisService redisService;
  private final AnchorWeeklyPolicyService anchorWeeklyPolicyService;

  private static final int WEEKLY_TTL_DAYS = 7;

  @Override
  public Long addWeeklyGiftGold(Long familyId, Long userId, Long giftGoldValue) {
    String key = buildWeeklyStatsKey(familyId, userId);
    Long totalAmount = redisService.increment(key, giftGoldValue);

    // 设置7天过期时间
    redisService.expire(key, WEEKLY_TTL_DAYS, TimeUnit.DAYS);

    log.debug("用户周收礼统计更新: familyId={}, userId={}, 本次={}, 累计={}",
        familyId, userId, giftGoldValue, totalAmount);

    return totalAmount;
  }

  @Override
  public Long getWeeklyGiftGold(Long familyId, Long userId) {
    String key = buildWeeklyStatsKey(familyId, userId);
    Long amount = redisService.getBitmapsCount(key);
    return amount != null ? amount : 0L;
  }

  @Override
  public List<AnchorWeeklyPolicy> getCachedPolicies() {
    String key = buildPolicyCacheKey();
    String json = redisService.getString(key);

    if (json != null) {
      return JacksonUtils.parseObject(json, new TypeReference<List<AnchorWeeklyPolicy>>() {});
    }

    // 缓存不存在，从数据库加载并缓存
    List<AnchorWeeklyPolicy> policies = anchorWeeklyPolicyService.listEnabledPoliciesOrderByPriority();
    cachePolicies(policies);
    return policies;
  }

  @Override
  public void cachePolicies(List<AnchorWeeklyPolicy> policies) {
    if (CollectionUtils.isEmpty(policies)) {
      return;
    }

    String key = buildPolicyCacheKey();
    String json = JacksonUtils.toJson(policies);
    redisService.setEx(key, json, WEEKLY_TTL_DAYS, TimeUnit.DAYS);

    log.info("缓存政策列表: {} 条政策", policies.size());
  }

  @Override
  public AnchorWeeklyPolicy findMatchingPolicy(Long weeklyGiftGold) {
    if (weeklyGiftGold == null || weeklyGiftGold <= 0) {
      return null;
    }

    List<AnchorWeeklyPolicy> policies = getCachedPolicies();

    // 从最高档位开始匹配
    for (AnchorWeeklyPolicy policy : policies) {
      if (weeklyGiftGold >= policy.getGiftGoldThreshold()) {
        log.debug("匹配到政策: {}, 收礼金币: {}, 门槛: {}",
            policy.getPolicyName(), weeklyGiftGold, policy.getGiftGoldThreshold());
        return policy;
      }
    }

    log.debug("未匹配到任何政策, 收礼金币: {}", weeklyGiftGold);
    return null;
  }

  @Override
  public String getCurrentWeek() {
    LocalDate now = LocalDate.now();
    WeekFields weekFields = WeekFields.of(Locale.getDefault());
    int year = now.getYear();
    int week = now.get(weekFields.weekOfYear());
    return String.format("%d-%02d", year, week);
  }

  /**
   * 构建周统计Key.
   * 格式: FAMILY:WEEKLY:STATS:{familyId}:{userId}:{week}
   */
  private String buildWeeklyStatsKey(Long familyId, Long userId) {
    return redisService.buildKey(FamilyWeeklyKey.WEEKLY_STATS,
        familyId, userId, getCurrentWeek());
  }

  /**
   * 构建政策缓存Key.
   * 格式: FAMILY:POLICY:CACHE:{week}
   */
  private String buildPolicyCacheKey() {
    return redisService.buildKey(FamilyWeeklyKey.POLICY_CACHE, getCurrentWeek());
  }

}
