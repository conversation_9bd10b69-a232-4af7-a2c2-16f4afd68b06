package com.red.circle.other.infra.database.rds.entity.family;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.red.circle.framework.mybatis.entity.TimestampBaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 工会消息表.
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-20
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("family_message")
public class FamilyMessage extends TimestampBaseEntity {

  private static final long serialVersionUID = 1L;

  /**
   * 主键标识.
   */
  @TableId(value = "id", type = IdType.ASSIGN_ID)
  private Long id;

  /**
   * 来源系统.
   */
  @TableField("sys_origin")
  private String sysOrigin;

  /**
   * 发送人用户ID
   */
  @TableField("sender_user")
  private Long senderUser;

  /**
   * 审批用户ID
   */
  @TableField("approve_user")
  private Long approveUser;

  /**
   * 工会ID.
   */
  @TableField("family_id")
  private Long familyId;

  /**
   * 标题.
   */
  @TableField("title")
  private String title;

  /**
   * 内容.
   */
  @TableField("content")
  private String content;

  /**
   * 消息类型.
   */
  @TableField("type")
  private String type;

  /**
   * 业务事件 FamilyMsgBusinessEnum
   */
  @TableField("business_event")
  private String businessEvent;

  /**
   * 状态(0.未处理 1.已处理).
   */
  @TableField("status")
  private Boolean status;

}
