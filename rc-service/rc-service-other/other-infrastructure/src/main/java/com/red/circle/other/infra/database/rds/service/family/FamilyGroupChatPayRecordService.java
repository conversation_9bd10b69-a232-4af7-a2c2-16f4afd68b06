package com.red.circle.other.infra.database.rds.service.family;

import com.red.circle.framework.mybatis.service.BaseService;
import com.red.circle.other.infra.database.rds.entity.family.FamilyGroupChatPayRecord;

/**
 * <p>
 * 工会群聊付费解锁记录 服务类.
 * </p>
 *
 * <AUTHOR> on 2023-12-21 17:56
 */
public interface FamilyGroupChatPayRecordService extends BaseService<FamilyGroupChatPayRecord> {

  Boolean getByFamilyIdId(Long familyId);

  void dismissGroupByFamilyId(Long familyId);

}
