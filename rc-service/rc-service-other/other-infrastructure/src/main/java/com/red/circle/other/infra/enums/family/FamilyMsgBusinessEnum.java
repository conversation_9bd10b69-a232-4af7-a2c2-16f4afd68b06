package com.red.circle.other.infra.enums.family;

/**
 * 工会消息操作业务.
 *
 * <AUTHOR>
 * @since 2021-07-23
 */
public enum FamilyMsgBusinessEnum {

  /**
   * 同意.
   */
  AGREE("同意"),

  /**
   * 拒绝.
   */
  REFUSE("拒绝");

  private final String description;

  FamilyMsgBusinessEnum(String description) {
    this.description = description;
  }

  public String getDescription() {
    return description;
  }

}
