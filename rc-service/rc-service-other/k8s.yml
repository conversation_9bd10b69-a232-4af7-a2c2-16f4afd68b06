---
apiVersion: apps/v1
kind: Deployment
metadata:
  annotations: {}
  labels:
    app: other
  name: other
  namespace: local
  resourceVersion: '93075962'
spec:
  progressDeadlineSeconds: 600
  replicas: 1
  revisionHistoryLimit: 2
  selector:
    matchLabels:
      app: other
  strategy:
    rollingUpdate:
      maxSurge: 25%
      maxUnavailable: 25%
    type: RollingUpdate
  template:
    metadata:
      annotations:
        kubectl.kubernetes.io/restartedAt: '2025-05-22T12:02:57+08:00'
      creationTimestamp: null
      labels:
        app: other
        pod-template-hash: b65cbc5c6
    spec:
      containers:
        - env:
            - name: SERVER_PORT
              value: '1800'
            - name: SERVER_PROFILE_ACTIVE
              value: dev
            - name: TRACE_PROTOCOL
              value: grpc
            - name: TRACE_ENDPOINT
              value: 'https://asxxxdev.ap-southeast-1.log.aliyuncs.com:10010'
            - name: TRACE_COMPRESSION
              value: gzip
            - name: TRACE_HEADERS
              value: >-
                x-sls-otel-project=aswat-app-trace-dev,x-sls-otel-instance-id=aswat-develop,x-sls-otel-ak-id=LTAI5xxxfeKrE5x,x-sls-otel-ak-secret=3kKQWtvXxxx6SzmHz
            - name: TRACE_HOST_NAME
              valueFrom:
                fieldRef:
                  apiVersion: v1
                  fieldPath: status.podIP
            - name: NACOS_HOST
              valueFrom:
                configMapKeyRef:
                  key: nacos.host
                  name: nacos
            - name: NACOS_PORT
              valueFrom:
                configMapKeyRef:
                  key: nacos.port
                  name: nacos
            - name: NACOS_USERNAME
              valueFrom:
                configMapKeyRef:
                  key: nacos.username
                  name: nacos
            - name: NACOS_PASSWORD
              valueFrom:
                configMapKeyRef:
                  key: nacos.password
                  name: nacos
          image: >-
            794038239327.dkr.ecr.ap-southeast-1.amazonaws.com/halar-dev:other-20250617v124409
          imagePullPolicy: IfNotPresent
          lifecycle:
            preStop:
              exec:
                command:
                  - /bin/bash
                  - '-c'
                  - >-
                    curl -X POST
                    http://localhost:1800/actuator/nacosservicederegister?key=CxILm9hA1b9hF3Hl
                    --header 'Content-Type:
                    application/vnd.spring-boot.actuator.v2+json;charset=UTF-8'
                  - sleep 30
          livenessProbe:
            failureThreshold: 2
            httpGet:
              path: /actuator/health
              port: 1800
              scheme: HTTP
            periodSeconds: 60
            successThreshold: 1
            timeoutSeconds: 20
          name: other
          ports:
            - containerPort: 1800
              protocol: TCP
          readinessProbe:
            failureThreshold: 2
            httpGet:
              path: /actuator/health
              port: 1800
              scheme: HTTP
            periodSeconds: 5
            successThreshold: 1
            timeoutSeconds: 10
          resources:
            limits:
              memory: 2Gi
            requests:
              memory: 1Gi
          startupProbe:
            failureThreshold: 60
            httpGet:
              path: /actuator/health
              port: 1800
              scheme: HTTP
            periodSeconds: 5
            successThreshold: 1
            timeoutSeconds: 5
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
      dnsPolicy: ClusterFirst
      imagePullSecrets:
        - name: aliyun-secret
      restartPolicy: Always
      schedulerName: default-scheduler
      securityContext: {}
      terminationGracePeriodSeconds: 120
status:
  availableReplicas: 1
  conditions:
    - lastTransitionTime: '2025-05-22T05:08:28Z'
      lastUpdateTime: '2025-05-22T05:08:28Z'
      message: Deployment has minimum availability.
      reason: MinimumReplicasAvailable
      status: 'True'
      type: Available
    - lastTransitionTime: '2025-05-22T05:08:28Z'
      lastUpdateTime: '2025-06-17T12:48:13Z'
      message: ReplicaSet "other-5497ff55d4" has successfully progressed.
      reason: NewReplicaSetAvailable
      status: 'True'
      type: Progressing
  observedGeneration: 209
  readyReplicas: 1
  replicas: 1
  updatedReplicas: 1

---
apiVersion: v1
kind: Service
metadata:
  annotations: {}
  name: other
  namespace: local
  resourceVersion: '22829616'
spec:
  clusterIP: **************
  clusterIPs:
    - **************
  internalTrafficPolicy: Cluster
  ipFamilies:
    - IPv4
  ipFamilyPolicy: SingleStack
  ports:
    - port: 80
      protocol: TCP
      targetPort: 1800
  selector:
    app: other
  sessionAffinity: None
  type: ClusterIP
status:
  loadBalancer: {}

