package com.red.circle.wallet.inner.model.enums;

import java.util.HashMap;
import java.util.Map;

/**
 * 金币来源映射.
 *
 * <AUTHOR> on 2023/9/14
 */
public interface GoldOriginMapping {

  Map<String, String> GAME_MAPPING = new HashMap<>() {{
    // hot
    // 游戏排行榜奖励
    put("HOT_GAME_200", "[Game1]ranking rewards");
    // 贪婪星球
    put("HOT_GAME_201", "[Game1]greedy planet");
    // 水果拉霸
    put("HOT_GAME_202", "[Game1]Fruit slot");
    // 炸金花
    put("HOT_GAME_203", "[Game1]fried golden flower");
    // 水果机
    put("HOT_GAME_204", "[Game1]fruit machine");
    // 赛车
    put("HOT_GAME_205", "[Game1]racing car");
    // 贪婪宝箱
    put("HOT_GAME_206", "[Game1]Greed box");
    // 捕鱼达人
    put("HOT_GAME_207", "[Game1]fishing master");
    // 海盗王
    put("HOT_GAME_208", "[Game1]pirate king");
    // 龙虎
    put("HOT_GAME_209", "[Game1]dragon tiger");
    // 幸运77
    put("HOT_GAME_210", "[Game1]Lucky 77");
    // 逃逸
    put("HOT_GAME_211", "[Game1]escape");
    // 高低牌
    put("HOT_GAME_212", "[Game1]high and low cards");
    // 拳击
    put("HOT_GAME_213", "[Game1]boxing");
    // 石头剪刀布
    put("HOT_GAME_214", "[Game1]rock-paper-scissors");
    // 足球拉霸
    put("HOT_GAME_215", "[Game1]Soccer slot");
    // 贪婪猫咪
    put("HOT_GAME_220", "[Game1]Greedy Cat");

    // hkys
    // 幸运水果机
    put("HKYS_GAME_101", "[Game2]lucky fruit machine");
    put("HKYS_GAME_201", "[Game2]lucky fruit machine");
    // 拉霸水果
    put("HKYS_GAME_102", "[Game2]sloth fruit");
    put("HKYS_GAME_202", "[Game2]sloth fruit");
    // 多人捕鱼
    put("HKYS_GAME_103", "[Game2]fishing");
    put("HKYS_GAME_203", "[Game2]fishing");
    // 龙虎斗
    put("HKYS_GAME_104", "[Game2]Dragon Tiger Fight");
    put("HKYS_GAME_204", "[Game2]Dragon Tiger Fight");
    // 赛马
    put("HKYS_GAME_105", "[Game2]race");
    put("HKYS_GAME_205", "[Game2]race");
    // 三色椅子
    put("HKYS_GAME_106", "[Game2]tricolor chair");
    put("HKYS_GAME_206", "[Game2]tricolor chair");
    // 点球大战
    put("HKYS_GAME_107", "[Game2]penalty shootout");
    put("HKYS_GAME_207", "[Game2]penalty shootout");
    // 幸运摩天轮
    put("HKYS_GAME_108", "[Game2]lucky ferris wheel");
    put("HKYS_GAME_208", "[Game2]lucky ferris wheel");
    // 盒子摩天轮
    put("HKYS_GAME_109", "[Game2]box ferris wheel");
    put("HKYS_GAME_209", "[Game2]box ferris wheel");
    // 飞机英雄
    put("HKYS_GAME_110", "[Game2]airplane hero");
    put("HKYS_GAME_210", "[Game2]airplane hero");
    // 水果转盘
    put("HKYS_GAME_111", "[Game2]fruit turntable");
    put("HKYS_GAME_211", "[Game2]fruit turntable");
    // 急速竞技
    put("HKYS_GAME_112", "[Game2]racing");
    put("HKYS_GAME_212", "[Game2]racing");
    // 大锤子
    put("HKYS_GAME_113", "[Game2]big hammer");
    put("HKYS_GAME_213", "[Game2]big hammer");
    // 动物园地
    put("HKYS_GAME_114", "[Game2]zoo land");
    put("HKYS_GAME_214", "[Game2]zoo land");
    // 猜骰子
    put("HKYS_GAME_115", "[Game2]Guess the dice");
    put("HKYS_GAME_215", "[Game2]Guess the dice");
    // 神庙寻宝
    put("HKYS_GAME_116", "[Game2]Temple Treasure Hunt");
    put("HKYS_GAME_216", "[Game2]Temple Treasure Hunt");

    // Baishun
    // 德州扑克
    put("BAISHUN_GAME_1001", "[Game3] Texas Hold'em");
    // ⽼⻁机
    put("BAISHUN_GAME_1004", "[Game3] Old machine");
    // 跳⼀跳
    put("BAISHUN_GAME_1005", "[Game3] Hop");
    // ⽔果机
    put("BAISHUN_GAME_1006", "[Game3] Fruit machine");
    // 天使与恶魔
    put("BAISHUN_GAME_1007", "[Game3] Angels and demons");
    // 打地⿏
    put("BAISHUN_GAME_1008", "[Game3] Hit the ground mouse");
    // 猜拳
    put("BAISHUN_GAME_1009", "[Game3] Mora");
    // 时时彩
    put("BAISHUN_GAME_1010", "[Game3] Colorful");
    // 数字⼤战
    put("BAISHUN_GAME_1012", "[Game3] Digital war");
    // 赛⻢
    put("BAISHUN_GAME_1013", "[Game3] Racehorse");
    // ⼀分快三
    put("BAISHUN_GAME_1014", "[Game3] Three points");
    // 台球
    put("BAISHUN_GAME_1015", "[Game3] Billiards");
    // ⼩⽕箭
    put("BAISHUN_GAME_1016", "[Game3] Small fire arrow");
    // ⽔果 2
    put("BAISHUN_GAME_1017", "[Game3] Fruit 2");
    // 赛⻢ 2
    put("BAISHUN_GAME_1018", "[Game3] Racehorse 2");
    // ⽼⻁机 2
    put("BAISHUN_GAME_1019", "[Game3] Old machine 2");
    // ⽔果农场
    put("BAISHUN_GAME_1020", "[Game3] Fruit farm");
    // 幸运礼物
    put("BAISHUN_GAME_1021", "[Game3] Lucky gift");

    // gift
    // 普通礼物
    put("ACCEPT_GIFT_ORDINARY", "[Ordinary] Accept Gift");
    put("GIVE_GIFT_ORDINARY", "[Ordinary] Give Gift");
    // 国旗礼物
    put("ACCEPT_GIFT_NATIONAL_FLAG", "[NationalFlag] Accept Gift");
    put("GIVE_GIFT_NATIONAL_FLAG", "[NationalFlag] Give Gift");
    // CP礼物
    put("ACCEPT_GIFT_CP", "[CP] Accept Gift");
    put("GIVE_GIFT_CP", "[CP] Give Gift");
    // 工会礼物
    put("ACCEPT_GIFT_FAMILY", "[Family] Accept Gift");
    put("GIVE_GIFT_FAMILY", "[Family] Give Gift");
    // 专属礼物
    put("ACCEPT_GIFT_EXCLUSIVE", "[Exclusive] Accept Gift");
    put("GIVE_GIFT_EXCLUSIVE", "[Exclusive] Give Gift");
    // 贵族礼物
    put("ACCEPT_GIFT_ARISTOCRACY", "[Aristocracy] Accept Gift");
    put("GIVE_GIFT_ARISTOCRACY", "[Aristocracy] Give Gift");
    // 幸运礼物
    put("ACCEPT_GIFT_LUCKY_GIFT", "[LuckyGift] Accept Gift");
    put("GIVE_GIFT_LUCKY_GIFT", "[LuckyGift] Give Gift");


  }};

}
