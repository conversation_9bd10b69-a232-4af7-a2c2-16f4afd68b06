package com.red.circle.wallet.inner.model.enums;

/**
 * 游戏券来源
 *
 * <AUTHOR> on 2021/12/21 16:15
 */
public enum GameCouponOrigin {

  /**
   * 活动奖励.
   */
  ACTIVITY_REWARD("Activity reward"),

  /**
   * 水果游戏-扣除.
   */
  DEDUCT_FRUIT_MACHINE("Fruit machine"),

  /**
   * 烧烤游戏-扣除.
   */
  DEDUCT_BARBECUE_MACHINE("Barbecue machine"),

  /**
   * 扑克游戏-扣除.
   */
  DEDUCT_POKER_GAME("Poker game"),

  /**
   * 骰子游戏-扣除.
   */
  DEDUCT_DICE_GAME("Dice game"),

  /**
   * 活动奖励.
   */
  ACTIVITY_AWARD("Activity Award"),

  /**
   * 系统误发
   */
  SYSTEM_MISREPRESENTED("[System] System misrepresented"),

  /**
   * 多品种水果游戏.
   */
  DOUBLE_LAYER_FRUIT("Multi-variety lottery game"),

  /**
   * cp奖励.
   */
  CP_REWARD("CP Reward"),

  /**
   * 累计充值奖励.
   */
  CUMULATIVE_RECHARGE_REWARDS("Cumulative recharge rewards"),

  /**
   * 周星.
   */
  WEEK_STAR("Week star"),

  /**
   * 房间奖励.
   */
  ROOM_REWARD("Room reward"),

  /**
   * 爆水晶游戏.
   */
  GAME_BURST_CRYSTAL("Game burst crystal"),

  /**
   * 首次充值.
   */
  FIRST_CHARGE_REWARD("First charge reward"),

  /**
   * 工会每周宝箱奖励领取
   */
  FAMILY_AWARD_RECEIVE("Family award receive"),

  /**
   * 每周CP相互赠送礼物榜单.
   */
  WEEK_CP_GIFT("Week cp gift"),

  /**
   * 房间PK奖励
   */
  ROOM_PK("Room PK"),

  /**
   * King国王王后.
   */
  WEEK_USER_CONSUME("Week user consume"),

  /**
   * 宠物奖励.
   */
  PET_REWARD("Pet reward"),

  /**
   * 宠物转盘抽奖.
   */
  PET_TURNTABLE_LOTTERY("Pet turntable lottery"),

  /**
   * 砸金蛋.
   */
  EGG("Egg"),

  /**
   * 扑克游戏.
   */
  POKER("Poker"),

  /**
   * 骰子游戏.
   */
  DICE("Dice"),

  /**
   * 游戏之王活动.
   */
  GAME_KING("GAME_KING"),

  /**
   * 每周国王.
   */
  WEEK_KING("Week King"),

  /**
   * 每周王后.
   */
  WEEK_QUEEN("Week Queen"),

  /**
   * 游戏王奖励.
   */
  GAME_KING_AWARD("Game King Award"),

  /**
   * 每日签到抽奖
   */
  DAILY_REGISTER("Daily sign-in raffle"),

  /**
   * 邀请用户奖励
   */
  INVITE_USER_REWARDS("Invite User Rewards"),

  /**
   * 炸金花.
   */
  BET_TEEN_PATTI("Bet Teen Patti"),

  /**
   * 每周游戏任务奖励.
   */
  WEEKLY_GAME_TASKS("Weekly game tasks"),

  /**
   * 每周最佳特殊关系奖励.
   */
  ACTIVITY_FRIENDSHIP_CARD_REWARDS("Best Special Relationship Award"),

  /**
   * 收款单据.
   */
  COLLECTION_RECEIPT("Collection receipt"),

  /**
   * 主播代理系统奖励.
   */
  HOST_AGENT_SYSTEM_REWARD("Rewards for the host agent system"),

  /**
   * 政策奖励.
   */
  POLICY_REWARDS("Policy rewards");


  private final String desc;

  GameCouponOrigin(String desc) {
    this.desc = desc;
  }

  public String getDesc() {
    return desc;
  }
}
