package com.red.circle.wallet.inner.model.enums;

import com.red.circle.tool.core.text.StringUtils;

/**
 * 金币来源.
 *
 * <AUTHOR> on 2021/8/20
 */
public enum GoldOrigin {

  /**
   * 补偿.
   */
  COMPENSATE("Compensate"),

  /**
   * 活动奖励.
   */
  ACTIVITY_REWARD("Activity reward"),

  /**
   * 男性用户注册.
   */
  @Deprecated
  MALE_USER_REGISTER("Male user registration"),

  /**
   * 举报成功补偿1颗.
   */
  VIDEO_VIOLATION_ONE("Report success"),

  /**
   * 赠送礼物.
   */
  GIVE_GIFT("Give a gift"),

  /**
   * 自定义主题.
   */
  CUSTOM_ROOM_THEME("Custom theme"),

  /**
   * 加入房间.
   */
  JOIN_ROOM("Join room"),

  /**
   * 水果游戏.
   */
  GAME_SLOT_MACHINE("Game Fruit"),

  BLESSINGS_GIFT("[BlessingsGift] Give Gift"),

  /**
   * 烧烤游戏.
   */
  GAME_BARBECUE("Game Barbecue"),

  /**
   * 转盘游戏.
   */
  TURNTABLE_GAME("Turntable game"),

  /**
   * ludo游戏.
   */
  LUDO_GAME("Ludo game"),

  /**
   * 购买靓号-金币.
   */
  PURCHASE_GOLD_SPECIAL_ID("[Gold]Purchase special id"),

  /**
   * 赠送靓号-金币.
   */
  PURCHASE_GOLD_SPECIAL_ID_GIVEAWAY("[Gold]Purchase special id,Give away"),

  /**
   * 购买贵族vip-金币.
   */
  PURCHASE_NOBLE_VIP("[Gold]Purchase aristocratic VIP"),

  /**
   * 赠送贵族VIP-金币.
   */
  PURCHASE_NOBLE_VIP_GIVEAWAY("[Gold]Purchase aristocratic VIP,Give away"),

  /**
   * 购买头像框-金币
   */
  PURCHASING_AVATAR_FRAME("[Gold]Buy avatar frame"),

  /**
   * 购买资料卡.
   */
  PURCHASING_DATA_CARD("[Gold]Buy data card"),

  /**
   * 购买聊天气泡-金币
   */
  PURCHASING_CHAT_BUBBLE("[Gold]Buy chat bubble"),

  /**
   * 购买车辆-金币
   */
  PURCHASING_CAR("[Gold]Buy car"),

  /**
   * 购买装扮-金币
   */
  PURCHASING_LAYOUT("[Gold]Buy layout"),

  /**
   * 赠送头像框-金币
   */
  PURCHASING_AVATAR_FRAME_GIVEAWAY("[Gold]Giveaway avatar frame"),

  /**
   * 赠送资料卡.
   */
  PURCHASING_DATA_CARD_GIVEAWAY("[Gold]Giveaway data card"),

  /**
   * 赠送聊天气泡-金币
   */
  PURCHASING_CHAT_BUBBLE_GIVEAWAY("[Gold]Giveaway chat bubble"),

  /**
   * 赠送车辆-金币
   */
  PURCHASING_CAR_GIVEAWAY("[Gold]Giveaway car"),

  /**
   * 赠送装扮-金币
   */
  GIVE_AWAY_GOLD_LAYOUT("[Gold]Giveaway layout"),

  /**
   * 发送红包.
   */
  SEND_RED_PACKET("Send Luck Bag"),

  /**
   * 发送用户个人红包.
   */
  SEND_USER_RED_PACKET("Send User Luck Bag"),

  /**
   * 发送工会群聊红包.
   */
  SEND_FAMILY_GROUP_RED_PACKET("Send Family Luck Bag"),

  /**
   * 购买房间锁.
   */
  PURCHASE_ROOM_LOCK("Purchase a room lock"),

  /**
   * 购买隐身浏览道具.
   */
  PURCHASE_STEALTH_BROWSE_PROPS("Purchase a stealth browse props"),

  /**
   * CP组建.
   */
  CP_BUILD("cp build"),

  /**
   * lucky box Game.
   */
  LUCKY_BOX_GAME("Lucky Box Game"),

  /**
   * ludo胜利.
   */
  LUDO_VICTORY("Ludo victory"),

  /**
   * ludo退款.
   */
  LUDO_REFUND("Ludo Refund"),

  /**
   * 每日任务.
   */
  DAILY_TASK("Daily task"),

  /**
   * cp奖励.
   */
  CP_REWARD("CP Reward"),

  /**
   * 货运代理.
   */
  SHIPPING_AGENT("shipping agent"),

  /**
   * 爆水晶游戏.
   */
  GAME_BURST_CRYSTAL("Game burst crystal"),

  /**
   * 累计充值奖励.
   */
  CUMULATIVE_RECHARGE_REWARDS("Cumulative recharge rewards"),

  /**
   * 房间奖励.
   */
  ROOM_REWARD("Room reward"),

  /**
   * 周星.
   */
  WEEK_STAR("Week Star"),

  /**
   * web端购买.
   */
  WEB_BUY("Web buy"),

  /**
   * 房间奖励提取.
   */
  ROOM_REWARD_EXTRACT("Room reward withdrawal"),

  /**
   * 签到获取奖励.
   */
  SEVEN_CHECK_IN("Sign-in reward"),

  /**
   * 游戏退还.
   */
  GAME_BACK("game back"),

  /**
   * 游戏胜利.
   */
  GAME_WINNING("game winning"),

  /**
   * 游戏抽成.
   */
  GAME_RAKE("game rake"),

  /**
   * 礼包.
   */
  GIFT_PACK("Gift pack"),

  /**
   * 购买金币.
   */
  BUY_GOLD("Buy gold coins"),

  /**
   * 朋友赠送金币.
   */
  FRIENDS_GIVE_GOLD_COINS("Friends give gold coins"),

  /**
   * 邀请用户注册奖励.
   */
  INVITED_NEW_USER_REWARD("Invite register"),

  /**
   * 邀请的用户首次充值奖励
   */
  INVITED_USER_FIRST_RECHARGE_REWARD("First recharge"),

  /**
   * 领取红包.
   */
  RECEIVE_RED_PACKET("Receive red packet"),

  /**
   * 领取用户个人红包.
   */
  RECEIVE_USER_RED_PACKET("Receive User red packet"),

  /**
   * 领取工会群聊红包.
   */
  RECEIVE_FAMILY_GROUP_RED_PACKET("Receive family red packet"),

  /**
   * 红包退款.
   */
  RECEIVE_RED_PACKET_REFUND("Receive red packet Refund"),

  /**
   * 用户个人红包退款.
   */
  RECEIVE_USER_RED_PACKET_REFUND("Receive User red packet Refund"),

  /**
   * 工会群聊红包退款.
   */
  RECEIVE_FAMILY_GROUP_RED_PACKET_REFUND("Receive Family red packet Refund"),

  /**
   * 接收礼物.
   */
  ACCEPT_GIFT("Accept gift"),

  /**
   * 用户首次充值奖励.
   */
  USER_FIRST_RECHARGE_REWARD("User First recharge"),

  /**
   * 使用金币创建工会.
   */
  GOLD_CREATE_FAMILY("[Family] Use gold coins to create a family"),

  /**
   * 工会每周宝箱奖励领取.
   */
  FAMILY_AWARD_RECEIVE("[Family] Receive family weekly treasure chest rewards"),

  /**
   * 首次充值.
   */
  FIRST_CHARGE_REWARD("Recharge for the first time"),

  /**
   * 砖石兑换金币.
   */
  DIAMOND_EXCHANGE_GOLD("Exchange Diamonds for Gold Coins"),

  /**
   * 盖房子消费.
   */
  BUILD_HOUSE("Build house"),

  /**
   * 加入汽车旅行.
   */
  JOIN_CAR_TRAVEL("join car travel"),

  /**
   * 开通vip.
   */
  OPEN_VIP("Open VIP"),

  /**
   * 购买主题-金币.
   */
  PURCHASING_THEME("[gold] Purchasing props themes"),

  /**
   * 购买主题-赠送.
   */
  PURCHASING_THEME_GIVEAWAY("[gold] Purchasing props themes Giveaway"),

  /**
   * 赠送道具主题-金币.
   */
  PURCHASING_LAYOUT_GIVEAWAY("[Gold]Giveaway layout"),

  /**
   * cp礼物互赠消耗
   */
  WEEK_CP_GIFT("[Activity] CP Ranking"),

  /**
   * 房间PK奖励
   */
  ROOM_PK("[PK] Room PK reward"),

  /**
   * 喂养宠物.
   */
  FEEDING_PETS("Feeding pets"),

  /**
   * 帮助他人喂养.
   */
  FEEDING_PETS_HELP_OTHERS("Help others to feed their pets"),

  /**
   * 激活宠物.
   */
  ACTIVATE_PET("Activate pet"),

  /**
   * 宠物奖励.
   */
  PET_REWARD("Pet reward"),

  /**
   * 豆子转换金币.
   */
  BEAN_TO_GOLD("Bean to gold"),

  /**
   * 宠物转盘抽奖.
   */
  PET_TURNTABLE_LOTTERY("Pet Turntable Lottery"),

  /**
   * 砸金蛋.
   */
  EGG("Smash the golden egg"),

  /**
   * 多品种水果游戏.
   */
  @Deprecated
  DOUBLE_LAYER_FRUIT("[Game] Many types fruit "),

  /**
   * 多品种烧烤.
   */
  @Deprecated
  DOUBLE_LAYER_BARBECUE("[Game] Many types BBQ"),

  /**
   * 水果游戏.
   */
  FRUIT_GAME("fruit game"),

  /**
   * 每周国王.
   */
  WEEK_KING("Week King"),

  /**
   * 每周王后.
   */
  WEEK_QUEEN("Week Queen"),

  /**
   * 游戏王奖励.
   */
  GAME_KING_AWARD("Game King Award"),

  /**
   * 每日签到抽奖
   */
  DAILY_REGISTER("Daily sign-in raffle"),
//
//  /**
//   * 幸运礼物奖励.
//   */
//  LUCKY_GIFT_REWARD("Lucky Gift Reward"),

  /**
   * 四月活动五福碎片.
   */
  APRIL_ACTIVITY_FRAGMENT("Lucky Reward"),

  /**
   * 累计充值抽奖.
   */
  CUMULATIVE_RECHARGE_LOTTERY("Cumulative recharge raffle prizes"),

  /**
   * 幸运彩票押注.
   */
  LOTTERY_NUMBER_BET("Divide gold coins"),

  /**
   * ludo游戏退款.
   */
  GAME_LUDO_REFUND("Game Ludo Refund"),

  /**
   * 邀请用户奖励
   */
  INVITE_USER_REWARDS("Invite User Rewards"),

  /**
   * 幸运礼物金币奖励.
   */
  LUCKY_GIFT_GOLD_REWARD("[LUCKY_GIFT] Send Lucky Gift Gold Reward"),

  /**
   * 赠送用户友谊关系卡.
   */
  USER_FRIENDSHIP_CARD_GIVE_AWAY("Gift user friendship card"),

  /**
   * 用户友谊关系卡退款.
   */
  USER_FRIENDSHIP_CARD_REFUND("User Friendship Card Refund"),

  /**
   * sud游戏获胜.
   */
  USD_GAME_WIN("Usd Game win"),

  /**
   * sud游戏退款.
   */
  USD_GAME_REFUND("Usd Game Refund"),

  /**
   * sud游戏退款平局.
   */
  USD_GAME_REFUND_DRAW("Usd Game Refund draw"),

  /**
   * 炸金花游戏.
   */
  TEEN_PATTI("TeenPatti luck"),

  /**
   * 炸金花.
   */
  BET_TEEN_PATTI("Bet Teen Patti"),

  /**
   * 购买表情包.
   */
  BUY_EMOJI("Buy Emoji"),

  /**
   * 超限制发动态支付金币.
   */
  SEND_DYNAMIC_PAY_GOLD("Send a post to pay coins"),

  /**
   * 每周游戏任务奖励.
   */
  WEEKLY_GAME_TASKS("Weekly game tasks"),

  /**
   * 每周最佳特殊关系奖励.
   */
  ACTIVITY_FRIENDSHIP_CARD_REWARDS("Best Special Relationship Award"),

  /**
   * 用户退款.
   */
  USER_REFUND("User refund"),

  /**
   * 频道喇叭消费.
   */
  ROOM_SEND_TRUMPET("channel speaker"),

  /**
   * Hkys游戏.
   */
  HKYS_GAME("Hkys game"),

  /**
   * hostgame游戏.
   */
  HOT_GAME("Hot game"),

  /**
   * Baishun 游戏.
   */
  BAISHUN_GAME("Baishun game"),

  /**
   * 房间贡献活动奖励.
   */
  ROOM_CONTRIBUTION_REWARD("Room contribution activity reward"),

  /**
   * 奖励金币
   */
  REWARD_COINS("Reward coins"),

  /**
   * 扣除金币
   */
  DEDUCT_COINS("Deduct coins"),

  /**
   * 工资
   */
  WAGES("Wages"),

  /**
   * 收款单据.
   */
  COLLECTION_RECEIPT("Collection receipt"),

  /**
   * 主播代理系统奖励.
   */
  HOST_AGENT_SYSTEM_REWARD("Rewards for the host agent system"),

  /**
   * 代理主播数量激励奖励.
   */
  ACTIVE_AGENT_ANCHOR_COUNT_REWARD("Reward for the number of anchors meeting the standard"),

  /**
   * 代理名下主播累计月目标奖励.
   */
  ACTIVE_AGENT_MONTH_TARGET_REWARD("Cumulative monthly target completion award"),

  /**
   * 主播月目标奖励.
   */
  ACTIVE_ANCHOR_MONTH_TARGET_REWARD("Reward for anchor's monthly goal completion"),

  /**
   * 主播日目标奖励.
   */
  ACTIVE_ANCHOR_DAY_TARGET_REWARD("Award for completion of anchor's daily goal"),

  /**
   * 金币兑换.
   */
  BANK_GOLD_COIN_EXCHANGE("[Bank]Gold coin exchange"),

  /**
   * 幸运礼物.
   */
  LUCKY_GIFT("[LUCKY_GIFT]Send Lucky gift"),

  /**
   * 购买活动房间麦类型.
   */
  PURCHASE_ROOM_MIKE("Purchase a room mikeType"),
  /**
   * 购买房间麦数量.
   */
  PURCHASE_ROOM_MIKE_NUM("Purchase a room mikeNumber"),

  /**
   * 政策奖励.
   */
  POLICY_REWARDS("Policy rewards"),

  /**
   * 经销商卖家发货.
   */
  SELLER_AGENT("Seller agent"),

  /**
   * new lucky box Game.
   */
  NEW_LUCKY_BOX_GAME(" New Lucky Box Game"),

  /**
   * 目标兑换金币.
   */
  TARGET_EXCHANGE_GOLD("Target Exchange Gold Coins"),

  /**
   * 主播申请解约金币消费.
   */
  HOST_TERMINATION_FEE("Application for termination fee"),

  /**
   * 主播取消申请解约金币退还.
   */
  HOST_TERMINATION_FEE_RETURN("Refund of termination fee"),

  /**
   * 购买红包封面-金币
   */
  PURCHASING_RED_PACKET("[Gold]Buy red envelope cover"),

  /**
   * 赠送红包封面-金币
   */
  PURCHASING_RED_PACKET_GIVEAWAY("[Gold]Giveaway red envelope cover"),

  /**
   * 自定义主题.
   */
  CUSTOM_THEME("Custom theme"),

  /**
   * 摩天轮游戏
   */
  GAME_FRUIT_BET("Ferris wheel game bet"),

  /**
   * 摩天轮游戏
   */
  GAME_FRUIT_AWARD("Ferris wheel game award"),

  /**
   * 工会群聊付费解锁.
   */
  FAMILY_GROUP_CHAT_AMOUNT("Family group chat amount")
  ;

  private final String desc;

  GoldOrigin(String desc) {
    this.desc = desc;
  }

  public String getDesc() {
    return desc;
  }

  public static String matchSpecialDescByEventType(String eventType, String defaultDesc) {
    if (StringUtils.isBlank(eventType)) {
      return defaultDesc;
    }
    String desc = GoldOriginMapping.GAME_MAPPING.get(eventType);
    return StringUtils.isBlank(desc) ? defaultDesc : desc;
  }

  public static GoldOrigin toEnum(String key) {
    try {
      return GoldOrigin.valueOf(key);
    } catch (Exception ex) {
      // ignore
    }
    return null;
  }

}
