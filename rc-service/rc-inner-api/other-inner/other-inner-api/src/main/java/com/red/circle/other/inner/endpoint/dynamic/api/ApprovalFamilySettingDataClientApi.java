package com.red.circle.other.inner.endpoint.dynamic.api;

import com.red.circle.framework.dto.PageResult;
import com.red.circle.framework.dto.ResultResponse;
import com.red.circle.other.inner.model.cmd.approval.ApprovalFamilyCmd;
import com.red.circle.other.inner.model.cmd.approval.ApprovalProfileDescQryCmd;
import com.red.circle.other.inner.model.dto.approval.ApprovalFamilyDTO;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * 工会资料审批
 *
 * <AUTHOR> on 2024/1/20
 */
public interface ApprovalFamilySettingDataClientApi {

  String API_PREFIX = "/approval/family/client";

  @PostMapping("/pageFamilyApproval")
  ResultResponse<PageResult<ApprovalFamilyDTO>> pageFamilyApproval(
      @RequestBody ApprovalProfileDescQryCmd query);

  @PostMapping("/notPass")
  ResultResponse<Void> notPass(@RequestBody ApprovalFamilyCmd cmd);
}
