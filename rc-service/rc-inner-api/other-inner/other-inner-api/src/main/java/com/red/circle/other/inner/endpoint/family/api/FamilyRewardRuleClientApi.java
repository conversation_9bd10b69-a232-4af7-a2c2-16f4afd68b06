package com.red.circle.other.inner.endpoint.family.api;


import com.red.circle.framework.dto.PageResult;
import com.red.circle.framework.dto.ResultResponse;
import com.red.circle.other.inner.model.cmd.famliy.FamilyRewardRuleCmd;
import com.red.circle.other.inner.model.cmd.famliy.FamilyRewardRuleQryCmd;
import com.red.circle.other.inner.model.dto.famliy.FamilyRewardRuleDTO;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * 工会奖励规则服务.
 *
 * <AUTHOR> on 2023/6/5
 */
public interface FamilyRewardRuleClientApi {

  String API_PREFIX = "/family/reward-rule";


  @PostMapping("/pageData")
  ResultResponse<PageResult<FamilyRewardRuleDTO>> pageData(
      @RequestBody FamilyRewardRuleQryCmd queryWhere);

  @PostMapping("/save")
  ResultResponse<Void> save(@RequestBody FamilyRewardRuleCmd param);
}
