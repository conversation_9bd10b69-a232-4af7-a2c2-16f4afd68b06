package com.red.circle.other.inner.endpoint.material.props.api;

import com.red.circle.framework.dto.PageResult;
import com.red.circle.framework.dto.ResultResponse;
import com.red.circle.other.inner.model.cmd.material.PropsResourcesMergeCmd;
import com.red.circle.other.inner.model.cmd.material.PropsSourceRecordQryCmd;
import com.red.circle.other.inner.model.dto.material.PropsRedPacketSkinDTO;
import com.red.circle.other.inner.model.dto.material.PropsResourcesDTO;
import com.red.circle.other.inner.model.dto.material.PropsResourcesMergeDTO;
import java.util.List;
import java.util.Map;
import java.util.Set;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * 道具资源.
 *
 * <AUTHOR> on 2023/11/7
 */
public interface PropsSourceClientApi {

  String API_PREFIX = "/props/source/client";

  /**
   * 获取指定资源信息映射.
   */
  @PostMapping("/getById")
  ResultResponse<PropsResourcesDTO> getById(@RequestParam("id") Long id);

  /**
   * 获取指定一组资源信息映射.
   *
   * @param ids id集合
   * @return map
   */
  @PostMapping("/mapByIds")
  ResultResponse<Map<Long, PropsResourcesDTO>> mapByIds(@RequestBody Set<Long> ids);

  /**
   * 获取一批组合道具资源.
   */
  @PostMapping("/mapMergeByIds")
  ResultResponse<PropsResourcesMergeDTO> mapMergeByIds(@RequestBody PropsResourcesMergeCmd cmd);

  /**
   * 获取平台资源类型.
   */
  @GetMapping("/listSysOrigin")
  ResultResponse<List<PropsResourcesDTO>> listSysOrigin(
      @RequestParam("sysOrigin") String sysOrigin, @RequestParam("type") String type);

  /**
   * 获取指定资源类型，不包含工会.
   */
  @GetMapping("/listExcludeFamily")
  ResultResponse<List<PropsResourcesDTO>> listExcludeFamily(
      @RequestParam("sysOrigin") String sysOrigin,
      @RequestParam("sourceId") String type
  );

  /**
   * 资源上/下架.
   */
  @GetMapping("/offShelfPropsResources")
  ResultResponse<Void> offShelfPropsResources(
      @RequestParam("id") Long id,
      @RequestParam("offShelf") Boolean offShelf
  );

  /**
   * 道具资源分页列表(运营后台).
   */
  @PostMapping("/pagePropsResourcesOps")
  ResultResponse<PageResult<PropsResourcesDTO>> pagePropsResourcesOps(
      @RequestBody PropsSourceRecordQryCmd qryCmd);

  /**
   * 添加或修改道具资源.
   */
  @PostMapping("/addOrUpdatePropsResources")
  ResultResponse<Void> addOrUpdatePropsResources(@RequestBody PropsResourcesDTO dto);

  /**
   * 获得红包道具细节皮肤.
   */
  @GetMapping("/getPropsRedPacketSkin")
  ResultResponse<PropsRedPacketSkinDTO> getPropsRedPacketSkin(@RequestParam("id") Long id);

}
