package com.red.circle.other.inner.endpoint.family;

import com.red.circle.other.inner.endpoint.family.api.FamilyCreateRulesClientApi;
import org.springframework.cloud.openfeign.FeignClient;

/**
 * 工会创建规则表服务.
 *
 * <AUTHOR> on 2023/6/5
 */
@FeignClient(name = "familyCreateRulesClient", url = "${feign.other.url}" +
    FamilyCreateRulesClientApi.API_PREFIX)
public interface FamilyCreateRulesClient extends FamilyCreateRulesClientApi {

}
