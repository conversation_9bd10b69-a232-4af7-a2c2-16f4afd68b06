package com.red.circle.other.inner.endpoint.family;

import com.red.circle.other.inner.endpoint.family.api.FamilyLevelConfigClientApi;
import org.springframework.cloud.openfeign.FeignClient;

/**
 * 工会创建规则表服务.
 *
 * <AUTHOR> on 2023/6/5
 */
@FeignClient(name = "familyLevelConfigClient", url = "${feign.other.url}" +
    FamilyLevelConfigClientApi.API_PREFIX)
public interface FamilyLevelConfigClient extends FamilyLevelConfigClientApi {

}
