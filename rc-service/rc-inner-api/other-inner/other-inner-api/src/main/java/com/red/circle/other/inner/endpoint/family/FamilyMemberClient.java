package com.red.circle.other.inner.endpoint.family;

import com.red.circle.other.inner.endpoint.family.api.FamilyMemberClientApi;
import org.springframework.cloud.openfeign.FeignClient;

/**
 * 工会成员表服务.
 *
 * <AUTHOR> on 2023/6/5
 */
@FeignClient(name = "familyMemberClient", url = "${feign.other.url}" +
    FamilyMemberClientApi.API_PREFIX)
public interface FamilyMemberClient extends FamilyMemberClientApi {

}
