package com.red.circle.other.inner.endpoint.family;

import com.red.circle.other.inner.endpoint.family.api.FamilyMemberWeekAwardClientApi;
import org.springframework.cloud.openfeign.FeignClient;

/**
 * 工会成员每周领奖.
 *
 * <AUTHOR> on 2023/11/16
 */
@FeignClient(name = "familyMemberWeekAwardClient", url = "${feign.other.url}" +
    FamilyMemberWeekAwardClientApi.API_PREFIX)
public interface FamilyMemberWeekAwardClient extends FamilyMemberWeekAwardClientApi {

}
