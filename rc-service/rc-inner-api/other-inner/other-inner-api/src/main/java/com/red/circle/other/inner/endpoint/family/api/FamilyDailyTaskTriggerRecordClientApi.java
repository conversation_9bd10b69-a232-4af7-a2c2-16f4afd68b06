package com.red.circle.other.inner.endpoint.family.api;

import com.red.circle.framework.dto.ResultResponse;
import org.springframework.web.bind.annotation.GetMapping;

/**
 * 工会每日任务触发记录.
 *
 * <AUTHOR> on 2023/10/22
 */
public interface FamilyDailyTaskTriggerRecordClientApi {

  String API_PREFIX = "/family/daily/task/trigger/record/client";

  /**
   * 删除所有任务记录.
   */
  @GetMapping("/deleteAll")
  ResultResponse<Void> deleteAll();

}
