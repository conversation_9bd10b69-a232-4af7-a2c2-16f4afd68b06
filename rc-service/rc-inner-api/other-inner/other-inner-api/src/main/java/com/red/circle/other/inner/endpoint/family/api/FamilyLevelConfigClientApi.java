package com.red.circle.other.inner.endpoint.family.api;


import com.red.circle.framework.dto.PageResult;
import com.red.circle.framework.dto.ResultResponse;
import com.red.circle.other.inner.model.cmd.famliy.FamilyLevelConfigCmd;
import com.red.circle.other.inner.model.cmd.famliy.FamilyLevelConfigQryCmd;
import com.red.circle.other.inner.model.dto.famliy.FamilyLevelConfigDTO;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * 工会创建规则表服务.
 *
 * <AUTHOR> on 2023/6/5
 */
public interface FamilyLevelConfigClientApi {

  String API_PREFIX = "/family/level-config";

  @PostMapping("/pageData")
  ResultResponse<PageResult<FamilyLevelConfigDTO>> pageData(
      @RequestBody FamilyLevelConfigQryCmd queryWhere);

  @PostMapping("/saveFamilyLevelConfig")
  ResultResponse<Void> saveFamilyLevelConfig(@RequestBody FamilyLevelConfigCmd cmd);
}
