package com.red.circle.other.inner.endpoint.family;

import com.red.circle.other.inner.endpoint.family.api.FamilyDailyTaskTriggerRecordClientApi;
import org.springframework.cloud.openfeign.FeignClient;

/**
 * 工会每日任务触发记录.
 *
 * <AUTHOR> on 2023/10/22
 */
@FeignClient(name = "familyDailyTaskTriggerRecordClient", url = "${feign.other.url}" +
    FamilyDailyTaskTriggerRecordClientApi.API_PREFIX)
public interface FamilyDailyTaskTriggerRecordClient extends FamilyDailyTaskTriggerRecordClientApi {

}
