package com.red.circle.other.inner.endpoint.family.api;

import com.red.circle.framework.dto.ResultResponse;
import com.red.circle.other.inner.model.dto.famliy.FamilyDetailsDTO;
import com.red.circle.other.inner.model.dto.famliy.FamilyMemberInfoDTO;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * 工会服务.
 *
 * <AUTHOR> on 2023/6/5
 */
public interface FamilyClientApi {

  String API_PREFIX = "/family/client";

  /**
   * 获取工会成员.
   */
  @GetMapping("/geMemberByUserId")
  ResultResponse<FamilyMemberInfoDTO> geMemberByUserId(@RequestParam("userId") Long userId);

  /**
   * 工会详情.
   */
  @GetMapping("/getDetails")
  ResultResponse<FamilyDetailsDTO> getDetails(
      @RequestParam("sysOrigin") String sysOrigin,
      @RequestParam("familyId") Long familyId
  );

}
