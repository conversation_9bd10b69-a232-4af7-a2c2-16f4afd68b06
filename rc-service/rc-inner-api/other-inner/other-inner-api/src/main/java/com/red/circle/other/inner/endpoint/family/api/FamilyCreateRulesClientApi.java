package com.red.circle.other.inner.endpoint.family.api;


import com.red.circle.framework.dto.PageResult;
import com.red.circle.framework.dto.ResultResponse;
import com.red.circle.other.inner.model.cmd.famliy.FamilyCreateRuleCmd;
import com.red.circle.other.inner.model.cmd.famliy.FamilyCreateRuleQryCmd;
import com.red.circle.other.inner.model.dto.famliy.FamilyCreateRuleDTO;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * 工会创建规则表服务.
 *
 * <AUTHOR> on 2023/6/5
 */
public interface FamilyCreateRulesClientApi {

  String API_PREFIX = "/family/create-rules/client";

  @PostMapping("/pageData")
  ResultResponse<PageResult<FamilyCreateRuleDTO>> pageData(
      @RequestBody FamilyCreateRuleQryCmd queryWhere);

  @PostMapping("/saveFamilyCreateRules")
  ResultResponse<Void> saveFamilyCreateRules(@RequestBody FamilyCreateRuleCmd cmd);
}
