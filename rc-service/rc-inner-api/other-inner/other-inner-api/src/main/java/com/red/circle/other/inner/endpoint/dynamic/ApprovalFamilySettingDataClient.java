package com.red.circle.other.inner.endpoint.dynamic;

import com.red.circle.other.inner.endpoint.dynamic.api.ApprovalFamilySettingDataClientApi;
import org.springframework.cloud.openfeign.FeignClient;

/**
 * 工会资料审批
 *
 * <AUTHOR> on 2024/1/20
 */
@FeignClient(name = "approvalFamilySettingDataClient", url = "${feign.other.url}" +
    ApprovalFamilySettingDataClientApi.API_PREFIX)
public interface ApprovalFamilySettingDataClient extends ApprovalFamilySettingDataClientApi {

}
