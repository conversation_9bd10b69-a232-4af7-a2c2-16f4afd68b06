package com.red.circle.other.inner.endpoint.family.api;

import com.red.circle.framework.dto.PageResult;
import com.red.circle.framework.dto.ResultResponse;
import com.red.circle.other.inner.model.cmd.famliy.FamilyBaseInfoQryCmd;
import com.red.circle.other.inner.model.dto.famliy.FamilyBaseInfoDTO;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * 工会基础信息服务.
 *
 * <AUTHOR> on 2023/6/5
 */
public interface FamilyBaseInfoClientApi {

  String API_PREFIX = "/family/base-info";

  @PostMapping("/pageData")
  ResultResponse<PageResult<FamilyBaseInfoDTO>> pageData(
      @RequestBody FamilyBaseInfoQryCmd queryWhere);

  @GetMapping("/delFamily")
  ResultResponse<Void> delFamily(@RequestParam("familyId") Long familyId);
}
