package com.red.circle.other.inner.endpoint.family.api;


import com.red.circle.framework.dto.PageResult;
import com.red.circle.framework.dto.ResultResponse;
import com.red.circle.other.inner.model.cmd.famliy.FamilyMemberQryCmd;
import com.red.circle.other.inner.model.dto.famliy.FamilyMemberDTO;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * 工会成员表服务.
 *
 * <AUTHOR> on 2023/6/5
 */
public interface FamilyMemberClientApi {

  String API_PREFIX = "/family/member";

  @PostMapping("/pageData")
  ResultResponse<PageResult<FamilyMemberDTO>> pageData(
      @RequestBody FamilyMemberQryCmd queryWhere);

  @GetMapping("/delMember")
  ResultResponse<Void> delMember(@RequestParam("familyId") Long familyId,
      @RequestParam("memberId") Long memberId);
}
