package com.red.circle.other.inner.endpoint.family.api;

import com.red.circle.framework.dto.ResultResponse;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * 工会成员每周领奖.
 *
 * <AUTHOR> on 2023/11/16
 */
public interface FamilyMemberWeekAwardClientApi {

  String API_PREFIX = "/family/member/week/award/record/client";

  /**
   * 清理：每周清空贡献值&每周领奖记录.
   */
  @GetMapping("/deleteAllWeekActivity")
  ResultResponse<Void> deleteAllWeekActivity();

  /**
   * 工会周奖励开始结束时间.
   */
  @GetMapping("/setFamilyWeekRewardTime")
  ResultResponse<Void> setFamilyWeekRewardTime(@RequestParam("dateStr") String dateStr);

}
