package com.red.circle.other.inner.endpoint.family;

import com.red.circle.other.inner.endpoint.family.api.FamilyBaseInfoClientApi;
import org.springframework.cloud.openfeign.FeignClient;

/**
 * 工会基础信息服务.
 *
 * <AUTHOR> on 2023/6/5
 */
@FeignClient(name = "familyBaseInfoClient", url = "${feign.other.url}" +
    FamilyBaseInfoClientApi.API_PREFIX)
public interface FamilyBaseInfoClient extends FamilyBaseInfoClientApi {

}
