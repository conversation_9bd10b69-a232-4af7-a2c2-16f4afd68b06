package com.red.circle.other.inner.endpoint.family;

import com.red.circle.other.inner.endpoint.family.api.FamilyRewardRuleClientApi;
import org.springframework.cloud.openfeign.FeignClient;

/**
 * 工会奖励规则服务.
 *
 * <AUTHOR> on 2023/6/5
 */
@FeignClient(name = "familyRewardRuleClient", url = "${feign.other.url}" +
    FamilyRewardRuleClientApi.API_PREFIX)
public interface FamilyRewardRuleClient extends FamilyRewardRuleClientApi {

}
