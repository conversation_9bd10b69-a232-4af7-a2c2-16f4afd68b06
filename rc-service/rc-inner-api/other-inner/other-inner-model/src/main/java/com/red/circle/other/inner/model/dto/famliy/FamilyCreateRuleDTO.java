package com.red.circle.other.inner.model.dto.famliy;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import java.io.Serializable;
import java.math.BigDecimal;
import java.sql.Timestamp;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 工会创建规则表.
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-28
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class FamilyCreateRuleDTO implements Serializable {

  private static final long serialVersionUID = 1L;

  /**
   * 主键标识.
   */
  @JsonSerialize(using = ToStringSerializer.class)
  private Long id;

  /**
   * 来源系统.
   */
  private String sysOrigin;

  /**
   * 支付糖果.
   */
  private BigDecimal payCandy;

  /**
   * 用户财富等级.
   */
  private Integer userWealthLevel;

  /**
   * 创建时间.
   */
  private Timestamp createTime;

  /**
   * 修改时间.
   */
  private Timestamp updateTime;

}
