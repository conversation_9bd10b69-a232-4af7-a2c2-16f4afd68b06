package com.red.circle.other.inner.model.cmd.famliy;

import com.red.circle.framework.core.dto.CommonCommand;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.io.Serial;
import java.math.BigDecimal;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 工会创建规则表.
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-20
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class FamilyCreateRuleCmd extends CommonCommand {

  @Serial
  private static final long serialVersionUID = 1L;

  /**
   * 主键标识.
   */
  private Long id;

  /**
   * 来源系统.
   */
  @NotBlank(message = "请选择系统平台")
  private String sysOrigin;

  /**
   * 支付糖果.
   */
  @NotNull(message = "请填写创建工会费用")
  private BigDecimal payCandy;

  /**
   * 用户财富等级.
   */
  @NotNull(message = "请填写用户魅力等级")
  private Integer userWealthLevel;

}
