package com.red.circle.other.inner.model.cmd.famliy;

import com.red.circle.framework.core.dto.PageCommand;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 工会创建规则.
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-20
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Accessors(chain = true)
public class FamilyCreateRuleQryCmd extends PageCommand {

  private static final long serialVersionUID = 1L;

  /**
   * 主键标识.
   */
  private Long id;

  /**
   * 来源系统.
   */
  private String sysOrigin;


}
