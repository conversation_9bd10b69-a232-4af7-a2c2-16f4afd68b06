package com.red.circle.other.inner.model.cmd.famliy;

import com.red.circle.framework.core.dto.CommonCommand;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.io.Serial;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 工会奖励规则表.
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-20
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class FamilyRewardRuleCmd extends CommonCommand {

  @Serial
  private static final long serialVersionUID = 1L;

  /**
   * 主键标识.
   */
  private Long id;

  /**
   * 来源系统.
   */
  @NotBlank(message = "请选择系统平台")
  private String sysOrigin;

  /**
   * 要求经验值.
   */
  @NotNull(message = "请填写目标贡献值")
  private Integer goalExp;

  /**
   * 奖励金币数量.
   */
  @NotNull(message = "请填写奖励金币数量")
  private Integer rewardQuantity;

  /**
   * 规则描述.
   */
  private String description;

  /**
   * 顺序.
   */
  @NotNull(message = "请填写奖励顺序")
  private Integer sort;

}
