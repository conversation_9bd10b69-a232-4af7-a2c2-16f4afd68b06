package com.red.circle.other.inner.model.cmd.famliy;

import com.red.circle.framework.core.dto.CommonCommand;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.io.Serial;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 工会等级配置表.
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-20
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class FamilyLevelConfigCmd extends CommonCommand {

  @Serial
  private static final long serialVersionUID = 1L;

  /**
   * 主键标识.
   */
  private Long id;

  /**
   * 来源系统.
   */
  @NotBlank(message = "请选择系统平台")
  private String sysOrigin;

  /**
   * 等级key.
   */
  private String levelKey;

  /**
   * 头像框ID.
   */
  private Long avatarFrameId;

  /**
   * 徽章ID.
   */
  @NotNull(message = "请选择徽章")
  private Long badgeId;

  /**
   * 礼物ID.
   */
  private Long giftId;

  /**
   * 等级背景图.
   */
  private String levelBackgroundPicture;

  /**
   * 等级经验值.
   */
  @NotNull(message = "请输入等级贡献值")
  private Integer levelExp;

  /**
   * 最大成员数.
   */
  @NotNull(message = "请输入最大成员数")
  private Integer maxMember;

  /**
   * 最大管理员数.
   */
  @NotNull(message = "请输入最大管理员数")
  private Integer maxManager;

  /**
   * 等级顺序.
   */
  @NotNull(message = "请输入顺序")
  private Integer sort;

  /**
   * 等级经验值.
   */
  @NotBlank(message = "请选择等级类型")
  private String levelType;


}
