package com.red.circle.other.inner.enums.sys;

import java.util.Arrays;
import java.util.Objects;

/**
 * 徽章类型枚举.
 *
 * <AUTHOR> on 2021/3/19
 */
public enum SysBadgeConfigTypeEnum {

  /**
   * 成就.
   */
  ACHIEVEMENT("ACHIEVEMENT", "用户-成就"),

  /**
   * 管理员.
   */
  ADMINISTRATOR("ADMINISTRATOR", "用户-管理员"),

  /**
   * 活动.
   */
  ACTIVITY("ACTIVITY", "用户-活动"),

  /**
   * 工会-徽章.
   */
  FAMILY("FAMILY", "工会-徽章"),

  /**
   * 房间成就.
   */
  ROOM_ACHIEVEMENT("ROOM_ACHIEVEMENT", "房间-成就");

  private final String desc;
  private final String name;

  SysBadgeConfigTypeEnum(String name, String desc) {
    this.name = name;
    this.desc = desc;
  }

  public static String getDescValue(String name) {
    return Arrays.stream(SysBadgeConfigTypeEnum.values())
        .filter(sysDictionaryTypeEnum -> Objects.equals(sysDictionaryTypeEnum.getName(), name))
        .findFirst()
        .map(SysBadgeConfigTypeEnum::getDesc)
        .orElse("未知");
  }

  public String getDesc() {
    return desc;
  }

  public String getName() {
    return name;
  }
}
