package com.red.circle.other.inner.model.cmd.famliy;

import com.red.circle.common.business.dto.cmd.HistoryRangeTimeQryPageCmd;
import java.io.Serial;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 工会.
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-20
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Accessors(chain = true)
public class FamilyBaseInfoQryCmd extends HistoryRangeTimeQryPageCmd {

  @Serial
  private static final long serialVersionUID = 1L;


  /**
   * 来源系统.
   */
  private String sysOrigin;

  /**
   * 主键标识.
   */
  private Long id;

  /**
   * 工会账号.
   */
  private Long familyAccount;

  /**
   * 族长ID.
   */
  private Long userId;

}
