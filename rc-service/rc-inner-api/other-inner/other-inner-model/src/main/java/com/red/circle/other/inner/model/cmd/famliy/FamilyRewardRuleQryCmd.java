package com.red.circle.other.inner.model.cmd.famliy;

import com.red.circle.framework.core.dto.PageCommand;
import java.io.Serial;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 工会奖励规则表.
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-20
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class FamilyRewardRuleQryCmd extends PageCommand {

  @Serial
  private static final long serialVersionUID = 1L;

  /**
   * 主键标识.
   */
  private Long id;

  /**
   * 来源系统.
   */
  private String sysOrigin;


}
