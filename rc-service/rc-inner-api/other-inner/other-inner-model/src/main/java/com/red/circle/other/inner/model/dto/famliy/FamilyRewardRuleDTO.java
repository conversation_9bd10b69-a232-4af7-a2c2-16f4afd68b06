package com.red.circle.other.inner.model.dto.famliy;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import java.io.Serial;
import java.io.Serializable;
import java.sql.Timestamp;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 工会奖励规则表.
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-20
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class FamilyRewardRuleDTO implements Serializable {

  @Serial
  private static final long serialVersionUID = 1L;

  /**
   * 主键标识.
   */
  @JsonSerialize(using = ToStringSerializer.class)
  private Long id;

  /**
   * 来源系统.
   */
  private String sysOrigin;

  /**
   * 要求经验值.
   */
  private Integer goalExp;

  /**
   * 奖励金币数量.
   */
  private Integer rewardQuantity;

  /**
   * 规则描述.
   */
  private String description;

  /**
   * 顺序.
   */
  private Integer sort;

  /**
   * 创建时间.
   */
  private Timestamp createTime;

  /**
   * 修改时间.
   */
  private Timestamp updateTime;

}
