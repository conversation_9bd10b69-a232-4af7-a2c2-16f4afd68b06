package com.red.circle.other.inner.model.dto.live;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.red.circle.other.inner.model.cmd.live.RunGameDTO;
import java.io.Serial;
import java.io.Serializable;
import java.sql.Timestamp;
import java.util.Objects;

import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.Accessors;
import lombok.experimental.FieldDefaults;

/**
 * 活跃语音房间.
 *
 * <AUTHOR> on 2020/12/11
 */
@Data
@Accessors(chain = true)
@FieldDefaults(level = AccessLevel.PRIVATE)
public class ActiveVoiceRoomDTO implements Serializable {

  @Serial
  private static final long serialVersionUID = 1L;

  /**
   * id.
   */
  @JsonSerialize(using = ToStringSerializer.class)
  Long id;

  /**
   * 时序ID每次创建刷新.
   */
  @JsonSerialize(using = ToStringSerializer.class)
  Long timeId;

  /**
   * 归属系统平台.
   */
  String sysOrigin;

  /**
   * 房主id.
   */
  @JsonSerialize(using = ToStringSerializer.class)
  Long userId;

  /**
   * 房间account.
   */
  String roomAccount;

  /**
   * 工会资料.
   */
  FamilyProfileDTO familyProfile;

  /**
   * 运行游戏.
   */
  RunGameDTO runGame;

  /**
   * SVip等级.
   */
  String superVipLevel;

  /**
   * 热门房间（true 不是热门， false 热门）.
   */
  Boolean hot;

  /**
   * 置顶房间.
   */
  Integer fixedWeights;

  /**
   * 国家code.
   */
  String countryCode;

  /**
   * 如果国家code是IA强制变更ID.适配印尼分国家
   */
  public void setCountryCode(String countryCode) {
    if (Objects.nonNull(countryCode) && Objects.equals(countryCode, "IA")) {
      this.countryCode = "ID";
    } else if (Objects.nonNull(countryCode) && Objects.equals(countryCode, "EY")) {
      this.countryCode = "EG";
    } else if (Objects.nonNull(countryCode) && Objects.equals(countryCode, "AB")) {
      this.countryCode = "AE";
    } else if (Objects.nonNull(countryCode) && Objects.equals(countryCode, "MB")) {
      this.countryCode = "MA";
    } else if (Objects.nonNull(countryCode) && Objects.equals(countryCode, "SP")) {
      this.countryCode = "SA";
    } else {
      this.countryCode = countryCode;
    }
  }

  /**
   * 国家名字.
   */
  String countryName;

  /**
   * 区域.
   */
  String region;

  /**
   * 在线数量.
   */
  Long onlineQuantity;

  /**
   * 权重.
   */
  Integer weights;

  /**
   * 过期时间.
   */
  Timestamp expiredTime;

  /**
   * 创建时间.
   */
  Timestamp createTime;

  /**
   * 修改时间.
   */
  Timestamp updateTime;

}
