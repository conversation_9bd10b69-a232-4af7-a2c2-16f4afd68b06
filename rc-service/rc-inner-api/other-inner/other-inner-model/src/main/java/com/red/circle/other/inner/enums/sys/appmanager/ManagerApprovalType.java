package com.red.circle.other.inner.enums.sys.appmanager;

/**
 * 管理员审批类型.
 *
 * <AUTHOR> on 2021/4/6
 */
public enum ManagerApprovalType {

  /**
   * 用户头像.
   */
  USER_AVATAR,

  /**
   * 用户昵称.
   */
  USER_NICKNAME,

  /**
   * 用户账户冻结.
   */
  USER_ACCOUNT_FREEZE,

  /**
   * 用户账户封存.
   */
  USER_ACCOUNT_ARCHIVE,

  /**
   * 房间名称.
   */
  ROOM_NICKNAME,

  /**
   * 房间封面.
   */
  ROOM_COVER,

  /**
   * 房间公告.
   */
  ROOM_NOTICE,

  /**
   * 解除账号所有封禁.
   */
  UNBLOCK_ACCOUNT,

  /**
   * 工会头像.
   */
  FAMILY_AVATAR,

  /**
   * 工会昵称.
   */
  FAMILY_NICKNAME,

  /**
   * 工会公告.
   */
  FAMILY_NOTICE;

  public static ManagerApprovalType covertStrategy(String name) {
    return ManagerApprovalType.valueOf(name.replace("STRATEGY_APPROVAL_", ""));
  }

}
