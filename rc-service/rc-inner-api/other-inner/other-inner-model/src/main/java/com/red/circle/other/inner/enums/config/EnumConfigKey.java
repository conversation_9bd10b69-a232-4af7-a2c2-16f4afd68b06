package com.red.circle.other.inner.enums.config;

import com.red.circle.common.business.core.enums.SysOriginPlatformEnum;

/**
 * 配置key.
 *
 * <AUTHOR> on 2023/6/16
 */
public enum EnumConfigKey {

  // =============================  基础默认配置 =============================
  /**
   * 用户默认头像.
   */
  DEFAULT_USER_AVATAR,
  /**
   * 违规图.
   */
  VIOLATION_PICTURE,
  /**
   * 积分默认图.
   */
  INTEGRAL_DEFAULT,
  /**
   * 房间默认封面.
   */
  DEFAULT_ROOM_COVER,

  /**
   * h5基础域名.
   */
  H5_DOMAIN_BASE_URL_V2,

  /**
   * h5基础域名. 新服务
   */
  H5_DOMAIN_BASE_URL_NEW_V2,

  // =============================  任务相关的 =============================
  /**
   * 发送红包.
   */
  SEND_LUCKY_BAG,

  /**
   * 完成转盘游戏.
   */
  TURNTABLE_GAME,

  /**
   * 完成抽奖.
   */
  LUCK_DRAW,

  /**
   * 在麦克风上.
   */
  ON_THE_MICROPHONE,

  /**
   * 房间赠送礼物.
   */
  ROOM_SEND_GIFTS,

  /**
   * 发送广播礼物.
   */
  ROOM_SEND_BROADCAST_GIFTS,

  /**
   * 获取新在房间成员.
   */
  GET_NEW_ROOM_MEMBER,

  /**
   * 每日赠送礼物.
   */
  DAILY_SEND_GIFT,

  /**
   * 每日赠送3个礼物.
   */
  DAILY_SEND_THREE_GIFT,

  /**
   * 每日赠送5个礼物.
   */
  DAILY_SEND_FIVE_GIFT,

  /**
   * 每日上麦10分钟.
   */
  DAILY_ONLINE_TEN_MINUTES,

  /**
   * 用户首次充值
   */
  USER_FIRST_CHARGE,

  // =============================  业务配置 =============================

  /**
   * 邀请用户注册..
   */
  INVITE_USER_REGISTER,

  /**
   * 自定义房间主题.
   */
  CUSTOM_ROOM_THEME,


  /**
   * 邀请新用户注册奖励.
   */
  INVITED_NEW_USER_REWARD,

  /**
   * 邀请用户首次充值奖励.
   */
  INVITED_USER_FIRST_RECHARGE_REWARD,

  /**
   * 邀请用户每笔充值奖励百分比.
   */
  INVITED_USER_EACH_RECHARGE,

  /**
   * 钻石兑换倍数.
   */
  DIAMOND_EXCHANGE_MULTIPLE,

  /**
   * 钻石最大兑换金币.
   */
  DIAMOND_EXCHANGE_MAX_GOLD,

  /**
   * 颗钻石=金币
   */
  DIAMOND_EQ_GOLD,

  /**
   * 播报金额.
   */
  BROADCAST_AMOUNT,
  YAHLLA_BROADCAST_AMOUNT,

  /**
   * 房间锁金额.
   */
  ROOM_LOCK_AMOUNT,

  /**
   * 活动房间特殊麦位解锁金额
   */
  ROOM_MIKE_AMOUNT_ASWAT,

  /**
   * 购买房间麦位数量较少解锁金额
   */
  ROOM_MIKE_NUM_MEDIUM_AMOUNT_ASWAT,

  /**
   * 购买房间麦位数量较多解锁金额
   */
  ROOM_MIKE_NUM_LARGE_AMOUNT_ASWAT,

  /**
   * 购买房间麦位数量较多解锁金额+1.
   */
  ROOM_MIKE_NUM_X_LARGE_AMOUNT,

  /**
   * 隐身道具金额.
   */
  PROPS_STEALTH_BROWSE_AMOUNT,

  /**
   * 服务通知账号.
   *  SERVER_NOTICE_ACCOUNT
   */

  /**
   * aswat-赠送金币礼物获得比率.
   */
  ASWAT_GIFT_GOLD_RATIO,

  /**
   * aswat-收到幸运礼物获得金币比例.
   */
  ASWAT_LUCKY_GIFT_GOLD_RATIO,

  /**
   * aswat-赠送金币礼物获得比率,自己送给自己.
   */
  ASWAT_GIFT_GOLD_RATIO_SELF,

  /**
   * yahlla-赠送金币礼物获得比率.
   */
  YAHLLA_GIFT_GOLD_RATIO,

  /**
   * yahlla-收到幸运礼物获得金币比例.
   */
  YAHLLA_LUCKY_GIFT_GOLD_RATIO,

  /**
   * yahlla-赠送金币礼物获得比率,自己送给自己.
   */
  YAHLLA_GIFT_GOLD_RATIO_SELF,

  /**
   * cp价格.
   */
  CP_PRICE,

  /**
   * Aswat 超级管理员ID.
   */
  ASWAT_ADMINS,

  /**
   * boos位置起步金额.
   */
  BOOS_STARTING_AMOUNT,

  /**
   * yahlla 豆子转金币汇率.
   */
  YAHLLA_BEN_TO_GOLD_RATE,

  /**
   * 瓜分金币押注金额.
   */
  LOTTERY_NUMBER_BET,

  /**
   * aswat水果游戏播报金额.
   */
  ASWAT_FRUIT_GAME_SWITCH,

  /**
   * Yahlla水果游戏播报金额.
   */
  YAHLLA_FRUIT_GAME_SWITCH,

  /**
   * 频道喇叭消费.
   */
  ROOM_SEND_TRUMPET,

  /**
   * YAHLLA 频道喇叭消费.
   */
  YAHLLA_ROOM_SEND_TRUMPET,

  /**
   * 发送频道喇叭消费要求等级.
   */
  ROOM_SEND_TRUMPET_LEVEL,

  /**
   * 高价值礼物墙要求金额.
   */
  HIGH_VALUE_GIFT_STANDARD,

  /**
   * 游戏飘窗金额.
   */
  GAME_BROADCAST_AMOUNT,

  /**
   * aswat 个人红包抽成比例 %.
   */
  ASWAT_USER_SV_USER_RED_PACKET_RATIO,

  /**
   * yahlla 个人红包抽成比例 %.
   */
  YAHLLA_USER_SV_USER_RED_PACKET_RATIO,

  /**
   * 幸运礼物飘窗倍数要求.
   */
  LUCKY_GIFT_POPUP_MULTIPLE,

  /**
   * 幸运礼物支持区域.
   */
  LUCKY_GIFT_SUPPORT_REGION,

  /**
   * 经销商充值给货运代理最低金额.
   */
  ASWAT_DEALER_RECHARGE,

  /**
   * 房间内发送红包数量
   */
  ASWAT_RED_PACKET_NUMBER,

  /**
   * 世界红包金额范围
   */
  ASWAT_WORLD_RED_PACKET_AMOUNT,

  /**
   * 国家红包金额范围
   */
  ASWAT_COUNTRY_RED_PACKET_AMOUNT,

  /**
   * 其他国家红包金额范围
   */
  ASWAT_OTHER_COUNTRY_RED_PACKET_AMOUNT,

  /**
   * 不发CP与代理奖励区域Code.
   */
  NOT_CP_AGENT_REWARD,

  /**
   * 房间内-快捷入口-游戏ID.
   */
  ROOM_SHORTCUT_GAME_ID,

  /**
   * 接受普通礼物目标比例.
   */
  ACCEPT_ORDINARY_GIFT_TARGET_RATIO,

  /**
   * 禁止消费(true = 不能消费).
   */
  SUSPEND_CONSUMPTION,

  /**
   * 工会群聊付费解锁.
   */
  FAMILY_GROUP_CHAT_AMOUNT,

  /**
   * 隐身进入房间, 后续要改成管理员列表里面的用户权益.
   */
  ENTER_ROOM_INVISIBLE_USER,

  /**
   * 朝拜(也就是yolo)语音房功能是否全局开放(true 是).
   */
  QI_BLA_VOICE_ROOM_FULLY_OPEN,
  // =============================  系统服务内使用 =============================

  /**
   * apple支付共享密钥.
   */
  APPLE_SHARED_PASSWORD,
  /**
   * 游戏白名单
   */
  GAME_WHITE_LIST,
  /**
   * 红包飘窗是否开启
   */
  WHETHER_TO_OPEN_THE_RED_ENVELOPE_BAY_WINDOW,
  /**
   * 房间奖励显示入口的区域
   */
  ROOM_BONUS_REGION,
  /**
   * 房间奖励跳转url
   */
  ROOM_REWARD_URL,
  /**
   * H5用户审核用户违规白名单
   */
  REVIEW_USER_WHITELIST,
  /**
   * H5用户账号处理白名单
   */
  ACCOUNT_PROCESS_WHITELIST;


  public String concatSysOrigin(SysOriginPlatformEnum sysOrigin) {
    return this.name().concat("_").concat(sysOrigin.name());
  }

}
