package com.red.circle.other.inner.model.cmd.approval;

import java.io.Serializable;
import java.util.List;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 工会资料审核
 *
 * <AUTHOR> on 2021/7/29
 */
@Data
@Accessors(chain = true)
public class ApprovalFamilyCmd implements Serializable {

  private static final long serialVersionUID = 1L;
  /**
   * 工会id
   */
  private List<Long> familyIds;

  /**
   * 工会类型
   */
  private String type;

  private String approvalStatus;


}
