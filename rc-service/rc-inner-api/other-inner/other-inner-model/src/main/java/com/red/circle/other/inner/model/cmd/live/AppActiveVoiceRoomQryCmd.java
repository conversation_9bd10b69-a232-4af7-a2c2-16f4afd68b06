package com.red.circle.other.inner.model.cmd.live;

import com.red.circle.framework.core.dto.CommonCommand;
import java.io.Serial;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 活跃房间搜索条件.
 *
 * <AUTHOR> on 2022/11/16
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class AppActiveVoiceRoomQryCmd extends CommonCommand {

  @Serial
  private static final long serialVersionUID = 1L;

  /**
   * 房间id.
   */
  private Long roomId;

  /**
   * account.
   */
  private Long roomAccount;

  /**
   * 来源系统.
   */
  private String sysOrigin;

  /**
   * 区域.
   */
  private String region;

  /**
   * 排序.
   */
  private OrderByStrategy orderByStrategy;

  /**
   * 国家code.
   */
  private String countryCode;

  /**
   * 工会id.
   */
  private Long familyId;

  /**
   * 每页数量.
   */
  private Integer limit;


  public enum OrderByStrategy {

    APP_DISCOVERY

  }
}
