package com.red.circle.other.inner.model.dto.famliy;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 工会当前等级. （原：FamilyDetailsCacheCO）
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-20
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class FamilyDetailsDTO implements Serializable {

  private static final long serialVersionUID = 1L;

  /**
   * 工会Id
   */
  @JsonSerialize(using = ToStringSerializer.class)
  private Long familyId;

  /**
   * 工会名称.
   */
  private String familyName;

  /**
   * 工会账号.
   */
  @JsonSerialize(using = ToStringSerializer.class)
  private Long familyAccount;

  /**
   * 工会头像.
   */
  private String familyAvatar;

  /**
   * 工会公告.
   */
  private String familyNotice;

  /**
   * 等级Id
   */
  @JsonSerialize(using = ToStringSerializer.class)
  private Long familyLevelId;

  /**
   * 等级顺序
   */
  private Integer levelSort;

  /**
   * 等级key
   */
  private String levelKey;

  /**
   * 等级经验值
   */
  @JsonSerialize(using = ToStringSerializer.class)
  private Long levelExp;

  /**
   * 头像框Id.
   */
  @JsonSerialize(using = ToStringSerializer.class)
  private Long avatarFrameId;

  /**
   * 头像框.
   */
  private String avatarFrameCover;

  /**
   * 头像框效果.
   */
  private String avatarFrameSvg;

  /**
   * 徽章Id.
   */
  @JsonSerialize(using = ToStringSerializer.class)
  private Long badgeId;

  /**
   * 徽章.
   */
  private String badgeCover;

  /**
   * 徽章效果.
   */
  private String badgeSvg;

  /**
   * 徽章Id.
   */
  @JsonSerialize(using = ToStringSerializer.class)
  private Long giftId;

  /**
   * 礼物.
   */
  private String giftCover;

  /**
   * 礼物效果.
   */
  private String giftSvg;

  /**
   * 最大成员数.
   */
  private Integer maxMember;

  /**
   * 最大管理员数.
   */
  private Integer maxManager;

  /**
   * 等级背景图.
   */
  private String levelBackgroundPicture;

}
