package com.red.circle.other.inner.model.dto.famliy;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 工会成员表.
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-20
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class FamilyMemberInfoDTO implements Serializable {

  private static final long serialVersionUID = 1L;

  /**
   * 主键标识.
   */
  @JsonSerialize(using = ToStringSerializer.class)
  private Long id;

  /**
   * 来源系统.
   */
  private String sysOrigin;

  /**
   * 工会ID.
   */
  @JsonSerialize(using = ToStringSerializer.class)
  private Long familyId;

  /**
   * 工会成员userId.
   */
  @JsonSerialize(using = ToStringSerializer.class)
  private Long memberUserId;

  /**
   * 角色.
   */
  private String memberRole;

}
