package com.red.circle.other.inner.model.cmd.famliy;

import com.red.circle.framework.core.dto.PageCommand;
import java.io.Serial;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 工会成员
 *
 * <AUTHOR>
 * @since 2021-07-30
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class FamilyMemberQryCmd extends PageCommand {

  @Serial
  private static final long serialVersionUID = 1L;

  /**
   * 用户id
   */
  private Long userId;

  /**
   * 工会id
   */
  private Long familyId;

}
