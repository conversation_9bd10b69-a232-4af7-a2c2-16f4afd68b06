package com.red.circle.other.inner.enums.material;

import java.util.Objects;

/**
 * 徽章类型枚举.
 *
 * <AUTHOR> on 2021/5/13
 */
public enum BadgeConfigTypeEnum {

  /**
   * 成就.
   */
  ACHIEVEMENT,

  /**
   * 管理员.
   */
  ADMINISTRATOR,

  /**
   * 活动.
   */
  ACTIVITY,

  /**
   * 房间徽章.
   */
  ROOM_BADGE,

  /**
   * 工会徽章.
   */
  FAMILY;

  public boolean eq(String name) {
    return Objects.equals(this.name(), name);
  }

}
