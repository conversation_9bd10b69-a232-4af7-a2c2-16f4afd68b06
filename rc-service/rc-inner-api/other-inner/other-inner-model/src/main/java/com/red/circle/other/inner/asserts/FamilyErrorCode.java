package com.red.circle.other.inner.asserts;

import com.red.circle.framework.dto.IResponseErrorCode;
import com.red.circle.framework.dto.ResErrorCode;

/**
 * 范围：2500 ~ 2900.
 *
 * <AUTHOR> on 2023/6/6
 */
@ResErrorCode(describe = "工会相关", minCode = 2500, maxCode = 2600)
public enum FamilyErrorCode implements IResponseErrorCode {

  /**
   * 已有工会,无法新建.
   */
  FAMILY_EXISTENCE(2500, "You already have a family and cannot be created"),

  /**
   * 没有达到创建工会的条件.
   */
  NOT_QUALIFIED_CREATE_FAMILY(2501, "Does not meet the conditions for creating a family"),

  /**
   * 创始人不能退出工会.
   */
  FOUNDER_NOT_ALLOWED_EXIT_FAMILY(2502, "Family founders cannot withdraw"),

  /**
   * 没有工会信息，核对失败.
   */
  NOT_EXIST_FAMILY_INFO_DATA(2503, "No family information, verification failed"),

  /**
   * 工会满员，无法加入.
   */
  FAMILY_MEMBER_MAX(2504, "Family members have reached, unable to join new members"),

  /**
   * 已存在工会，无法加入.
   */
  THERE_ARE_FAMILIES_ERROR(2505, "Family already exists, unable to join"),

  /**
   * 工会拒绝用户加入.
   */
  FAMILY_REFUSE_JOIN(2506, "The family has rejected your application to join"),

  /**
   * 不能重复申请.
   */
  REPEAT_APPLICATION(2507, "Do not apply repeatedly, please wait patiently for the result"),

  /**
   * 单据已过期.
   */
  NOT_FOUND_REWARD(2508, "Couldn't find the family reward"),

  /**
   * 工会管理员满员，无法授权.
   */
  FAMILY_MANAGE_MAX(2509, "The administrator is full, unable to add an administrator"),

  /**
   * 族长不能被移除
   */
  ADMIN_DEL_ERROR(2510, "The patriarch cannot be removed"),

  /**
   * 规则最多三条
   */
  UP_TO_THREE_RULES(2511, "Three rules at most"),

  ;


  private final int code;
  private final String message;

  FamilyErrorCode(int code, String message) {
    this.code = code;
    this.message = message;
  }

  @Override
  public Integer getCode() {
    return this.code;
  }

  @Override
  public String getMessage() {
    return this.message;
  }

  @Override
  public String getErrorCodeName() {
    return this.name();
  }

}
