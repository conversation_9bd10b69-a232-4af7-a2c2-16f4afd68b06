package com.red.circle.other.inner.model.dto.famliy;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import java.io.Serial;
import java.io.Serializable;
import java.sql.Timestamp;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 工会等级配置表.
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-20
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class FamilyLevelConfigDTO implements Serializable {

  @Serial
  private static final long serialVersionUID = 1L;

  /**
   * 主键标识.
   */
  @JsonSerialize(using = ToStringSerializer.class)
  private Long id;

  /**
   * 来源系统.
   */
  private String sysOrigin;

  /**
   * 等级key.
   */
  private String levelKey;

  /**
   * 头像框ID.
   */
  @JsonSerialize(using = ToStringSerializer.class)
  private Long avatarFrameId;

  /**
   * 徽章ID.
   */
  @JsonSerialize(using = ToStringSerializer.class)
  private Long badgeId;

  /**
   * 礼物ID.
   */
  @JsonSerialize(using = ToStringSerializer.class)
  private Long giftId;

  /**
   * 等级经验值.
   */
  private Integer levelExp;

  /**
   * 最大成员数.
   */
  private Integer maxMember;

  /**
   * 最大管理员数.
   */
  private Integer maxManager;

  /**
   * 等级背景图.
   */
  private String levelBackgroundPicture;

  /**
   * 等级顺序.
   */
  private Integer sort;

  /**
   * 等级类型.
   */
  private String levelType;

  /**
   * 创建时间.
   */
  private Timestamp createTime;

  /**
   * 修改时间.
   */
  private Timestamp updateTime;

}
