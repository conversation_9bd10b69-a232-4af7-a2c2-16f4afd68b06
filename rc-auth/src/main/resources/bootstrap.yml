spring-profile.active: @profiles.active@

spring:
  application:
    name: @artifactId@
  cloud:
    nacos:
      username: ${NACOS_USERNAME:nacos}
      password: ${NACOS_PASSWORD:nacos}
      discovery:
        server-addr: ${NACOS_HOST:127.0.0.1}:${NACOS_PORT:8848}
        namespace: @register.namespace@
        group: ${spring-profile.active}
      config:
        server-addr: ${NACOS_HOST:127.0.0.1}:${NACOS_PORT:8848}
        file-extension: @register.file-extension@
        namespace: @register.namespace@
        group: ${spring.application.name}
        shared-configs:
          - data-id: <EMAIL>-extension@
            group: ${spring.application.name}
            refresh: true
    loadbalancer:
      cache:
        ttl: 5s
  config:
    import:
      - optional:nacos:<EMAIL>-extension@
      - optional:nacos:<EMAIL>-extension@?group=@config.group.common@
      - optional:nacos:<EMAIL>-extension@?refresh=true&group=@config.group.common@

logging:
  level:
    com:
      alibaba:
        cloud:
          nacos:
            configdata:
              NacosConfigDataLoader: error
            client:
              NacosPropertySourceBuilder: error
