server:
  port: 1000
  shutdown: graceful
  undertow:
    no-request-timeout: 10000
  compression:
    enabled: true
    mime-types:
      - text/html
      - text/xml
      - text/plain
      - application/json
      - application/xml
      - application/openmetrics-text
    min-response-size: 2KB

spring:
  lifecycle:
    timeout-per-shutdown-phase: 30s
  main:
    allow-bean-definition-overriding: true
  profiles:
    active: @profiles.active@
    group:
      local: web-framework,openfeign
      dev: web-framework,openfeign
      prod: web-framework,openfeign

management:
  endpoints:
    web:
      exposure:
        include: "*"
  endpoint:
    shutdown:
      enabled: false
    info:
      enabled: true
    health:
      show-details: always
framework:
  nacos:
    health-endpoint: /actuator/health
    health-status: UP
