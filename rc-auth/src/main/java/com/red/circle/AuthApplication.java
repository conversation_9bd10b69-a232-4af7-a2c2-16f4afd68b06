package com.red.circle;

import com.red.circle.component.redis.RedisAutoConfiguration;
import com.red.circle.framework.cloud.annotation.RedCircleCloudApplication;
import io.lettuce.core.RedisClient;
import io.lettuce.core.RedisURI;
import io.lettuce.core.api.StatefulRedisConnection;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;

/**
 * 认证服务.
 *
 * <AUTHOR> on 2023/4/28
 */
@Slf4j
@EnableDiscoveryClient
@EnableFeignClients
@SpringBootApplication(exclude = RedisAutoConfiguration.class)
public class AuthApplication {

  public static void main(String[] args) {
    log.info("Begin to start Spring Boot Application");

    long startTime = System.currentTimeMillis();
    SpringApplication.run(AuthApplication.class, args);
    long endTime = System.currentTimeMillis();
    log.info("End starting Spring Boot Application, Time used: " + (endTime - startTime));
  }



//    public static void main(String[] args) {
//      String host = "bj-crs-7zkv17oh.sql.tencentcdb.com";
//      int port = 25244;
//      String username = "qdd";  // 用户名
//      String password = "qdd12345";  // 密码
//
//      // 带用户名的连接URL格式：redis://username:password@host:port
//      RedisURI redisURI = RedisURI.create("redis://" + username + ":" + password + "@" + host + ":" + port);
//
//      RedisClient client = RedisClient.create(redisURI);
//      StatefulRedisConnection<String, String> connection = client.connect();
//
//      // 验证客户端名称
//      System.out.println("连接成功，客户端名称: " + redisURI.getClientName());
//    }
}
