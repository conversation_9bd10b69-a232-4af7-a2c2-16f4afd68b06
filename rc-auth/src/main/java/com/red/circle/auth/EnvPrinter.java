package com.red.circle.auth;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.CommandLineRunner;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class EnvPrinter implements CommandLineRunner {

    @Value("${spring.cloud.nacos.discovery.server-addr}")
    private String nacosServerAddr;

    @Value("${spring.cloud.nacos.discovery.namespace}")
    private String namespace;

@Value("${spring.cloud.nacos.discovery.group}")
    private String group;

    @Override
    public void run(String... args) {
        // 打印系统变量
        log.info("系统变量 NACOS_HOST: {}", System.getenv("NACOS_HOST"));

        // 打印解析后的Nacos配置
        log.info("Nacos服务器地址: {}", nacosServerAddr);
        log.info("Nacos服务器地址: {}", namespace);
        log.info("Nacos服务器地址: {}", group);

        // 打印所有环境变量（用于调试）
        System.getenv().forEach((k, v) -> {
            if (k.contains("NACOS") || k.contains("SERVER")) {
                log.info("环境变量 {}: {}", k, v);
            }
        });
    }
}
