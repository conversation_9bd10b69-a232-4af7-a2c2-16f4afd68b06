<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">

  <modelVersion>4.0.0</modelVersion>
  <artifactId>rc-auth</artifactId>
  <packaging>jar</packaging>
  <description>认证服务</description>

  <dependencies>

    <dependency>
      <groupId>com.red.circle</groupId>
      <artifactId>rc-tool-crypto</artifactId>
    </dependency>

    <dependency>
      <groupId>com.red.circle</groupId>
      <artifactId>component-redis</artifactId>
    </dependency>

<!--    <dependency>-->
<!--      <groupId>com.red.circle</groupId>-->
<!--      <artifactId>framework-cloud</artifactId>-->
<!--    </dependency>-->

<!--    <dependency>-->
<!--      <groupId>com.red.circle</groupId>-->
<!--      <artifactId>framework-cloud</artifactId>-->
<!--      &lt;!&ndash; 排除 Nacos 相关传递依赖 &ndash;&gt;-->
<!--      <exclusions>-->
<!--        <exclusion>-->
<!--          <groupId>com.alibaba.cloud</groupId>-->
<!--          <artifactId>spring-cloud-starter-alibaba-nacos-discovery</artifactId>-->
<!--        </exclusion>-->
<!--        <exclusion>-->
<!--          <groupId>com.alibaba.cloud</groupId>-->
<!--          <artifactId>spring-cloud-starter-alibaba-nacos-config</artifactId>-->
<!--        </exclusion>-->
<!--      </exclusions>-->
<!--    </dependency>-->

    <!-- 单独引入 Nacos 相关依赖 -->
    <dependency>
      <groupId>com.alibaba.cloud</groupId>
      <artifactId>spring-cloud-starter-alibaba-nacos-discovery</artifactId>
    </dependency>
    <dependency>
      <groupId>com.alibaba.cloud</groupId>
      <artifactId>spring-cloud-starter-alibaba-nacos-config</artifactId>
    </dependency>

    <dependency>
      <groupId>com.red.circle</groupId>
      <artifactId>other-inner-api</artifactId>
    </dependency>

    <dependency>
      <groupId>com.red.circle</groupId>
      <artifactId>auth-inner-api</artifactId>
    </dependency>

    <dependency>
      <groupId>com.tencentcloudapi.cls</groupId>
      <artifactId>tencentcloud-cls-logback-appender</artifactId>
      <version>${tencentcloud-cls-logback-appender.version}</version>
    </dependency>

    <dependency>
      <groupId>com.jthinking.common</groupId>
      <artifactId>ip-info</artifactId>
      <version>${ip.info.version}</version>
    </dependency>

<!--    <dependency>-->
<!--      <groupId>com.maxmind.geoip2</groupId>-->
<!--      <artifactId>geoip2</artifactId>-->
<!--      <version>2.15.0</version>-->
<!--    </dependency>-->

  </dependencies>

  <build>
    <resources>
      <resource>
        <directory>src/main/resources</directory>
        <filtering>true</filtering>
        <!-- 排除二进制文件的过滤 -->
        <excludes>
          <exclude>static/GeoLite2-City.mmdb</exclude>
          <!-- 可添加其他二进制文件，如 *.jpg, *.png 等 -->
        </excludes>
      </resource>
      <resource>
        <directory>src/main/resources</directory>
        <filtering>false</filtering>
        <includes>
          <include>static/GeoLite2-City.mmdb</include>
        </includes>
      </resource>
    </resources>
    <finalName>${project.name}-${revision}</finalName>
    <plugins>
      <plugin>
        <artifactId>spring-boot-maven-plugin</artifactId>
        <groupId>org.springframework.boot</groupId>
        <!--        <configuration>-->
        <!--          <fork>true</fork>-->
        <!--          <classifier>exec</classifier>-->
        <!--        </configuration>-->
        <executions>
          <execution>
            <goals>
              <goal>repackage</goal>
            </goals>
          </execution>
        </executions>
        <configuration>
          <excludes>
            <exclude>
              <groupId>org.projectlombok</groupId>
              <artifactId>lombok</artifactId>
            </exclude>
          </excludes>
        </configuration>
      </plugin>
    </plugins>
  </build>

  <properties>
    <maven.deploy.skip>true</maven.deploy.skip>
  </properties>

  <parent>
    <artifactId>rc-dependencies</artifactId>
    <groupId>com.red.circle</groupId>
    <version>${revision}</version>
    <relativePath>./../rc-dependencies/pom.xml</relativePath>
  </parent>

</project>
