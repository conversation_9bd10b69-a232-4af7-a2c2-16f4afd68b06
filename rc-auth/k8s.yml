---
apiVersion: apps/v1
kind: Deployment
metadata:
  annotations: {}
  labels:
    app: auth
  name: auth
  namespace: local
  resourceVersion: '83780018'
spec:
  progressDeadlineSeconds: 600
  replicas: 1
  revisionHistoryLimit: 2
  selector:
    matchLabels:
      app: auth
  strategy:
    rollingUpdate:
      maxSurge: 25%
      maxUnavailable: 25%
    type: RollingUpdate
  template:
    metadata:
      annotations:
        kubectl.kubernetes.io/restartedAt: '2025-05-22T10:43:01+08:00'
      creationTimestamp: null
      labels:
        app: auth
    spec:
      containers:
        - env:
            - name: JVM_XMS
              value: 520m
            - name: JVM_XMX
              value: 520m
            - name: JVM_XMN
              value: 384m
            - name: SERVER_PORT
              value: '1000'
            - name: SERVER_PROFILE_ACTIVE
              value: dev
            - name: TRACE_PROTOCOL
              value: grpc
            - name: TRACE_ENDPOINT
              value: 'https://asxxx-dev.ap-southeast-1.log.aliyuncs.com:10010'
            - name: TRACE_COMPRESSION
              value: gzip
            - name: TRACE_HEADERS
              value: >-
                x-sls-otel-project=aswat-app-trace-dev,x-sls-otel-instance-id=aswat-develop,x-sls-otel-ak-id=LTAI5t9ohxxxffeKrE5x,x-sls-otel-ak-secret=3kKQWtxxxxxxtqXUDSTPm6SzmHz
            - name: TRACE_HOST_NAME
              valueFrom:
                fieldRef:
                  apiVersion: v1
                  fieldPath: status.podIP
            - name: NACOS_HOST
              valueFrom:
                configMapKeyRef:
                  key: nacos.host
                  name: nacos
            - name: NACOS_PORT
              valueFrom:
                configMapKeyRef:
                  key: nacos.port
                  name: nacos
            - name: NACOS_USERNAME
              valueFrom:
                configMapKeyRef:
                  key: nacos.username
                  name: nacos
            - name: NACOS_PASSWORD
              valueFrom:
                configMapKeyRef:
                  key: nacos.password
                  name: nacos
          image: >-
            794038239327.dkr.ecr.ap-southeast-1.amazonaws.com/halar-dev:auth-20250414v100417
          imagePullPolicy: IfNotPresent
          lifecycle:
            preStop:
              exec:
                command:
                  - /bin/bash
                  - '-c'
                  - >-
                    curl -X POST
                    http://localhost:1000/actuator/nacosservicederegister?key=CxILm9hA1b9hF3Hl
                    --header 'Content-Type:
                    application/vnd.spring-boot.actuator.v2+json;charset=UTF-8'
                  - sleep 30
          livenessProbe:
            failureThreshold: 2
            httpGet:
              path: /actuator/health
              port: 1000
              scheme: HTTP
            periodSeconds: 60
            successThreshold: 1
            timeoutSeconds: 20
          name: auth
          ports:
            - containerPort: 1000
              protocol: TCP
          readinessProbe:
            failureThreshold: 2
            httpGet:
              path: /actuator/health
              port: 1000
              scheme: HTTP
            periodSeconds: 5
            successThreshold: 1
            timeoutSeconds: 10
          resources:
            limits:
              memory: 1Gi
            requests:
              memory: 300Mi
          startupProbe:
            failureThreshold: 60
            httpGet:
              path: /actuator/health
              port: 1000
              scheme: HTTP
            periodSeconds: 10
            successThreshold: 1
            timeoutSeconds: 5
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
      dnsPolicy: ClusterFirst
      imagePullSecrets:
        - name: aliyun
      restartPolicy: Always
      schedulerName: default-scheduler
      securityContext: {}
      terminationGracePeriodSeconds: 120
status:
  availableReplicas: 1
  conditions:
    - lastTransitionTime: '2025-05-10T07:36:30Z'
      lastUpdateTime: '2025-05-10T07:36:30Z'
      message: Deployment has minimum availability.
      reason: MinimumReplicasAvailable
      status: 'True'
      type: Available
    - lastTransitionTime: '2025-01-16T15:12:11Z'
      lastUpdateTime: '2025-05-22T02:45:08Z'
      message: ReplicaSet "auth-dbf5b86df" has successfully progressed.
      reason: NewReplicaSetAvailable
      status: 'True'
      type: Progressing
  observedGeneration: 59
  readyReplicas: 1
  replicas: 1
  updatedReplicas: 1

---
apiVersion: v1
kind: Service
metadata:
  annotations: {}
  name: auth
  namespace: local
  resourceVersion: '22843125'
spec:
  clusterIP: *************
  clusterIPs:
    - *************
  internalTrafficPolicy: Cluster
  ipFamilies:
    - IPv4
  ipFamilyPolicy: SingleStack
  ports:
    - port: 80
      protocol: TCP
      targetPort: 1000
  selector:
    app: auth
  sessionAffinity: None
  type: ClusterIP
status:
  loadBalancer: {}

