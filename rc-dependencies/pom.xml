<project
  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xmlns="http://maven.apache.org/POM/4.0.0"
  xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
  <modelVersion>4.0.0</modelVersion>
  <artifactId>rc-dependencies</artifactId>
  <groupId>com.red.circle</groupId>
  <packaging>pom</packaging>
  <version>${revision}</version>

  <profiles>
    <profile>
      <id>local</id>
      <properties>
        <revision>1.0.0-local</revision>
        <profiles.active>local</profiles.active>
        <register.namespace>69450bca-ce4a-4e67-8bb8-b21bcf55d0fd</register.namespace>
      </properties>
      <activation>
        <activeByDefault>true</activeByDefault>
      </activation>
    </profile>
    <profile>
      <id>dev</id>
      <properties>
        <revision>1.0.0-dev</revision>
        <profiles.active>dev</profiles.active>
        <register.namespace>RedCircleServiceDev</register.namespace>
      </properties>
    </profile>
    <profile>
      <id>test</id>
      <properties>
        <revision>1.0.2-test</revision>
        <profiles.active>test</profiles.active>
        <register.namespace>RedCircleServiceTest</register.namespace>
      </properties>
    </profile>
    <profile>
      <id>prod</id>
      <properties>
        <revision>1.4.70-RELEASE</revision>
        <profiles.active>prod</profiles.active>
        <register.namespace>RedCircleServiceProd</register.namespace>
        <!--        <app-name>rc-app-server-${revision}-${maven.build.timestamp}</app-name>-->
      </properties>
    </profile>
  </profiles>

  <build>
    <finalName>${project.name}</finalName>
    <resources>
      <resource>
        <directory>src/main/resources</directory>
        <filtering>true</filtering>
      </resource>
    </resources>
    <testResources>
      <testResource>
        <directory>src/test/resources</directory>
      </testResource>
      <testResource>
        <directory>src/main/resources</directory>
      </testResource>
    </testResources>

    <plugins>
      <plugin>
        <groupId>org.codehaus.mojo</groupId>
        <artifactId>flatten-maven-plugin</artifactId>
        <version>1.1.0</version>
        <configuration>
          <updatePomFile>true</updatePomFile>
          <flattenMode>resolveCiFriendliesOnly</flattenMode>
        </configuration>
        <executions>
          <execution>
            <id>flatten</id>
            <phase>process-resources</phase>
            <goals>
              <goal>flatten</goal>
            </goals>
          </execution>
          <execution>
            <id>flatten.clean</id>
            <phase>clean</phase>
            <goals>
              <goal>clean</goal>
            </goals>
          </execution>
        </executions>
      </plugin>

      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-compiler-plugin</artifactId>
        <version>${maven-compiler-plugin.version}</version>
        <configuration>
          <encoding>${project.reporting.outputEncoding}</encoding>
          <source>${maven.compiler.source}</source>
          <target>${maven.compiler.target}</target>
          <debug>false</debug>
          <compilerArgs>
            <arg>-parameters</arg>
          </compilerArgs>
        </configuration>
      </plugin>
    </plugins>
  </build>

  <dependencies>
    <dependency>
      <artifactId>lombok</artifactId>
      <groupId>org.projectlombok</groupId>
    </dependency>

    <dependency>
      <artifactId>mapstruct</artifactId>
      <groupId>org.mapstruct</groupId>
    </dependency>

    <dependency>
      <artifactId>mapstruct-processor</artifactId>
      <groupId>org.mapstruct</groupId>
    </dependency>

    <dependency>
      <groupId>org.projectlombok</groupId>
      <artifactId>lombok-mapstruct-binding</artifactId>
    </dependency>

    <dependency>
      <artifactId>junit</artifactId>
      <groupId>junit</groupId>
      <scope>test</scope>
    </dependency>

    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-actuator</artifactId>
    </dependency>

  </dependencies>

  <dependencyManagement>
    <dependencies>

      <dependency>
        <artifactId>rc-dependencies-framework</artifactId>
        <groupId>com.red.circle</groupId>
        <scope>import</scope>
        <type>pom</type>
        <version>${rc-dependencies-framework.version}</version>
      </dependency>

      <dependency>
        <artifactId>common-business-core</artifactId>
        <groupId>com.red.circle</groupId>
        <version>${project.version}</version>
      </dependency>

      <dependency>
        <groupId>com.red.circle</groupId>
        <artifactId>common-business-dto</artifactId>
        <version>${project.version}</version>
      </dependency>

      <dependency>
        <groupId>com.red.circle</groupId>
        <artifactId>common-business-mq</artifactId>
        <version>${project.version}</version>
      </dependency>

      <dependency>
        <groupId>com.red.circle</groupId>
        <artifactId>business-rocketmq</artifactId>
        <version>${project.version}</version>
      </dependency>

      <dependency>
        <groupId>com.red.circle</groupId>
        <artifactId>business-mq-model</artifactId>
        <version>${project.version}</version>
      </dependency>


      <!-- 内部服务，api -->
      <dependency>
        <groupId>com.red.circle</groupId>
        <artifactId>console-inner-api</artifactId>
        <version>${project.version}</version>
      </dependency>

      <dependency>
        <groupId>com.red.circle</groupId>
        <artifactId>other-inner-api</artifactId>
        <version>${project.version}</version>
      </dependency>

      <dependency>
        <groupId>com.red.circle</groupId>
        <artifactId>other-inner-model</artifactId>
        <version>${project.version}</version>
      </dependency>

      <dependency>
        <groupId>com.red.circle</groupId>
        <artifactId>order-inner-api</artifactId>
        <version>${project.version}</version>
      </dependency>

      <dependency>
        <groupId>com.red.circle</groupId>
        <artifactId>order-inner-model</artifactId>
        <version>${project.version}</version>
      </dependency>

      <dependency>
        <groupId>com.red.circle</groupId>
        <artifactId>wallet-inner-api</artifactId>
        <version>${project.version}</version>
      </dependency>

      <dependency>
        <groupId>com.red.circle</groupId>
        <artifactId>wallet-inner-model</artifactId>
        <version>${project.version}</version>
      </dependency>

      <dependency>
        <groupId>com.red.circle</groupId>
        <artifactId>auth-inner-api</artifactId>
        <version>${project.version}</version>
      </dependency>

      <dependency>
        <groupId>com.red.circle</groupId>
        <artifactId>external-inner-api</artifactId>
        <version>${project.version}</version>
      </dependency>

      <dependency>
        <groupId>com.red.circle</groupId>
        <artifactId>external-inner-model</artifactId>
        <version>${project.version}</version>
      </dependency>

      <dependency>
        <groupId>com.red.circle</groupId>
        <artifactId>live-inner-api</artifactId>
        <version>${project.version}</version>
      </dependency>

      <dependency>
        <groupId>com.red.circle</groupId>
        <artifactId>live-inner-model</artifactId>
        <version>${project.version}</version>
      </dependency>

      <dependency>
        <groupId>com.red.circle</groupId>
        <artifactId>game-fruit-inner-api</artifactId>
        <version>${project.version}</version>
      </dependency>

      <dependency>
        <groupId>com.alibaba.fastjson2</groupId>
        <artifactId>fastjson2</artifactId>
        <version>${fastjson2.version}</version>
      </dependency>

    </dependencies>
  </dependencyManagement>

  <properties>
    <revision>1.0.0</revision>
    <register.file-extension>yml</register.file-extension>
    <config.group.common>common</config.group.common>
    <rc-dependencies-framework.version>1.5.12-RELEASES</rc-dependencies-framework.version>
    <spring-boot-admin.version>3.1.8</spring-boot-admin.version>

    <java.version>17</java.version>
    <maven.compiler.source>17</maven.compiler.source>
    <maven.compiler.target>17</maven.compiler.target>
    <maven.build.timestamp.format>HHmmss</maven.build.timestamp.format>
    <maven-compiler-plugin.version>3.8.0</maven-compiler-plugin.version>
    <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
    <project.build.sourceEncoding>UTF8</project.build.sourceEncoding>

    <tencentcloud-cls-logback-appender.version>1.0.4</tencentcloud-cls-logback-appender.version>
    <tencentcloud.version>3.1.877</tencentcloud.version>
    <opentelemetry-logback.version>1.9.2-alpha</opentelemetry-logback.version>
    <protobuf-java.version>3.5.1</protobuf-java.version>
    <easyexcel.version>3.1.3</easyexcel.version>
    <agora.version>2.0.0</agora.version>
    <ip.info.version>2.1.7</ip.info.version>
    <shardingsphere-jdbc-core.version>5.3.2</shardingsphere-jdbc-core.version>
    <fastjson2.version>2.0.53</fastjson2.version>
    <java-telegram-bot-api.version>7.11.0</java-telegram-bot-api.version>
  </properties>


</project>
